import { Geo } from '@vercel/functions'
import { addHours, differenceInDays, subHours } from 'date-fns'

import prisma from '@/db/prisma'
import { getEstateById } from '@/server/model/BrokerEstate/factory'

import { oldOARevisitTemplate } from '../lib/email-template/old-oa-revisit'

import { safeGetContactName } from './get-contact-name'
import { logListingAgreementInteraction } from './log-listing-agreement-interaction'
import { sendMail } from './mail/sendMail'

const REVISIT_THRESHOLD_DAYS = 14

interface ViewTokenExtraData {
  source?: string | null
  ip_address?: string
  geolocation?: Geo
  // Pathname of the page where the token was used (e.g. '/oppdragsavtale')
  path?: string
  userAgent?: {
    browser: {
      name?: string
      version?: string
    }
    device: {
      model?: string
      type?: string
      vendor?: string
    }
  }

  [key: string]: unknown
}

type ViewTokenParams = {
  token: string
  cid?: string | null
  ecid?: string | null
} & ViewTokenExtraData

async function logTokenRelatedInteraction(
  tokenEntry: NonNullable<
    Awaited<ReturnType<typeof prisma.offer_access_tokens.findUnique>>
  >,
  contactId: string | null,
  extra_data: ViewTokenExtraData,
) {
  if (tokenEntry.listing_agreement_id) {
    try {
      await logListingAgreementInteraction({
        listing_agreements_id: tokenEntry.listing_agreement_id,
        event_type: 'viewed',
        seller_id: contactId,
        extra_data,
      })
    } catch (error) {
      console.warn('Failed to log listing agreement interaction:', error)
    }
    return
  }

  // Or via inspection folder -> listing agreement
  if (tokenEntry.inspection_folder_id) {
    try {
      const inspection = await prisma.inspection_folders.findUnique({
        where: { id: tokenEntry.inspection_folder_id },
        select: {
          listing_agreement: { select: { id: true } },
          listing_agreement_active: true,
        },
      })

      const listingAgreementId = inspection?.listing_agreement?.id
      if (!listingAgreementId) return

      try {
        await logListingAgreementInteraction({
          listing_agreements_id: listingAgreementId,
          event_type: 'viewed',
          seller_id: contactId,
          extra_data: {
            ...extra_data,
            listing_agreement_active: inspection?.listing_agreement_active,
          },
        })
      } catch (error) {
        console.warn('Failed to log inspection folder interaction:', error)
      }
    } catch (error) {
      console.warn('Failed to fetch inspection folder for logging:', error)
    }
  }
}

async function maybeSendRevisitNotification(
  tokenEntry: NonNullable<
    Awaited<ReturnType<typeof prisma.offer_access_tokens.findUnique>>
  >,
  contactId: string,
) {
  if (!tokenEntry.estate_id) return

  try {
    const [agreement, lastVisit] = await Promise.all([
      prisma.listing_agreements.findUnique({
        where: { id: tokenEntry.estate_id, is_valuation: false },
      }),
      // Find most recent previous visit by this contact for this estate
      prisma.page_visit.findFirst({
        where: {
          estate_id: tokenEntry.estate_id,
          contact_id: contactId,
        },
        orderBy: { last_heartbeat: 'desc' },
      }),
    ])

    if (!agreement) return

    if (agreement.signing_finished_at) {
      // OA is signed, we don't send a revisit notification
      return
    }

    if (!lastVisit) return // first ever visit – not a revisit

    const daysSinceLastVisit = differenceInDays(
      new Date(),
      lastVisit.last_heartbeat,
    )
    if (daysSinceLastVisit < REVISIT_THRESHOLD_DAYS) return

    // Suppress duplicate revisit notifications within the last 24 hours for the same estate
    const duplicateRecent = await prisma.email_audit.findFirst({
      where: {
        context_id: tokenEntry.estate_id,
        created_at: { gte: subHours(new Date(), 24) },
      },
      select: { id: true },
    })

    if (duplicateRecent) return

    // Load estate & broker
    const estate = await getEstateById({ id: tokenEntry.estate_id })
    if (!estate) return
    const broker = estate.broker
    if (!broker?.email) return

    const contactName = await safeGetContactName(contactId)
    const address = estate.address?.streetAddress || estate.estateId
    const subject = `Selger har besøkt oppdragsavtalen igjen – ${address}`

    const body = oldOARevisitTemplate({
      brokerFirstName: broker.name?.split(' ')[0],
      contactName,
      address,
      daysSinceLastVisit,
      estateId: estate.estateId,
    })

    await sendMail({
      title: '',
      subject,
      body,
      from: {
        email: process.env.NO_REPLY_EMAIL ?? '<EMAIL>',
        name: 'Nordvik',
      },
      emails: [
        {
          email: broker.email,
          name: broker.name,
        },
      ],
      initialReceiver: { email: broker.email, name: broker.name },
      context: { type: 'old_oa_revisit', id: tokenEntry.estate_id },
      sendAt: addHours(new Date(), 1).toISOString(),
    })
  } catch (e) {
    console.error('Failed 2-week revisit notification logic', e)
  }
}

export async function viewTokenUsed({
  token,
  cid,
  ecid,
  ...extra_data
}: ViewTokenParams) {
  const tokenEntry = await prisma.offer_access_tokens.findUnique({
    where: { token },
  })
  // Nothing else we can do without a valid token.
  if (!tokenEntry) return

  const contactId = cid || ecid || null

  // Persist audit (fire & forget semantics fine here)
  try {
    await Promise.all([
      logTokenRelatedInteraction(tokenEntry, contactId, extra_data),
      prisma.access_token_audit.create({
        data: {
          token,
          contact_id: cid,
          extra_contact_id: ecid,
          source: extra_data.source,
        },
      }),
    ])
  } catch (e) {
    console.warn('Failed to create access_token_audit entry', e)
  }

  if (contactId) {
    await maybeSendRevisitNotification(tokenEntry, contactId)
  }
}
