import Estate from '@/server/model/BrokerEstate/model'

import { sendMail } from './mail/sendMail'

export async function notifyBrokerOfLead(estate: Estate) {
  try {
    const broker = await estate.broker

    if (!broker) {
      console.warn(
        `No broker found for estate ${estate.estateId}, skipping notification`,
      )
      return
    }

    await sendMail({
      title: '',
      emails: [
        {
          email: broker.email,
          name: broker.name,
        },
      ],
      subject: `Lead er sendt til Storebrand for ${estate.address.streetAddress}`,
      body: messageToBroker(estate),
      from: {
        email: process.env.NO_REPLY_EMAIL ?? '<EMAIL>',
        name: '<PERSON><PERSON>',
      },
      initialReceiver: { email: broker.email, name: broker.name },
      context: {
        type: 'lead_notification',
        id: estate.id ?? undefined,
      },
    })
  } catch (error) {
    console.error(
      `Failed to send lead notification email to broker for estate ${estate.id}:`,
      error,
    )
  }
}

function messageToBroker(estate: Estate) {
  const tail = estate.isValuation ? ' via verdivurderingen' : ''
  return `
<p>Hei, ${estate.broker?.name.split(' ')[0]}</p>
<p>Eieren av ${estate.address.streetAddress} har bedt om å bli kontaktet av Storebrand${tail}.</p>
<p>Hvis du har lagt inn din Storebrand-kontakt under Din profil i Nordvik Megler så sendes leadet direkte til kontakten. Ellers følg opp med din kontakt hos Storebrand ved behov.</p>
  `
}
