import { Decimal } from '@nordvik/database'

export type ReplaceDecimalWithNumber<T> = {
  [K in keyof T]: T[K] extends Decimal | null ? number | null : T[K]
}

export type ReplaceBigIntWithNumber<T> = {
  [K in keyof T]: T[K] extends bigint | null ? number | null : T[K]
}

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

export type AllOptional<T> = {
  [K in keyof T]?: T[K]
}

export type SanitizedPrismaObject<T> = {
  [K in keyof T]: T[K] extends Decimal
    ? number
    : T[K] extends Decimal | null
      ? number | null
      : T[K] extends Date
        ? string
        : T[K] extends Date | null
          ? string | null
          : T[K] extends bigint
            ? number
            : T[K] extends bigint | null
              ? number | null
              : T[K]
}

export function sanitizePrismaObject<T>(obj: T): SanitizedPrismaObject<T> {
  if (obj instanceof Decimal) return obj.toNumber() as SanitizedPrismaObject<T>
  if (typeof obj === 'bigint') return Number(obj) as SanitizedPrismaObject<T>
  if (obj instanceof Date) return obj.toISOString() as SanitizedPrismaObject<T>

  if (Array.isArray(obj)) {
    return obj.map(sanitizePrismaObject) as SanitizedPrismaObject<T>
  }

  if (obj && typeof obj === 'object') {
    const newObj: Record<string, unknown> = {}
    for (const key in obj) {
      const value = (obj as Record<string, unknown>)[key]
      newObj[key] = sanitizePrismaObject(value)
    }
    return newObj as SanitizedPrismaObject<T>
  }

  return obj as SanitizedPrismaObject<T>
}
