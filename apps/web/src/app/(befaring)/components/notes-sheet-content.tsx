import { useQueryClient } from '@tanstack/react-query'
import { Op } from 'quill'
import React from 'react'

import * as Sheet from '@nordvik/ui/sheet'
import { useToast } from '@nordvik/ui/toaster'

import { useNotesQuery, useUpdateNotesMutation } from '@/api/generated-client'
import { QUILL_FORMATS } from '@/components/rich-text-editor/config'
import { RichTextEditor } from '@/components/rich-text-editor/rich-text-editor'
import { useQuill } from '@/components/rich-text-editor/use-quill.hook'
import { convertDeltaToHtml } from '@/components/rich-text-editor/utils'
import { useThrottle } from '@/hooks/use-throttle'
import { useTrackEvent } from '@/lib/analytics/track-event'

export const NotesSheetContent = function NotesSheetContent({
  ref,
  notes: initial = '',
  estateId,
}: {
  notes?: string
  estateId: string
  ref?: React.RefObject<HTMLDivElement>
}) {
  const { Quill } = useQuill()
  const [value, setValue] = React.useState<{ text: string } | null>({
    text: initial,
  })

  const { toast } = useToast()
  const trackEvent = useTrackEvent()

  const queryClient = useQueryClient()

  const { mutate } = useUpdateNotesMutation({
    onMutate(variables) {
      const key = useNotesQuery.getKey({ estateId: variables.estateId })
      const previousNote = queryClient.getQueryData(key)
      queryClient.setQueryData(key, {
        notes: variables.notes,
      })

      return { previousNote }
    },
    onSettled() {
      queryClient.invalidateQueries({
        queryKey: useNotesQuery.getKey({ estateId }),
      })
      trackEvent('notes_updated', { estateId })
    },
    onError: (error, variables, ctx) => {
      if (ctx) {
        queryClient.setQueryData(
          useNotesQuery.getKey({ estateId: variables.estateId }),
          ctx.previousNote,
        )
      }
      console.error('Error updating notes', error)
      toast({
        variant: 'destructive',
        title: 'Kunne ikke lagre notater',
      })
    },
  })

  React.useEffect(() => {
    const editor = document.querySelector('.ql-editor')
    if (editor && editor instanceof HTMLElement) {
      editor.focus()
    }
  }, [])

  const saveNotes = useThrottle(
    (ops: Op[]) => {
      try {
        mutate({
          estateId,
          notes: convertDeltaToHtml(ops, Quill),
        })
      } catch (error) {
        console.error('Error updating notes', error)
      }
    },
    2000,
    {
      trailing: true,
    },
  )
  return (
    <Sheet.SheetContent
      ref={ref}
      className="w-[40rem] max-w-full px-10 flex flex-col gap-3"
    >
      <Sheet.SheetTitle className="sr-only">Notater</Sheet.SheetTitle>
      <Sheet.SheetDescription className="sr-only">
        Notater for oppdraget er ikke synlig for kunder.
      </Sheet.SheetDescription>
      <h2 className="typo-display-sm">Notater</h2>
      <RichTextEditor
        className="w-full grow flex flex-col bg-root [&_.quill]:grow [&_.ql-editor]:p-0 [&_.ql-blank.ql-editor::before]:left-0"
        value={value}
        onChange={(value) => {
          if (value.ops) {
            setValue({ text: convertDeltaToHtml(value.ops, Quill) })
            saveNotes(value.ops)
          }
        }}
        formats={QUILL_FORMATS}
        placeholder="Notater er kun synlig for meglerne på oppdraget og ikke for kunden."
      />
    </Sheet.SheetContent>
  )
}
