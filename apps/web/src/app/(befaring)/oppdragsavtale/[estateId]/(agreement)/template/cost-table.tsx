import { cn } from '@nordvik/theme/cn'

import { formatCurrency } from '@/lib/formatCurrency'

interface CostTableRow {
  description: string
  cost: number
  bold?: boolean
}

function TableHeader({ headers }: { headers: string[] }) {
  return (
    <thead>
      <tr className="text-left">
        {headers.map((header) => (
          <th key={header} className="border-b px-4 py-1 text-left">
            {header}
          </th>
        ))}
      </tr>
    </thead>
  )
}

function TableRow({ data }: { data: CostTableRow }) {
  return (
    <tr>
      <td
        width="80%"
        className={cn('px-4', { 'font-medium border-y': data.bold })}
      >
        {data.description}
      </td>
      <td
        width="20%"
        className={cn('px-4', { 'font-medium border-y': data.bold })}
      >
        <div className="flex justify-between">
          <span>kr.</span>
          <span>{formatCurrency(data.cost, { currency: '' })}</span>
        </div>
      </td>
    </tr>
  )
}

function CostTable({ title, rows }: { title: string; rows: CostTableRow[] }) {
  const headers = [title, '']

  return (
    <table className="mb-6 w-full border-collapse">
      <TableHeader headers={headers} />
      <tbody>
        {rows.map((row) => (
          <TableRow key={row.description} data={row} />
        ))}
      </tbody>
    </table>
  )
}

export default CostTable
