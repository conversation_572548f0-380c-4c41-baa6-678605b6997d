import { cn } from '@nordvik/theme/cn'

import Checkbox from './checkbox'

export default function CheckboxSection({
  description,
  horizontal,
  title,
  checkboxes,
  className,
}: {
  horizontal?: boolean
  title?: string
  checkboxes: {
    label: string | React.ReactNode
    checked?: boolean
    indented?: boolean
  }[]
  description?: string
  className?: string
}) {
  return (
    <section className={cn('mt-6 py-4', className)}>
      {title ? <p className="font-medium">{title}</p> : null}
      <div className={cn({ 'flex gap-4': horizontal })}>
        {checkboxes.map(({ checked, label, indented }) => {
          const id = label?.toString().toLowerCase().replace(' ', '-')
          return (
            <label
              key={label?.toString()}
              htmlFor={id}
              className={cn(
                'mt-2 flex items-start space-x-2',
                indented && 'ml-4',
              )}
            >
              <Checkbox checked={checked} className="mt-1" />
              {typeof label === 'string' ? <span>{label}</span> : label}
            </label>
          )
        })}
      </div>

      {description ? <p className="mt-4 text-sm">{description}</p> : null}
    </section>
  )
}
