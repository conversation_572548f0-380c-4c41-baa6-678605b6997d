import { cache } from 'react'

import prisma from '@/db/prisma'
import { sanitizePrismaObject } from '@/utils/typeHelpers'

export const cachedListingAgreement = cache(async (estateId: string) => {
  const agreement = await prisma.listing_agreements.findUnique({
    where: { estate_id: estateId },
  })
  return sanitizePrismaObject(agreement)
})

export type CachedListingAgreement = NonNullable<
  Awaited<ReturnType<typeof cachedListingAgreement>>
>
