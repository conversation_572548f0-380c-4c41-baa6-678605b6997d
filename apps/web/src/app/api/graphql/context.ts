import { getCurrentUser } from '@/lib/session'
import { Context } from '@/server/context'

import { createEmployeeLoader } from './loaders/employee-loader'
import { createEstateLoader } from './loaders/estate-loader'
import { createEtakstCheckLoader } from './loaders/etakst-check-leader'
import { createInspectionEventLoader } from './loaders/inspection-event-loader'
import { createInspectionFolderLoader } from './loaders/inspection-folder-loader'
import { createListingAgreementLoader } from './loaders/listing-agreement-loader'

export async function createContext(): Promise<Context> {
  return {
    user: await getCurrentUser(),
    loaders: {
      listingAgreementLoader: createListingAgreementLoader(this),
      inspectionFolderLoader: createInspectionFolderLoader(this),
      etakstCheckLoader: createEtakstCheckLoader(),
      estateLoader: createEstateLoader(),
      employeeLoader: createEmployeeLoader(),
      inspectionEventLoader: createInspectionEventLoader(),
    },
  }
}
