import DataLoader from 'dataloader'

import prisma from '@/db/prisma'

export function createInspectionEventLoader() {
  return new DataLoader(async (estateIds: readonly string[]) => {
    const events = await prisma.inspection_event.findMany({
      where: {
        estate_id: {
          in: estateIds as string[],
        },
      },
    })

    return estateIds.map((entry) => events.find((a) => a.estate_id === entry))
  })
}
