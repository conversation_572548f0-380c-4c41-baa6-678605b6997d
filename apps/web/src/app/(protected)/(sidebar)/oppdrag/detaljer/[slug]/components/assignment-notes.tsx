'use client'

import { useQueryClient } from '@tanstack/react-query'
import { Op } from 'quill'
import React from 'react'

import { useToast } from '@nordvik/ui/toaster'

import { useNotesQuery, useUpdateNotesMutation } from '@/api/generated-client'
import { QUILL_FORMATS } from '@/components/rich-text-editor/config'
import { RichTextEditor } from '@/components/rich-text-editor/rich-text-editor'
import { useQuill } from '@/components/rich-text-editor/use-quill.hook'
import { convertDeltaToHtml } from '@/components/rich-text-editor/utils'
import { useThrottle } from '@/hooks/use-throttle'
import { useTrackEvent } from '@/lib/analytics/track-event'

import { SaveIndicator } from '../../../status/[status]/components/estate-list/estate-card-footer/estate-card-notes'

export function AssignmentNotes({ estateId }: { estateId: string }) {
  const { Quill } = useQuill()
  const { data: notesData, isLoading } = useNotesQuery({ estateId })
  const [value, setValue] = React.useState<{ text: string } | null>(null)
  const [isSaving, setIsSaving] = React.useState(false)
  const [isTyping, setIsTyping] = React.useState(false)

  const { toast } = useToast()
  const trackEvent = useTrackEvent()
  const queryClient = useQueryClient()

  // Update local state when data is fetched
  React.useEffect(() => {
    if (notesData?.inspectionFolder?.notes !== undefined) {
      setValue({ text: notesData.inspectionFolder.notes })
    }
  }, [notesData?.inspectionFolder?.notes])

  const { mutate } = useUpdateNotesMutation({
    onMutate(variables) {
      const key = useNotesQuery.getKey({ estateId: variables.estateId })
      const previousNote = queryClient.getQueryData(key)
      queryClient.setQueryData(key, {
        inspectionFolder: {
          id: estateId,
          notes: variables.notes,
        },
      })

      return { previousNote }
    },
    onSettled() {
      queryClient.invalidateQueries({
        queryKey: useNotesQuery.getKey({ estateId }),
      })
      trackEvent('notes_updated', { estateId })
      setIsSaving(false)
    },
    onError: (error, variables, ctx) => {
      if (ctx) {
        queryClient.setQueryData(
          useNotesQuery.getKey({ estateId: variables.estateId }),
          ctx.previousNote,
        )
        setIsSaving(false)
      }
      console.error('Error updating notes', error)
      toast({
        variant: 'destructive',
        title: 'Kunne ikke lagre notater',
      })
    },
  })

  const saveNotes = useThrottle(
    (ops: Op[]) => {
      setIsTyping(false) // User stopped typing
      setIsSaving(true) // Now show loading
      try {
        mutate({
          estateId,
          notes: convertDeltaToHtml(ops, Quill),
        })
      } catch (error) {
        console.error('Error updating notes', error)
      }
    },
    2000,
    {
      trailing: true,
    },
  )

  if (isLoading) {
    return (
      <div className="flex flex-col gap-3">
        <h2 className="typo-title-sm">Notater</h2>
        <div className="w-full min-h-[4lh] bg-root-muted rounded-md animate-pulse" />
      </div>
    )
  }

  return (
    <div className="flex flex-col gap-3">
      <h2 className="typo-title-sm">Notater</h2>
      <div className="relative grow flex flex-col">
        <SaveIndicator isSaving={isSaving} isTyping={isTyping} />

        <RichTextEditor
          className="w-full min-h-[4lh] grow flex flex-col bg-root-muted rounded-md [&_.quill]:grow [&_.ql-editor]:px-2 [&_.ql-editor]:py-2.5 [&_.ql-blank.ql-editor::before]:left-2 [&_.ql-blank.ql-editor::before]:ink-muted"
          value={value}
          onChange={(value) => {
            if (value.ops) {
              setIsTyping(true) // User is typing
              setIsSaving(false) // Hide loading while typing
              setValue({ text: convertDeltaToHtml(value.ops, Quill) })
              saveNotes(value.ops)
            }
          }}
          formats={QUILL_FORMATS}
          placeholder="Notater er kun synlig for meglerne på oppdraget og ikke for kunden."
        />
      </div>
    </div>
  )
}
