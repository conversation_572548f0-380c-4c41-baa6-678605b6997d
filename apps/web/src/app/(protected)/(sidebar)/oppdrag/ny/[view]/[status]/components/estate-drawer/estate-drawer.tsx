import { Check, ChevronsRight, Circle } from 'lucide-react'

import { cn } from '@nordvik/theme/cn'
import { Avatar, AvatarFallback, AvatarImage } from '@nordvik/ui/avatar'
import { Badge } from '@nordvik/ui/badge'
import { Button } from '@nordvik/ui/button'
import { Link } from '@nordvik/ui/global-navigation-progress/link'
import { Sheet, SheetContent } from '@nordvik/ui/sheet'
import { Textarea } from '@nordvik/ui/textarea'

import { useEstateDrawerDetailsQuery } from '@/api/generated-client'
import { Icons } from '@/components/icons'
import { formatDate } from '@/lib/dates'

import { useSidePanel } from '../../client-fetching/use-side-panel'

import { mockEstate } from './mock-estate'

const EstateDrawer = () => {
  const { isOpen, openPanel, estateId, closePanel } = useSidePanel()
  const { data } = useEstateDrawerDetailsQuery(
    { id: estateId! },
    { enabled: isOpen && !!estateId },
  )

  if (!data?.estate) return null

  return (
    <div>
      <button onClick={() => openPanel(estateId!)} className="sr-only">
        Se mer
      </button>
      <Sheet open={isOpen} onOpenChange={closePanel}>
        <SheetContent
          className="w-full max-w-xl bg-root-muted p-0"
          closeButton={false}
        >
          <div className="flex flex-row items-center gap-2 bg-background-root px-6 py-3 border-b border-stroke-muted h-[60px]">
            <Button
              iconOnly={<ChevronsRight />}
              size="md"
              variant="ghost"
              onClick={closePanel}
            />
            <h2 className="typo-body-md-bold">Detaljer</h2>
          </div>
          <div className="flex flex-col gap-4 bg-background-root px-6 py-4 border-b border-stroke-muted">
            <div className="flex flex-row gap-2 justify-start">
              <img
                src={data.estate.mainImage?.large}
                alt=""
                className="rounded-sm w-12 h-12"
              />
              <div>
                <p className="typo-body-md-bold">
                  {data.estate.address?.streetAddress}
                </p>
                <p className="typo-body-md">
                  {data.estate.assignmentNumber}{' '}
                  <a href={data.estate.linkToNext}>Se i next</a>{' '}
                  {/** TODO: Look for link component */}
                </p>
              </div>
            </div>
            <div className="flex flex-row justify-between">
              <div>
                <p className="typo-body-sm-bold">Prisantydning</p>
                <p className="typo-body-sm">
                  {data.estate.estatePrice?.priceSuggestion}
                </p>
              </div>
              <div>
                <p className="typo-body-sm-bold">BRA-i</p>
                <p className="typo-body-sm">{data.estate.sumArea?.braI}</p>
              </div>
              <div>
                <p className="typo-body-sm-bold">Antall rom</p>
                <p className="typo-body-sm">{data.estate.noOfRooms}</p>
              </div>
            </div>
            <hr className="border-stroke-muted" />
            <div className="grid grid-cols-3 gap-2">
              <p>fase</p> <p className="col-span-2">{mockEstate.meta.phase}</p>
              <p>Megler</p>
              <div className="flex gap-2 col-span-2">
                <Avatar
                  className={cn(
                    'size-6 ring-2 ring-transparent ring-offset-2 ring-offset-background-root',
                  )}
                >
                  {mockEstate.meta.broker.image ? (
                    <AvatarImage
                      src={mockEstate.meta.broker.image}
                      alt={mockEstate.meta.broker.name || 'Megler'}
                    />
                  ) : (
                    <AvatarFallback>
                      {getInitials(mockEstate.meta.broker.name)}
                    </AvatarFallback>
                  )}
                </Avatar>
                <span>{mockEstate.meta.broker.name}</span>
              </div>
              <p>Ansvarlig megler</p>
              <div className="flex gap-2 col-span-2">
                <Avatar
                  className={cn(
                    'size-6 ring-2 ring-transparent ring-offset-2 ring-offset-background-root',
                  )}
                >
                  {mockEstate.meta.broker.image ? (
                    <AvatarImage
                      src={mockEstate.meta.broker.image}
                      alt={mockEstate.meta.broker.name || 'Megler'}
                    />
                  ) : (
                    <AvatarFallback>
                      {getInitials(mockEstate.meta.broker.name)}
                    </AvatarFallback>
                  )}
                </Avatar>
                <span>{mockEstate.meta.broker.name}</span>
              </div>
              <div className="col-span-3">
                <Textarea value={mockEstate.meta.note} variant="fill" />
              </div>
            </div>
          </div>
          <div className="p-4 flex flex-col gap-4">
            <div className="bg-background-root rounded-sm p-4 flex flex-col gap-2">
              <div>
                <h3 className="typo-body-sm-bold">Oppgaver selgere</h3>
                <h3 className="typo-body-sm">
                  Status på innhenting av informasjon fra selgere
                </h3>

                {mockEstate.sellersTasks.map((task) => (
                  <div
                    key={task.title}
                    className="flex gap-2 justify-between py-2 border-b last:border-0 border-stroke-muted"
                  >
                    <div className="flex gap-2 justify-between">
                      <StatusBadge status={task.status} />
                      <p className="typo-body-sm">{task.title}</p>
                    </div>
                    <p className="typo-body-sm text-ink-muted">
                      {getStatus(task.status)}
                    </p>
                  </div>
                ))}
              </div>
            </div>
            <div className="bg-background-root rounded-sm p-4 flex flex-col gap-2">
              <div>
                <h3 className="typo-body-sm-bold">Meglers ansvar</h3>
                <h3 className="typo-body-sm">Status på oppgaver i Next</h3>

                {mockEstate.brokersTaks.map((task) => (
                  <div
                    key={task.title}
                    className="flex gap-2 justify-between py-2 border-b last:border-0 border-stroke-muted"
                  >
                    <div className="flex gap-2 justify-between">
                      <StatusBadge status={task.status} />
                      <p className="typo-body-sm">{task.title}</p>
                    </div>
                    <p className="typo-body-sm text-ink-muted">
                      {getStatus(task.status)}
                    </p>
                  </div>
                ))}
              </div>
            </div>
            <div className="bg-background-root rounded-sm p-4 flex flex-col gap-2">
              <div>
                <h3 className="typo-body-sm-bold">Fremdriftsplan</h3>
                <h3 className="typo-body-sm">
                  Datoer er hentet fra befaringsmappa. Datoer som settes i Next
                  vil overstyre
                </h3>

                {mockEstate.inspectionEvents.map((inspectionEvent) => (
                  <div
                    key={inspectionEvent.title}
                    className="flex gap-2 justify-between items-center py-2 border-b last:border-0 border-stroke-muted"
                  >
                    <div className="flex gap-2 justify-between">
                      <p className="typo-body-sm">
                        {inspectionEvent.title}{' '}
                        {inspectionEvent.origin === 'next' && (
                          <Badge variant={'ghost'} className="ml-2">
                            Fra Next
                          </Badge>
                        )}
                      </p>
                    </div>
                    <div>
                      <p className="typo-body-sm text-ink-muted">
                        {formatDate(inspectionEvent.date)}
                      </p>
                      {inspectionEvent.startTime && (
                        <p className="typo-body-sm text-ink-muted">
                          {`kl ${formatDate(inspectionEvent.startTime, 'HH:mm')}${inspectionEvent.endTime && `-${formatDate(inspectionEvent.endTime, 'HH:mm')}`}`}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
              <Link href={mockEstate.meta.befaringsmappaUrl} target="_blank">
                Endre i fremdriftsplanen
              </Link>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </div>
  )
}

function getInitials(name?: string) {
  if (!name) return 'N'
  return name
    .split(' ')
    .filter(Boolean)
    .map((n) => n[0]!)
    .join('')
    .toUpperCase()
}

const StatusBadge = ({ status }: { status: number }) => {
  if (status === 1) {
    return (
      <div className="rounded-sm border p-1 w-fit h-fit border-stroke-yellow-muted bg-fill-yellow-subtle fill-interactive-emphasis text-fill-interactive-emphasis">
        <Icons.standby className="w-4 h-4 text-ink-on-yellow-subtle" />
      </div>
    )
  }
  if (status === 2) {
    return (
      <div className="rounded-sm border p-1 w-fit h-fit border-success-muted bg-fill-success-subtle text-ink-on-success-emphasis">
        <Check className="w-4 h-4" />
      </div>
    )
  }
  return (
    <div className="rounded-sm border p-1 w-fit h-fit border-stroke-muted bg-fill-gray-muted text-fill-interactive-emphasis">
      <Circle className="w-4 h-4 p-0.5" />
    </div>
  )
}
const getStatus = (status: number): string => {
  switch (status) {
    case 0:
      return 'Ikke mottatt'
    case 1:
      return 'Sendt'
    case 2:
      return 'Utført'
    default:
      return 'Ukjent'
  }
}

export default EstateDrawer
