'use client'

import React from 'react'

import type { GQLEstatesOverviewItemFragment } from '@/api/generated-client'
import { formatDate } from '@/lib/dates'

export function ViewingCell({
  showings,
  inspectionViewings,
}: {
  showings: GQLEstatesOverviewItemFragment['showings']
  inspectionViewings: NonNullable<
    GQLEstatesOverviewItemFragment['inspectionEvents']
  >
}) {
  // Only include future showings, sort ascending and take up to 2 upcoming
  const upcoming = React.useMemo(() => {
    if (!showings || showings.length === 0)
      return [] as NonNullable<typeof showings>
    const now = Date.now()
    return [...showings]
      .filter((s) => s?.start && new Date(s.start!).getTime() >= now)
      .sort(
        (a, b) => new Date(a!.start!).getTime() - new Date(b!.start!).getTime(),
      )
      .slice(0, 2)
  }, [showings])

  if (upcoming.length === 0) {
    if (inspectionViewings.length > 0) {
      return (
        <div className="space-y-0.5">
          {inspectionViewings.map((s, idx) => (
            <div key={s.id ?? idx} className="leading-5 whitespace-nowrap">
              {formatDate(s.start!, 'd. MMM ')}
            </div>
          ))}
        </div>
      )
    }

    return <span className="ink-muted">Ikke satt</span>
  }

  return (
    <div className="space-y-0.5">
      {upcoming.map((s, idx) => (
        <div key={s!.showingId ?? idx} className="leading-5 whitespace-nowrap">
          {formatDate(s!.start!, 'd. MMM HH:mm')}
        </div>
      ))}
    </div>
  )
}

export const MemoViewingCell = React.memo(ViewingCell)
