import { BrokerRole } from '@befaring/lib/broker-constants'
import { Fragment } from 'react'

import { cn } from '@nordvik/theme/cn'
import { Avatar, AvatarFallback, AvatarImage } from '@nordvik/ui/avatar'

import { GQLEstateDrawerDetailsQuery } from '@/api/generated-client'

type Props = Pick<NonNullable<GQLEstateDrawerDetailsQuery['estate']>, 'brokers'>

const Brokers = ({ brokers }: Props) => {
  const brokersSorted =
    brokers?.sort((a, b) => (a?.role ?? 0) - (b?.role ?? 0)) ?? []
  const responsibleBroker = brokersSorted[0]?.employeeRoles?.some(
    (role) => role?.typeId === 6,
  )
    ? brokersSorted[1]
    : brokersSorted[0]

  return (
    <>
      {brokersSorted.reduce(removeDuplicates, []).map((broker) => {
        if (!broker?.role) return null
        return (
          <Fragment key={`${broker?.employeeId}-${broker?.role}`}>
            <p className="typo-body-sm text-ink-muted">
              {getBrokerRole(broker, responsibleBroker)}
            </p>
            <Broker broker={broker} />
          </Fragment>
        )
      })}
    </>
  )
}

const Broker = ({
  broker,
}: {
  broker: NonNullable<Props['brokers']>[number]
}) => {
  if (!broker) return null
  return (
    <div className="flex gap-2 col-span-2">
      <Avatar
        className={cn(
          'size-6 ring-2 ring-transparent ring-offset-2 ring-offset-background-root',
        )}
      >
        {broker.image?.small ? (
          <AvatarImage src={broker.image.small} alt={''} />
        ) : (
          <AvatarFallback>{getInitials(broker.name)}</AvatarFallback>
        )}
      </Avatar>
      <span className="typo-body-sm">{broker.name ?? 'Ukjent megler'}</span>
    </div>
  )
}

function removeDuplicates(
  acc: NonNullable<Props['brokers']>,
  val: NonNullable<Props['brokers']>[number],
) {
  if (acc?.find((v) => v?.employeeId === val?.employeeId)) {
    return acc
  }
  return [...acc, val]
}

function getBrokerRole(
  broker: NonNullable<Props['brokers']>[number],
  responsibleBroker: NonNullable<Props['brokers']>[number],
): string {
  if (broker?.role === BrokerRole.Main) {
    if (broker.employeeRoles?.some((role) => role?.typeId === 6)) {
      return 'Eiendomsmeglerfullmektig'
    }
    return 'Megler'
  }
  if (broker?.role === BrokerRole.Responsible) {
    if (responsibleBroker?.employeeId === broker.employeeId) {
      return 'Ansvarlig megler'
    }
    return 'Medhjelper'
  }
  if (broker?.role === BrokerRole.Assistant) {
    return 'Assistent'
  }

  if (broker?.role === BrokerRole.SecondaryAssistant) {
    return 'Medhjelper'
  }

  return 'Megler'
}

function getInitials(name?: string): string {
  if (!name) return 'N'
  return name
    .split(' ')
    .filter(Boolean)
    .map((n) => n[0]!)
    .join('')
    .toUpperCase()
}

export default Brokers
