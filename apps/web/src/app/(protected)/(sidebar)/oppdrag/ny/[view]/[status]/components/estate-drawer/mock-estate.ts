export const mockEstate = {
  estate: {
    address: 'Abborveien 42',
    image: 'cdn.nordvik.no/abborveien42.jpg',
    assignmentNumber: '13-0401/25',
    nextLink: 'google.com',
    value: '10200000',
    brai: '106',
    roomCount: 3,
  },
  meta: {
    phase: 'Klargj<PERSON>ring',
    broker: { image: 'cdn.nordvik.no/nora.jpeg', name: '<PERSON>' }, // Not sure if this should be an array and add a "type" field for megler1, fullmektig, ansvarlig... or if they should be individual properties
    sellers: [{ name: '<PERSON>' }, { name: '<PERSON>' }],
    note: '...........',
    befaringsmappaUrl: 'www.google.com', // Needs a better name
  },
  sellersTasks: [
    { title: 'task1', status: 0 }, // Open for other data than 0, 1, 2
    { title: 'task2', status: 1 },
    { title: 'task3', status: 2 },
  ],
  brokersTaks: [
    { title: 'task1', status: 0 },
    { title: 'task2', status: 1 },
    { title: 'task3', status: 2 },
  ],
  inspectionEvents: [
    {
      title: 'Bygningssakkynding',
      date: new Date(),
      origin: 'next',
    },
    {
      title: 'Styling',
      date: new Date(),
      origin: 'bm',
    },
    {
      title: 'Visning 1',
      date: new Date(),
      startTime: new Date(),
      endTime: new Date(),
      origin: 'next',
    },
    {
      title: 'Visning 2',
      date: new Date(),
      startTime: new Date(),
      endTime: new Date(),
      origin: 'next',
    },
  ],
}
