query estateDrawerDetails($id: String!) {
  estate(id: $id) {
    status
    id
    estateId

    address {
      streetAddress
      city
      zipCode
      municipality
    }

    latitude
    longitude

    assignmentNumber
    assignmentType
    isValuation
    isEtakstPublished
    ownAssignmentType
    noOfBedRooms
    noOfRooms
    soldDate
    commissionAcceptedDate
    linkToNext

    finn {
      finnExpireDate
      finnPublishDate
    }

    estatePrice {
      totalPrice
      priceSuggestion
      soldPrice
    }

    sumArea {
      braI
      pRom
    }
    areaSize {
      BRAItotal
    }

    mainImage {
      large
    }

    placeholderImage

    links {
      linkType
      url
      text
    }

    sellers {
      firstName
      lastName
      email
      mobilePhone
      mainContact
      contactId
    }

    showings {
      start
      end
      showingId
    }

    activities {
      start
      end
      type
      typeName
      name
      performedById
      done
      id
      value
    }

    upcomingEvents {
      start
      end
      type
      typeName
      name
      performedById
      done
      id
    }

    linkToNext
    hjemUrl

    inspectionFolder {
      id
      publishedAt
    }

    listingAgreement {
      id
      updatedAt
      createdAt
      signicatDocumentId
      feePercentage
      suggestedPrice
      deadline
      signedAt
      initiatedSigningAt
      sentToClientAt
      status
      signers {
        id
        externalSignerId
        url
        signedAt
        title
        email
        firstName
        lastName
      }
      deadlineHasBeenExceeded
      commission
    }
    brokers {
      employeeId
      name
      mobilePhone
      email
      slug
      role
      title
      employeeRoles {
        source
        typeId
        name
      }
      image {
        small
      }
    }

    brokersIdWithRoles {
      employeeId
      brokerRole
      employee {
        title
        name
        email
        mobilePhone
        image {
          small
        }
      }
    }
  }
}
