import { TextButton } from '@nordvik/ui/text-button'

import { GQLEstateDrawerDetailsQuery } from '@/api/generated-client'
import { formatCurrency } from '@/lib/formatCurrency'

import EstateThumbnail from '../../estate-thumbnail'

type Props = {
  estate: NonNullable<GQLEstateDrawerDetailsQuery['estate']>
}
const EstateDetails = ({ estate }: Props) => {
  const price = estate.estatePrice?.priceSuggestion
  return (
    <>
      <div className="flex flex-row gap-2 justify-start">
        <EstateThumbnail imageSource={estate.mainImage?.large} />
        <div>
          <p className="typo-body-md-bold">{estate.address?.streetAddress}</p>
          <div className="flex gap-3">
            {estate.assignmentNumber && (
              <span className="mr-1 ink-muted typo-body-sm">
                {estate.assignmentNumber}
              </span>
            )}
            {estate.linkToNext && (
              <TextButton
                size="md"
                href={estate.linkToNext}
                target="_blank"
                iconEnd="external"
                onClick={(e) => e.stopPropagation()}
              >
                Se i Next
              </TextButton>
            )}
          </div>
        </div>
      </div>
      <div className="flex flex-row justify-between">
        <EstateDetailItem title="Prisantydning">
          {price ? formatCurrency(price) : 'Ukjent'}
        </EstateDetailItem>
        <EstateDetailItem title="BRA-i">
          {estate.sumArea?.braI ? (
            <span>
              {estate.sumArea?.braI} m<sup>2</sup>
            </span>
          ) : (
            'Ukjent'
          )}
        </EstateDetailItem>
        <EstateDetailItem title="Antall soverom">
          {estate.noOfBedRooms ? estate.noOfBedRooms : 'Ukjent'}
        </EstateDetailItem>
      </div>
    </>
  )
}

function EstateDetailItem({
  title,
  children,
}: React.PropsWithChildren<{ title: string }>) {
  return (
    <div>
      <p className="typo-body-sm-bold">{title}</p>
      <p className="typo-body-sm">{children}</p>
    </div>
  )
}

export default EstateDetails
