'use client'

import React from 'react'

import { DocumentStatuses } from '../../../client-fetching/document-statuses'

import { ColumnCell, EstateColumnProps } from './base'

export type SellerTasksColumnProps = EstateColumnProps

export const SellerTasksColumn = ({ estate }: SellerTasksColumnProps) => {
  return (
    <ColumnCell>
      <div className="flex flex-wrap gap-2">
        <DocumentStatuses estateId={estate.estateId} />
      </div>
    </ColumnCell>
  )
}
