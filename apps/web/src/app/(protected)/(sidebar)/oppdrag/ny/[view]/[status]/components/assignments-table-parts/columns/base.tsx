'use client'

import React from 'react'

import { GQLEstatesOverviewItemFragment } from '@/api/generated-client'

export type ColumnCellProps = {
  children: React.ReactNode
  className?: string
  // Whether the table cell should render a border. Defaults to true since most cells in the table have borders
  withBorder?: boolean
}

export const ColumnCell = ({
  children,
  className,
  withBorder = true,
}: ColumnCellProps) => {
  const base = `py-8 px-3 border-stroke-muted${withBorder ? ' border' : ''}`
  return (
    <td className={`${base}${className ? ` ${className}` : ''}`}>{children}</td>
  )
}

export type EstateColumnProps = {
  estate: GQLEstatesOverviewItemFragment
}
