import { Badge } from '@nordvik/ui/badge'
import { Textarea } from '@nordvik/ui/textarea'

import { GQLEstateDrawerDetailsQuery } from '@/api/generated-client'
import { getStatusNameByNumber } from '@/app/(protected)/(sidebar)/oppdrag/util'
import type { TaskBadgeState } from '@/components/task-status-badge/task-status-badge'
import { providerTypesForStatus } from '@/server/model/AssignmentDocumentStatus/helpers/provider-types'

import { StatusItem } from '../../client-fetching/use-document-statuses-batch-sse'
import { useSidePanel } from '../../client-fetching/use-side-panel'
import { CHECKLIST_ITEMS } from '../assignments-table-parts/columns/broker-tasks-column'
import { useCurrentEstatesTab } from '../use-current-estates-tab'

import Brokers from './estate-drawer-parts/brokers'
import EstateDetails from './estate-drawer-parts/estate-details'
import EventList from './estate-drawer-parts/event-list'
import PublishDate from './estate-drawer-parts/publish-date'
import Sellers from './estate-drawer-parts/sellers'
import Showings from './estate-drawer-parts/showings'
import TaskList from './estate-drawer-parts/task-list'

type Props = {
  estateData: GQLEstateDrawerDetailsQuery
  documentStatuses: Record<string, StatusItem>
}

const EstateDrawerContent = ({ estateData, documentStatuses }: Props) => {
  const { estateId } = useSidePanel()

  const phaseLabel = useCurrentEstatesTab()

  const types = providerTypesForStatus(phaseLabel)

  const sellersTasks = types.map((a) => ({
    title: a.name,
    status: (documentStatuses[a.type]?.state as TaskBadgeState) || 'NONE',
  }))

  const brokersTasks: { title: string; status: TaskBadgeState }[] =
    CHECKLIST_ITEMS.map((a) => ({
      title: a.name,
      status: a.check(estateData.estate?.checklist) ? 'COMPLETE' : 'NONE',
    }))

  if (!estateId || !estateData.estate) return null

  const { estate, events } = estateData

  return (
    <>
      <div className="flex flex-col gap-4 bg-background-root px-6 py-4 border-b border-stroke-muted">
        <EstateDetails estate={estate} />
        <hr className="border-stroke-muted" />
        <div className="grid grid-cols-3 gap-2">
          <p className="typo-body-sm text-ink-muted">Fase</p>{' '}
          <Badge variant="beige" className="col-span-2 w-fit">
            {getStatusNameByNumber(estate.status)}
          </Badge>
          <Brokers brokers={estate.brokers} />
          <Sellers sellers={estate.sellers} />
          <div className="col-span-2"></div>
          <div className="col-span-3">
            <Textarea
              label="Notat"
              value=""
              placeholder="Skriv inn notat..."
              variant="fill"
              readOnly
            />
          </div>
        </div>
      </div>
      <div className="p-4 flex flex-col gap-4">
        <PublishDate estate={estate} />
        <Showings showings={estate.showings} />
        <TaskList
          title="Selgers ansvar"
          description="Status på innhenting av informasjon fra selgere"
          tasks={sellersTasks}
        />
        <TaskList
          title="Oppgaver"
          description="Status på viktige oppgaver i Next"
          tasks={brokersTasks}
        />
        <EventList estateId={estateId} events={events} />
      </div>
    </>
  )
}

export default EstateDrawerContent
