query NewAssignmentsOverview(
  $brokerId: String!
  $departmentId: Int!
  $limit: Int!
  $offset: Int!
  $tabs: [EstateTabFilter!]!
  $archived: Boolean = false
  $search: String
  $officeView: Boolean = false
  $brokerIds: [String!]
) {
  broker: estatesForBrokerById(
    brokerId: $brokerId
    limit: $limit
    offset: $offset
    tabs: $tabs
    assignmentTypeGroup: [0, 1, 2, 4, 5, 6, 7, 8, 9, 10]
    archived: $archived
    search: $search
  ) @skip(if: $officeView) {
    pagination {
      total
      count
      offset
      limit
    }
    items {
      ...EstatesOverviewItem
    }
  }

  office: estatesForDepartment(
    departmentId: $departmentId
    limit: $limit
    offset: $offset
    tabs: $tabs
    assignmentTypeGroup: [0, 1, 2, 4, 5, 6, 7, 8, 9, 10]
    archived: $archived
    search: $search
    brokerIds: $brokerIds
  ) @include(if: $officeView) {
    pagination {
      total
      count
      offset
      limit
    }
    items {
      ...EstatesOverviewItem
    }
  }
}

fragment EstatesOverviewItem on BrokerEstate {
  mainImage {
    small
  }
  estateId
  status
  isValuation
  checklist {
    firstTag
    value
  }
  marketingStart
  soldDate
  showings {
    start
    end
    showingId
  }
  brokers {
    name
    email
    role
    image {
      small
    }
  }
  address {
    streetAddress
  }
  linkToNext
  assignmentNumber
}
