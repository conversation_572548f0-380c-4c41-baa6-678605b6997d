'use client'

import React from 'react'

import { MemoViewingCell } from '../viewing-cell'

import { ColumnCell, EstateColumnProps } from './base'

export type ViewingColumnProps = EstateColumnProps

export const ViewingColumn = ({ estate }: ViewingColumnProps) => {
  return (
    <ColumnCell>
      <MemoViewingCell
        showings={estate.showings}
        inspectionViewings={
          estate.inspectionEvents?.filter((e) => e.type === 'viewing') || []
        }
      />
    </ColumnCell>
  )
}
