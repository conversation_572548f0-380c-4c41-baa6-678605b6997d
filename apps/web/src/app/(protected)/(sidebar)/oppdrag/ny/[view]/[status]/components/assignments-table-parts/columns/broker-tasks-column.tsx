'use client'

import React from 'react'

import { MiniRing } from '../../mini-ring'

import { ColumnCell, EstateColumnProps } from './base'

export type BrokerTasksColumnProps = EstateColumnProps

export const BrokerTasksColumn = ({ estate }: BrokerTasksColumnProps) => {
  const totalChecks = estate.checklist?.length || 0
  const completedChecks =
    estate.checklist?.filter((item) => item.value).length || 0
  const progress = totalChecks ? completedChecks / totalChecks : 0

  return (
    <ColumnCell>
      <div className="flex items-center gap-2 ">
        <MiniRing progress={progress} />
        <div>
          <span>{completedChecks}</span>/{totalChecks}
        </div>
      </div>
    </ColumnCell>
  )
}
