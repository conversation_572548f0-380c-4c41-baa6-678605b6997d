'use client'

import React from 'react'

import { Table, TableBody, TableFooter } from '@nordvik/ui/table'

import {
  GQLEstateTabFilter,
  useInfiniteNewAssignmentsOverviewQuery,
} from '@/api/generated-client'
import { useSearchQuery } from '@/hooks/useSearchQuery'
import { useUserContext } from '@/lib/UserContext'

import { useCurrentTab } from '../../../../status/[status]/components/estate-list/use-current-tab'
import { estateListStatusMap } from '../../../../util'
import { DocumentStatusesProvider } from '../client-fetching/use-document-statuses-batch-sse'
import { SidePanelProvider } from '../client-fetching/use-side-panel'

import { GroupedBody } from './assignments-table-parts/grouped-body'
import {
  useAssignmentsDerived,
  useSortingQueryState,
} from './assignments-table-parts/hooks'
import { LoadMoreButton } from './assignments-table-parts/load-more-button'
import { SkeletonRows } from './assignments-table-parts/skeleton-rows'
import { TableFooterInfo } from './assignments-table-parts/table-footer-info'
import { TableHeaderControls } from './assignments-table-parts/table-header-controls'
import { UngroupedBody } from './assignments-table-parts/ungrouped-body'
import EstateDrawer from './estate-drawer/estate-drawer'
import { useEstatesFromQuery } from './use-estates-from-query'

export type SortKey = 'address' | 'publish'
export type SortDir = 'asc' | 'desc'

const PAGE_LIMIT = 15

export function AssignmentsTable({ status }: { status: string }) {
  // To be implemeted later
  const officeView = false

  const [search] = useSearchQuery()

  const { tab, isArchiveTab } = useCurrentTab(status)

  const tabs = estateListStatusMap[tab] ?? [GQLEstateTabFilter.Requested]

  // To pass to child components
  const currentTab = tabs[0] as GQLEstateTabFilter

  const { sortKey, dirKey, onSortAddress, onSortPublish, ariaSortFor } =
    useSortingQueryState()

  const { user } = useUserContext()

  const baseVars = {
    brokerId: user?.employeeId ?? '',
    departmentId: Number(user?.department?.departmentId) || 0,
    tabs,
    limit: PAGE_LIMIT,
    offset: 0,
    archived: isArchiveTab,
    search,
    officeView: officeView || undefined,
  }

  const {
    data,
    isError,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
  } = useInfiniteNewAssignmentsOverviewQuery(baseVars, {
    enabled:
      Boolean(user?.employeeId) && Boolean(user?.department?.departmentId),
    initialPageParam: { offset: 0, limit: PAGE_LIMIT },
    getNextPageParam: (lastPage) => {
      const pag = officeView
        ? lastPage.office?.pagination
        : lastPage.broker?.pagination
      if (!pag) return undefined
      const nextOffset = (pag.offset || 0) + (pag.count || 0)
      if (nextOffset >= (pag.total || 0)) return undefined
      return { offset: nextOffset, limit: pag.limit || PAGE_LIMIT }
    },
    refetchOnWindowFocus: false,
  })

  const { allEstates, totalFromQuery } = useEstatesFromQuery(
    data?.pages ?? [],
    officeView,
  )

  // Derived UI state (sorted, groups, counts, visible ids)
  const {
    inPreperation,
    sorted,
    groups,
    withoutPublishDate,
    visibleCount,
    visibleEstateIds,
  } = useAssignmentsDerived(allEstates, sortKey, dirKey, currentTab)

  return (
    <DocumentStatusesProvider estateIds={visibleEstateIds} status={currentTab!}>
      <SidePanelProvider>
        <div className="mt-2 rounded-lg ">
          <Table className="text-sm">
            <TableHeaderControls
              ariaSortFor={ariaSortFor}
              onSortAddress={onSortAddress}
              onSortPublish={onSortPublish}
              currentTab={currentTab}
            />
            <TableBody alwaysLastBorder>
              {inPreperation ? (
                <GroupedBody
                  groups={groups}
                  withoutPublishDate={withoutPublishDate}
                  currentTab={currentTab}
                />
              ) : (
                <UngroupedBody estates={sorted} currentTab={currentTab} />
              )}
              {(isLoading || isFetchingNextPage) && (
                <SkeletonRows colSpan={undefined} currentTab={currentTab} />
              )}
            </TableBody>
            <TableFooter>
              <TableFooterInfo
                visibleCount={visibleCount}
                totalFromQuery={totalFromQuery}
                isError={isError}
              />
            </TableFooter>
          </Table>

          <LoadMoreButton
            hasNextPage={!!hasNextPage}
            isFetchingNextPage={isFetchingNextPage}
            onClick={() => fetchNextPage()}
          />
        </div>
        <EstateDrawer />
      </SidePanelProvider>
    </DocumentStatusesProvider>
  )
}
