'use client'

import { Table, TableBody, TableFooter } from '@nordvik/ui/table'

import {
  GQLEstateTabFilter,
  GQLSortEstateBy,
  useInfiniteNewAssignmentsOverviewQuery,
} from '@/api/generated-client'
import InfinityScroll from '@/components/infinity-scroll'
import { useBrokerIdsQuery } from '@/hooks/useBrokerIdsQuery'
import { useSearchQuery } from '@/hooks/useSearchQuery'
import { useUserContext } from '@/lib/UserContext'

import { useCurrentTab } from '../../../../status/[status]/components/estate-list/use-current-tab'
import { estateListStatusMap } from '../../../../util'
import { DocumentStatusesProvider } from '../client-fetching/use-document-statuses-batch-sse'
import { SidePanelProvider } from '../client-fetching/use-side-panel'

import { GroupedBody } from './assignments-table-parts/grouped-body'
import {
  useAssignmentsDerived,
  useSortingQueryState,
} from './assignments-table-parts/hooks'
import { SearchGroupedBody } from './assignments-table-parts/search-grouped-body'
import { SkeletonRows } from './assignments-table-parts/skeleton-rows'
import { TableFooterInfo } from './assignments-table-parts/table-footer-info'
import { TableHeaderControls } from './assignments-table-parts/table-header-controls'
import { UngroupedBody } from './assignments-table-parts/ungrouped-body'
import { BrokersFilter } from './brokers-filter'
import EstateDrawer from './estate-drawer/estate-drawer'
import { useEstatesFromQuery } from './use-estates-from-query'
import { useGroupedSearchResults } from './use-grouped-search-results'

export type SortKey = 'address' | 'publish'
export type SortDir = 'asc' | 'desc'

const PAGE_LIMIT = 15

export function AssignmentsTable({
  status,
  view,
}: {
  status: string
  view: string
}) {
  const officeView = view === 'kontor'

  const [search] = useSearchQuery()

  const { tab, isArchiveTab } = useCurrentTab(status)

  const tabs = estateListStatusMap[tab] ?? [GQLEstateTabFilter.Requested]

  // To pass to child components
  const currentTab = tabs[0] as GQLEstateTabFilter

  const { onSortAddress, onSortPublish, ariaSortFor } = useSortingQueryState()

  const { user } = useUserContext()

  const [selectedBrokerIds] = useBrokerIdsQuery()

  const baseVars = {
    brokerId: user?.employeeId ?? '',
    departmentId: Number(user?.department?.departmentId) || 0,
    tabs,
    limit: PAGE_LIMIT,
    offset: 0,
    archived: isArchiveTab,
    search,
    officeView: officeView || undefined,
    sortBy: GQLSortEstateBy.ChangedDate,
    brokerIds:
      officeView && selectedBrokerIds.length ? selectedBrokerIds : undefined,
  }

  const {
    data,
    isError,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
  } = useInfiniteNewAssignmentsOverviewQuery(baseVars, {
    enabled:
      Boolean(user?.employeeId) && Boolean(user?.department?.departmentId),
    initialPageParam: { offset: 0, limit: PAGE_LIMIT },
    getNextPageParam: (lastPage) => {
      const pag = officeView
        ? lastPage.office?.pagination
        : lastPage.broker?.pagination
      if (!pag) return undefined
      const nextOffset = (pag.offset || 0) + (pag.count || 0)
      if (nextOffset >= (pag.total || 0)) return undefined
      return { offset: nextOffset, limit: pag.limit || PAGE_LIMIT }
    },
    refetchOnWindowFocus: false,
  })

  const { allEstates, totalFromQuery } = useEstatesFromQuery(
    data?.pages ?? [],
    officeView,
  )

  // Derived UI state (sorted, groups, counts, visible ids)
  const { groups, withoutPublishDate, visibleCount, visibleEstateIds } =
    useAssignmentsDerived(allEstates, currentTab)

  // Derive grouped search result state (multi-status search)
  const { hasSearch, grouped } = useGroupedSearchResults(allEstates, search)

  return (
    <DocumentStatusesProvider estateIds={visibleEstateIds} status={currentTab!}>
      <SidePanelProvider>
        <div className="mt-6 rounded-lg">
          {officeView && !hasSearch && (
            <div className="mb-3 flex gap-2">
              <BrokersFilter
                departmentId={Number(user?.department?.departmentId) || 0}
                fallbackBrokers={allEstates
                  .flatMap((e) => e.brokers || [])
                  .filter((b): b is NonNullable<typeof b> => !!b)}
                disabled={isLoading}
              />
            </div>
          )}
          <InfinityScroll
            fetchNextPage={() => {
              fetchNextPage()
            }}
            isLoading={isLoading || isFetchingNextPage}
            lastPage={!hasNextPage}
          >
            {hasSearch ? (
              <SearchGroupedBody groups={grouped} />
            ) : (
              <Table className="text-sm">
                <TableHeaderControls
                  ariaSortFor={ariaSortFor}
                  onSortAddress={onSortAddress}
                  onSortPublish={onSortPublish}
                  currentTab={currentTab}
                />
                <TableBody alwaysLastBorder>
                  {officeView &&
                  currentTab === GQLEstateTabFilter.InPreparation ? (
                    <GroupedBody
                      groups={groups}
                      withoutPublishDate={withoutPublishDate}
                      currentTab={currentTab}
                    />
                  ) : (
                    <UngroupedBody
                      estates={allEstates}
                      currentTab={currentTab}
                    />
                  )}
                  {(isLoading || isFetchingNextPage) && (
                    <SkeletonRows colSpan={undefined} currentTab={currentTab} />
                  )}
                </TableBody>
                <TableFooter className="bg-root">
                  <TableFooterInfo
                    visibleCount={visibleCount}
                    totalFromQuery={totalFromQuery}
                    isError={isError}
                  />
                </TableFooter>
              </Table>
            )}
          </InfinityScroll>
        </div>
        <EstateDrawer />
      </SidePanelProvider>
    </DocumentStatusesProvider>
  )
}
