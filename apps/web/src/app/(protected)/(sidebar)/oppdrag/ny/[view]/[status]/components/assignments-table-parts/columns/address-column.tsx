'use client'

import Image from 'next/image'
import React from 'react'

import { TextButton } from '@nordvik/ui/text-button'

import { ColumnCell, EstateColumnProps } from './base'

export type AddressColumnProps = EstateColumnProps

export const AddressColumn = ({ estate }: AddressColumnProps) => {
  return (
    <ColumnCell withBorder={false} className="pl-4">
      <div className="flex items-start gap-3">
        {estate.mainImage?.small ? (
          <Image
            src={estate.mainImage.small}
            alt={
              estate.address?.streetAddress
                ? `${estate.address.streetAddress} – bilde`
                : 'Boligbilde'
            }
            width={48}
            height={48}
            sizes="48px"
            className="size-12 shrink-0 rounded-md object-cover"
          />
        ) : (
          <div className="size-12 shrink-0 rounded-md bg-root-muted" />
        )}
        <div className="space-y-1">
          <div className="typo-body-sm-bold leading-5">
            {estate.address?.streetAddress ?? '—'}
          </div>
          <div className="flex gap-2 flex-wrap items-center">
            <span className="ink-muted typo-body-sm">
              {estate.assignmentNumber || '—'}
            </span>
            <TextButton
              size="md"
              href={estate.linkToNext}
              target="_blank"
              iconEnd="external"
            >
              Se i Next
            </TextButton>
          </div>
        </div>
      </div>
    </ColumnCell>
  )
}
