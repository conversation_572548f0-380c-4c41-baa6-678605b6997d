import React from 'react'

import { But<PERSON> } from '@nordvik/ui/button'

export function LoadMoreButton({
  hasNextPage,
  isFetchingNextPage,
  onClick,
}: {
  hasNextPage: boolean
  isFetchingNextPage: boolean
  onClick: () => void
}) {
  if (!hasNextPage) return null
  return (
    <div className="mt-4 flex">
      <Button
        className="w-full"
        variant="outline"
        size="sm"
        disabled={isFetchingNextPage}
        onClick={onClick}
      >
        {isFetchingNextPage ? 'Laster...' : 'Last flere'}
      </Button>
    </div>
  )
}
