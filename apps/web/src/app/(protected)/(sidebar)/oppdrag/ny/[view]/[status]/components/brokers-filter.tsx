'use client'

import { UsersIcon } from 'lucide-react'
import React from 'react'

import { cn } from '@nordvik/theme/cn'
import { Button } from '@nordvik/ui/button'
import { Checkbox } from '@nordvik/ui/checkbox'
import { Popover, PopoverContent, PopoverTrigger } from '@nordvik/ui/popover'
import { ScrollArea } from '@nordvik/ui/scroll-area'

import { useDepartmentEmployeesQuery } from '@/api/generated-client'
import { useBrokerIdsQuery } from '@/hooks/useBrokerIdsQuery'

export function BrokersFilter({
  departmentId,
  disabled,
  fallbackBrokers = [],
}: {
  departmentId?: number
  disabled?: boolean
  // Fallback list (from loaded estates) used until query returns
  fallbackBrokers?: { employeeId?: string | null; name?: string | null }[]
}) {
  const [selected, setSelected] = useBrokerIdsQuery()
  const [open, setOpen] = React.useState(false)
  const { data } = useDepartmentEmployeesQuery(
    { departmentId: departmentId ?? 0 },
    {
      enabled: typeof departmentId === 'number' && departmentId > 0,
      select: (d) =>
        d.department?.employees?.filter(Boolean).map((e) => ({
          employeeId: e?.employeeId ?? '',
          name: e?.name ?? 'Ukjent',
        })) ?? [],
      staleTime: 5 * 60 * 1000,
    },
  )

  const uniqueBrokers = React.useMemo(() => {
    const source = data && data.length > 0 ? data : fallbackBrokers
    const map = new Map<string, string>()
    source.forEach((b) => {
      if (b.employeeId) {
        if (!map.has(b.employeeId)) map.set(b.employeeId, b.name || 'Ukjent')
      }
    })
    return Array.from(map.entries())
      .map(([employeeId, name]) => ({ employeeId, name }))
      .sort((a, b) => a.name.localeCompare(b.name))
  }, [data, fallbackBrokers])

  const toggle = (id: string) => {
    if (selected.includes(id)) {
      setSelected(selected.filter((s) => s !== id))
    } else {
      setSelected([...selected, id])
    }
  }

  const clearAll = () => setSelected([])

  const summary = selected.length ? `${selected.length} valgt` : 'Meglere'

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={cn('rounded-md font-normal', disabled && 'opacity-50')}
          disabled={disabled || uniqueBrokers.length === 0}
        >
          <UsersIcon className="size-4" />
          <span className="truncate max-w-[140px] text-left">{summary}</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent align="start" className="w-64 p-0">
        <div className="flex items-center justify-between px-3 py-2 border-b border-stroke-muted">
          <p className="typo-body-sm font-medium">Meglere</p>
          {selected.length > 0 && (
            <Button
              onClick={clearAll}
              size="sm"
              variant="ghost"
              className="h-6 px-1 typo-body-xs"
            >
              Nullstill
            </Button>
          )}
        </div>
        <ScrollArea className="max-h-72">
          <ul className="divide-y divide-stroke-muted">
            {uniqueBrokers.map((b) => {
              const checked = selected.includes(b.employeeId)
              return (
                <li
                  key={b.employeeId}
                  onClick={() => toggle(b.employeeId)}
                  className={cn(
                    'flex w-full items-center gap-2 px-3 py-2 text-left hover:bg-root-muted focus:outline-none cursor-pointer',
                    checked && 'bg-root-muted',
                  )}
                >
                  <Checkbox
                    checked={checked}
                    onCheckedChange={() => toggle(b.employeeId)}
                    className="pointer-events-none"
                  />
                  <span className="typo-body-sm flex-1 truncate">{b.name}</span>
                </li>
              )
            })}
            {uniqueBrokers.length === 0 && (
              <li className="px-3 py-4 typo-body-sm ink-subtle">
                Ingen meglere
              </li>
            )}
          </ul>
        </ScrollArea>
      </PopoverContent>
    </Popover>
  )
}
