import React from 'react'

import { providerTypesForStatus } from '@/server/model/AssignmentDocumentStatus/helpers/provider-types'

import { useCurrentEstatesTab } from '../components/use-current-estates-tab'

import { DocumentStatusItem } from './document-status-item'
import { useEstateDocumentStatuses } from './use-document-statuses-batch-sse'

export const DocumentStatuses = React.memo(function DocumentStatuses({
  estateId,
}: {
  estateId: string
}) {
  const itemsMap = useEstateDocumentStatuses(estateId)
  const currentTab = useCurrentEstatesTab()
  const types = providerTypesForStatus(currentTab)

  return (
    <div className="flex flex-wrap gap-2">
      {types.map(({ type, name }) => (
        <DocumentStatusItem
          key={type}
          estateId={estateId}
          name={name}
          item={itemsMap[type]}
        />
      ))}
    </div>
  )
})
