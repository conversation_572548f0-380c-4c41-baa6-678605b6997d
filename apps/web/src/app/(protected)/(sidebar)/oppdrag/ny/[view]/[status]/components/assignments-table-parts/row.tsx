'use client'

import { useQueryClient } from '@tanstack/react-query'
import debounce from 'lodash/debounce'
import Image from 'next/image'
import React from 'react'

import { Badge } from '@nordvik/ui/badge'
import { TableRow } from '@nordvik/ui/table'
import { TextButton } from '@nordvik/ui/text-button'

import {
  GQLEstateTabFilter,
  GQLEstatesOverviewItemFragment,
  useEstateByIdQuery,
} from '@/api/generated-client'

import { DocumentStatuses } from '../../client-fetching/document-statuses'
import { useSidePanel } from '../../client-fetching/use-side-panel'
import { CalendarPopover } from '../calendar-popover'
import { MiniRing } from '../mini-ring'

import { MemoBrokersCell } from './brokers-cell'
import { ColumnId, getColumnsForTab } from './columns'
import { MemoViewingCell } from './viewing-cell'

export function Row({
  estate,
  currentTab,
  columns,
}: {
  estate: GQLEstatesOverviewItemFragment
  currentTab?: GQLEstateTabFilter
  columns?: ColumnId[]
}) {
  const totalChecks = estate.checklist?.length || 0
  const completedChecks =
    estate.checklist?.filter((item) => item.value).length || 0
  const progress = totalChecks ? completedChecks / totalChecks : 0

  const cols = React.useMemo(
    () => columns ?? getColumnsForTab(currentTab),
    [columns, currentTab],
  )

  const { openPanel } = useSidePanel()
  const queryClient = useQueryClient()

  // Prefetch of estate details so opening the drawer feels instant
  const prefetchEstate = React.useMemo(
    () =>
      debounce((estateId: string) => {
        const variables = { id: estateId }
        const queryKey = useEstateByIdQuery.getKey(variables)

        // Skip if already fetched or currently fetching
        const state = queryClient.getQueryState(queryKey)
        if (state?.status === 'success' || state?.status === 'pending') return

        const fetcher = useEstateByIdQuery.fetcher(variables)
        queryClient.prefetchQuery({
          queryKey,
          queryFn: fetcher,
          // Keep it fresh briefly to avoid immediate refetch on open
          staleTime: 60 * 1000,
        })
      }, 200),
    [queryClient],
  )

  return (
    <TableRow
      className="align-top border-stroke-muted"
      onClick={() => openPanel(estate.estateId)}
      onMouseEnter={() => prefetchEstate(estate.estateId)}
      onFocus={() => prefetchEstate(estate.estateId)}
    >
      {cols.map((c) => {
        // Maybe switch statement?
        if (c === 'address') {
          return (
            <td key={c} className="py-8 px-3 pl-4 border-stroke-muted">
              <div className="flex items-start gap-3">
                {estate.mainImage?.small ? (
                  <Image
                    src={estate.mainImage.small}
                    alt={
                      estate.address?.streetAddress
                        ? `${estate.address.streetAddress} – bilde`
                        : 'Boligbilde'
                    }
                    width={48}
                    height={48}
                    sizes="48px"
                    className="size-12 shrink-0 rounded-md object-cover"
                  />
                ) : (
                  <div className="size-12 shrink-0 rounded-md bg-root-muted" />
                )}
                <div className="space-y-1">
                  <div className="typo-body-sm-bold leading-5">
                    {estate.address?.streetAddress ?? '—'}
                  </div>
                  <div className="flex gap-2 flex-wrap items-center">
                    <span className="ink-muted typo-body-sm">
                      {estate.assignmentNumber || '—'}
                    </span>
                    <TextButton
                      size="md"
                      href={estate.linkToNext}
                      target="_blank"
                      iconEnd="external"
                    >
                      Se i Next
                    </TextButton>
                  </div>
                </div>
              </div>
            </td>
          )
        }
        if (c === 'brokerTasks') {
          return (
            <td key={c} className="py-8 px-3 border-stroke-muted border">
              <div className="flex items-center gap-2 ">
                <MiniRing progress={progress} />
                <div>
                  <span>{completedChecks}</span>/{totalChecks}
                </div>
              </div>
            </td>
          )
        }
        if (c === 'sellerTasks') {
          return (
            <td key={c} className="py-8 px-3 border-stroke-muted border">
              <div className="flex flex-wrap gap-2">
                <DocumentStatuses estateId={estate.estateId} />
              </div>
            </td>
          )
        }
        if (c === 'publish') {
          const isSold = currentTab === GQLEstateTabFilter.Sold
          const isReadOnly = currentTab === GQLEstateTabFilter.ForSale || isSold
          const date = isSold ? estate.soldDate : estate.marketingStart
          const d = date ? new Date(date) : undefined
          return (
            <td key={c} className="py-8 px-3 p-4 border-stroke-muted border">
              {isReadOnly ? (
                <Badge variant="grey" size="md">
                  {d
                    ? d.toLocaleDateString('no-NO', {
                        day: '2-digit',
                        month: '2-digit',
                        year: 'numeric',
                      })
                    : '—'}
                </Badge>
              ) : (
                <div onClick={(e) => e.stopPropagation()}>
                  <CalendarPopover publishDate={estate.marketingStart} />
                </div>
              )}
            </td>
          )
        }
        if (c === 'viewing') {
          return (
            <td key={c} className="py-8 px-3 border-stroke-muted border">
              <MemoViewingCell showings={estate.showings} />
            </td>
          )
        }
        if (c === 'brokers') {
          return (
            <td key={c} className="p-3 justify-end border-stroke-muted border">
              <MemoBrokersCell brokers={estate.brokers} />
            </td>
          )
        }
        return null
      })}
    </TableRow>
  )
}

export const MemoRow = React.memo(Row)
