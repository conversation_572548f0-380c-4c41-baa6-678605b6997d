'use client'

import { useQueryClient } from '@tanstack/react-query'
import debounce from 'lodash/debounce'
import React from 'react'

import { TableRow } from '@nordvik/ui/table'

import {
  GQLEstateTabFilter,
  GQLEstatesOverviewItemFragment,
  useEstateDrawerDetailsQuery,
} from '@/api/generated-client'

import { useSidePanel } from '../../client-fetching/use-side-panel'

import { ColumnId, getColumnsForTab } from './columns'
import { AddressColumn } from './columns/address-column'
import { BrokerTasksColumn } from './columns/broker-tasks-column'
import { BrokersColumn } from './columns/brokers-column'
import { PublishColumn } from './columns/publish-column'
import { SellerTasksColumn } from './columns/seller-tasks-column'
import { ViewingColumn } from './columns/viewing-column'

export function Row({
  estate,
  currentTab,
  columns,
}: {
  estate: GQLEstatesOverviewItemFragment
  currentTab?: GQLEstateTabFilter
  columns?: ColumnId[]
}) {
  const cols = React.useMemo(
    () => columns ?? getColumnsForTab(currentTab),
    [columns, currentTab],
  )

  const { openPanel } = useSidePanel()
  const queryClient = useQueryClient()

  // Prefetch of estate details so opening the drawer feels instant
  const prefetchEstate = React.useMemo(
    () =>
      debounce((estateId: string) => {
        const variables = { id: estateId }
        const queryKey = useEstateDrawerDetailsQuery.getKey(variables)

        // Skip if already fetched or currently fetching
        const state = queryClient.getQueryState(queryKey)
        if (state?.status === 'success' || state?.status === 'pending') return

        const fetcher = useEstateDrawerDetailsQuery.fetcher(variables)
        queryClient.prefetchQuery({
          queryKey,
          queryFn: fetcher,
          // Keep it fresh briefly to avoid immediate refetch on open
          staleTime: 60 * 1000,
        })
      }, 200),
    [queryClient],
  )

  return (
    <TableRow
      className="align-top border-stroke-muted"
      onClick={() => openPanel(estate.estateId)}
      onMouseEnter={() => prefetchEstate(estate.estateId)}
      onFocus={() => prefetchEstate(estate.estateId)}
    >
      {cols.map((c) => {
        if (c === 'address') {
          return <AddressColumn key={c} estate={estate} />
        }
        if (c === 'brokerTasks') {
          return <BrokerTasksColumn key={c} estate={estate} />
        }
        if (c === 'sellerTasks') {
          return <SellerTasksColumn key={c} estate={estate} />
        }
        if (c === 'publish') {
          return (
            <PublishColumn key={c} estate={estate} currentTab={currentTab} />
          )
        }
        if (c === 'viewing') {
          return <ViewingColumn key={c} estate={estate} />
        }
        if (c === 'brokers') {
          return <BrokersColumn key={c} estate={estate} />
        }
        return null
      })}
    </TableRow>
  )
}

export const MemoRow = React.memo(Row)
