'use client'

import type { Session } from 'next-auth'

import { cn } from '@nordvik/theme/cn'

import Filters from './filters'
import LeaderBoard from './filters/leader-board/leader-board'
import { HallOfFame } from './hall-of-fame/hall-of-fame'
import ListingAgreementsDashboard from './listing-agreements-dashboard'
import SectionTabs, { useTopListTab } from './section-tabs'

export function ToplistClient({
  user,
  hasHallOfFameFlag,
}: {
  user?: Session['user']
  hasHallOfFameFlag: boolean
}) {
  const [type] = useTopListTab()

  if (type === 'hall-of-fame' && hasHallOfFameFlag) {
    return (
      <Header>
        <HallOfFame />
      </Header>
    )
  }

  if (type === 'listing-agreements') {
    return (
      <>
        <Header />
        <ListingAgreementsDashboard />
      </>
    )
  }

  return (
    <LeaderBoard user={user}>
      <Header>
        <Filters departmentId={user?.department?.departmentId} />
      </Header>
    </LeaderBoard>
  )
}

function Header({ children }: { children?: React.ReactNode }) {
  return (
    <div className="relative flex flex-col">
      <div className={cn('bg-root')} data-theme="dark">
        <div className="container relative flex flex-col gap-4 pt-4 lg:gap-8 lg:pb-8 lg:pt-5">
          <h1 className="typo-display-lg">Toppliste</h1>

          <SectionTabs />
          {children}
        </div>
      </div>
    </div>
  )
}
