'use client'

import {
  AwardIcon,
  BookOpenIcon,
  Contact2Icon,
  LayoutDashboard,
  NewspaperIcon,
  StarIcon,
  TargetIcon,
} from 'lucide-react'
import type { Session } from 'next-auth'
import { usePathname } from 'next/navigation'
import React from 'react'

import { cn } from '@nordvik/theme/cn'

import { useUserContext } from '@/lib/UserContext'
import { useFeatureFlags } from '@/lib/analytics/feature-flag'
import { useNativeBridge } from '@/lib/use-native-bridge'
import userHasAdminRole from '@/lib/userHasAdminRole'

import { ChangelogCard } from './changelog-card/changelog-card'
import NavLinks, { type NavLink } from './nav-links'

function NavMenuContent({ user }: { user?: Session['user'] }) {
  const { notifications } = useUserContext()
  const { isWebView } = useNativeBridge()
  const pathname = usePathname()
  const featureFlags = useFeatureFlags()

  const links: NavLink[] = [
    {
      title: 'Dashboard',
      icon: LayoutDashboard,
      href: '/dashboard',
      active: pathname.startsWith('/dashboard'),
    },
    {
      title: 'Toppliste',
      icon: AwardIcon,
      href: '/toppliste',
      active: pathname.startsWith('/toppliste'),
    },
    {
      title: 'Nyheter',
      icon: NewspaperIcon,
      href: '/nyheter',
      notificationCount: notifications.news,
      active: pathname.startsWith('/nyheter'),
    },
    {
      title: 'Mine oppdrag',
      icon: Contact2Icon,
      href: '/oppdrag/status/innsalg',
      active:
        pathname.startsWith('/oppdrag') && !pathname.startsWith('/oppdrag/ny'),
    },
    {
      title: 'Kundetilfredshet',
      icon: StarIcon,
      href: '/kundetilfredshet',
      active: pathname.startsWith('/kundetilfredshet'),
    },
    {
      title: 'Nordvik Ekstra',
      href: '/api/adplenty/login',
      icon: TargetIcon,
      external: true,
    },
    {
      title: 'Hjelpesenter',
      icon: BookOpenIcon,
      href: '/hjelpesenter',
      active: pathname.startsWith('/hjelpesenter'),
    },
  ]

  if (featureFlags['assignments-overview-v2']) {
    links.splice(3, 0, {
      title: 'Oppdrag (ny)',
      icon: Contact2Icon,
      href: '/oppdrag/ny',
      active: pathname.startsWith('/oppdrag/ny'),
    })
  }

  const footerLinks: { top: NavLink[]; bottom: NavLink[] } = {
    top: [
      {
        title: 'Ledige stillinger',
        href: '/ledige-stillinger',
        active: pathname.startsWith('/ledige-stillinger'),
      },
      {
        title: 'Driftsmeldinger',
        href: '/driftsmeldinger',
        active: pathname.startsWith('/driftsmeldinger'),
      },

      {
        title: 'Nordvik Ads',
        href: '/api/adplenty/login?platform=saas',
        external: true,
      },
    ],
    bottom: [],
  }

  footerLinks.top.unshift({
    title: 'Oppdateringer',
    href: '/oppdateringer',
    active: pathname.startsWith('/oppdateringer'),
  })

  if (userHasAdminRole(user)) {
    footerLinks.bottom.unshift({
      title: 'Admin',
      href: '/admin',
      active: pathname.startsWith('/admin'),
    })
  }

  if (isWebView && process.env.NEXT_PUBLIC_NORDVIK_APP_URL) {
    footerLinks.bottom.push({
      title: 'Gå til kunde app',
      href:
        process.env.NEXT_PUBLIC_NORDVIK_APP_URL + '/customer/home/<USER>',
      active: false,
      external: false,
    })
  }

  return (
    <div className="mb-4 flex grow flex-col">
      <NavLinks className="mb-auto" links={links} />

      <ChangelogCard fallback={<Separator />} />
      <NavLinks size="sm" links={footerLinks.top} />

      {footerLinks.bottom.length > 0 && (
        <>
          <Separator />
          <NavLinks size="sm" links={footerLinks.bottom} />
        </>
      )}
    </div>
  )
}

function Separator({ className }: { className?: string }) {
  return (
    <div
      className={cn('my-3 mx-3 shrink-0 h-[1px] bg-stroke-muted', className)}
    />
  )
}

export default NavMenuContent
