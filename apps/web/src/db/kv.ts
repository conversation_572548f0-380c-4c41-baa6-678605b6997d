import { createClient } from '@vercel/kv'
import 'server-only'

const cache = createClient({
  url: process.env.REDIS_REST_API_URL!,
  token: process.env.REDIS_REST_API_TOKEN!,
})

export async function get<T>(key: string) {
  return cache.get<T>(key)
}

export async function set<T>(key: string, data: T, ttl: number = 60 * 30) {
  if (data === null || data === undefined) {
    return cache.del(key)
  }

  return cache.set(key, data, {
    ex: ttl,
  })
}

/**
 * Returns remaining TTL in seconds for the given key.
 * - null when key has no expiry, does not exist, or TTL is unavailable.
 */
export function ttl(key: string) {
  return cache.ttl(key)
}

export default cache
