import { useQuery, useInfiniteQuery, useMutation, UseQueryOptions, UseInfiniteQueryOptions, InfiniteData, UseMutationOptions } from '@tanstack/react-query';
import { fetchData } from './graphql';
export type Maybe<T> = T | undefined;
export type InputMaybe<T> = T | undefined | null;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  Date: { input: string; output: string; }
  DateTime: { input: string; output: string; }
  JSON: { input: any; output: any; }
  NodeID: { input: any; output: any; }
};

export type GQLAccessToken = {
  __typename?: 'AccessToken';
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['NodeID']['output'];
  token?: Maybe<Scalars['String']['output']>;
};

export type GQLAccordion = {
  __typename?: 'Accordion';
  header?: Maybe<Scalars['String']['output']>;
  text?: Maybe<Scalars['String']['output']>;
};

export type GQLActivitySummary = {
  __typename?: 'ActivitySummary';
  recipientsCount: Scalars['Int']['output'];
  signedCount: Scalars['Int']['output'];
  signersCount: Scalars['Int']['output'];
  totalTimeSpent: Scalars['Float']['output'];
  visitorCount: Scalars['Int']['output'];
  visitsCount: Scalars['Int']['output'];
};

export type GQLAreaSize = {
  __typename?: 'AreaSize';
  BRAItotal?: Maybe<Scalars['Float']['output']>;
};

export enum GQLAssignmentDocumentState {
  Complete = 'COMPLETE',
  Error = 'ERROR',
  InProgress = 'IN_PROGRESS',
  Missing = 'MISSING',
  Pending = 'PENDING'
}

export type GQLAssignmentDocumentStatusItem = {
  __typename?: 'AssignmentDocumentStatusItem';
  message?: Maybe<Scalars['String']['output']>;
  state: GQLAssignmentDocumentState;
  type: GQLAssignmentDocumentType;
  updatedAt: Scalars['DateTime']['output'];
};

export type GQLAssignmentDocumentStatuses = {
  __typename?: 'AssignmentDocumentStatuses';
  estateId: Scalars['String']['output'];
  generatedAt: Scalars['DateTime']['output'];
  items: Array<GQLAssignmentDocumentStatusItem>;
  partialFailure: Scalars['Boolean']['output'];
};

export enum GQLAssignmentDocumentType {
  EnergyCertificate = 'ENERGY_CERTIFICATE',
  ListingAgreement = 'LISTING_AGREEMENT',
  Photos = 'PHOTOS',
  SafetyDeclaration = 'SAFETY_DECLARATION',
  SellerInterview = 'SELLER_INTERVIEW',
  SurveyReport = 'SURVEY_REPORT'
}

export type GQLAuditExtraData = {
  __typename?: 'AuditExtraData';
  recipients?: Maybe<Array<GQLAuditRecipient>>;
  template?: Maybe<GQLAuditTemplate>;
};

export type GQLAuditRecipient = {
  __typename?: 'AuditRecipient';
  contactId?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  mobilePhone?: Maybe<Scalars['String']['output']>;
};

export type GQLAuditTemplate = {
  __typename?: 'AuditTemplate';
  modified?: Maybe<Scalars['Boolean']['output']>;
  templateId?: Maybe<Scalars['String']['output']>;
};

export type GQLAverageCommissionEntry = {
  __typename?: 'AverageCommissionEntry';
  commission?: Maybe<Scalars['Float']['output']>;
  price: Scalars['Float']['output'];
  salesCount?: Maybe<Scalars['Float']['output']>;
  type: Scalars['String']['output'];
};

export type GQLAverageCommissionResponse = {
  __typename?: 'AverageCommissionResponse';
  compare: GQLAverageCommissionEntry;
  departmentName: Scalars['String']['output'];
  reference: GQLAverageCommissionEntry;
};

export type GQLAward = {
  __typename?: 'Award';
  id?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  origin?: Maybe<Scalars['String']['output']>;
  year?: Maybe<Scalars['Int']['output']>;
};

export type GQLBroker = {
  __typename?: 'Broker';
  aboutMe?: Maybe<Scalars['String']['output']>;
  awards?: Maybe<Array<GQLAward>>;
  createdDate?: Maybe<Scalars['DateTime']['output']>;
  department?: Maybe<GQLDepartment>;
  email: Scalars['String']['output'];
  employeeActive?: Maybe<Scalars['Boolean']['output']>;
  employeeId: Scalars['String']['output'];
  employeeRoles?: Maybe<Array<Maybe<GQLBrokerRole>>>;
  id: Scalars['NodeID']['output'];
  image?: Maybe<GQLBrokerImage>;
  instagram?: Maybe<Scalars['String']['output']>;
  kti?: Maybe<Scalars['Float']['output']>;
  links?: Maybe<GQLBrokerProfileLinks>;
  mobilePhone?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  nordvikAwards?: Maybe<Array<GQLNordvikAward>>;
  rating?: Maybe<GQLRating>;
  ratings?: Maybe<GQLBrokerRatingsList>;
  reviews: Array<GQLBrokerRating>;
  slug?: Maybe<Scalars['String']['output']>;
  team?: Maybe<Array<GQLBrokerPartner>>;
  title?: Maybe<Scalars['String']['output']>;
  usp?: Maybe<Array<GQLUsp>>;
};


export type GQLBrokerReviewsArgs = {
  input: GQLBrokerReviewsInput;
};

export type GQLBrokerAddress = {
  __typename?: 'BrokerAddress';
  apartmentNumber?: Maybe<Scalars['String']['output']>;
  city?: Maybe<Scalars['String']['output']>;
  municipality?: Maybe<Scalars['String']['output']>;
  streetAddress?: Maybe<Scalars['String']['output']>;
  zipCode?: Maybe<Scalars['String']['output']>;
};

export type GQLBrokerEstate = {
  __typename?: 'BrokerEstate';
  activities: Array<GQLEstateActivity>;
  address?: Maybe<GQLBrokerAddress>;
  ads: Array<GQLEstateAd>;
  areaSize?: Maybe<GQLAreaSize>;
  assignmentNumber?: Maybe<Scalars['String']['output']>;
  assignmentType?: Maybe<Scalars['Int']['output']>;
  assignmentTypeGroup?: Maybe<Scalars['Int']['output']>;
  assistantBroker?: Maybe<GQLBroker>;
  broker?: Maybe<GQLBroker>;
  brokerId?: Maybe<Scalars['String']['output']>;
  brokers?: Maybe<Array<Maybe<GQLEstateBroker>>>;
  brokersIdWithRoles?: Maybe<Array<Maybe<GQLBrokerIdWithRole>>>;
  businessManagerContact?: Maybe<GQLBusinessManagerContact>;
  campaigns: Array<GQLBrokerEstateCampaign>;
  changedDate?: Maybe<Scalars['DateTime']['output']>;
  checklist: Array<GQLEstateChecklistItem>;
  commissionAcceptedDate?: Maybe<Scalars['DateTime']['output']>;
  companyContacts: Array<GQLContact>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  department?: Maybe<GQLDepartment>;
  departmentId?: Maybe<Scalars['Int']['output']>;
  documents: Array<GQLNextDocument>;
  estateId: Scalars['String']['output'];
  estatePrice?: Maybe<GQLEstatePrice>;
  estatePriceModel?: Maybe<GQLEstatePriceModel>;
  estateSizeModel?: Maybe<GQLEstateSizeModel>;
  estateType?: Maybe<Scalars['String']['output']>;
  estateTypeExternal?: Maybe<Scalars['Int']['output']>;
  estateTypeId?: Maybe<Scalars['String']['output']>;
  etakst?: Maybe<GQLNextDocument>;
  expireDate?: Maybe<Scalars['DateTime']['output']>;
  extraContacts: Array<GQLContact>;
  finn?: Maybe<GQLFinnData>;
  forms: Array<GQLBrokerEstateForm>;
  hasCompanySeller?: Maybe<Scalars['Boolean']['output']>;
  hasInspection?: Maybe<Scalars['Boolean']['output']>;
  heading?: Maybe<Scalars['String']['output']>;
  hjemUrl?: Maybe<Scalars['String']['output']>;
  id: Scalars['NodeID']['output'];
  images?: Maybe<Array<GQLBrokerEstateImage>>;
  inspection?: Maybe<GQLInspection>;
  inspectionDate?: Maybe<Scalars['DateTime']['output']>;
  inspectionEvents?: Maybe<Array<GQLInspectionEvent>>;
  inspectionFolder?: Maybe<GQLInspectionFolder>;
  isEtakstPublished?: Maybe<Scalars['Boolean']['output']>;
  isValuation?: Maybe<Scalars['Boolean']['output']>;
  isWithdrawn: Scalars['Boolean']['output'];
  landIdentificationMatrix?: Maybe<GQLLandIdentificationMatrix>;
  latitude?: Maybe<Scalars['Float']['output']>;
  linkToNext?: Maybe<Scalars['String']['output']>;
  links: Array<GQLBrokerEstateLink>;
  listingAgreement?: Maybe<GQLListingAgreement>;
  location?: Maybe<GQLLocation>;
  longitude?: Maybe<Scalars['Float']['output']>;
  mainBroker?: Maybe<GQLBroker>;
  mainImage?: Maybe<GQLBrokerEstateImage>;
  mainSeller?: Maybe<GQLBrokerEstateSeller>;
  marketingStart?: Maybe<GQLMarketingStart>;
  matrikkel: Array<Maybe<GQLLandIdentificationMatrix>>;
  noOfBedRooms?: Maybe<Scalars['Int']['output']>;
  noOfRooms?: Maybe<Scalars['Int']['output']>;
  numberOfBedrooms?: Maybe<Scalars['Int']['output']>;
  ownAssignmentType?: Maybe<Scalars['String']['output']>;
  ownership?: Maybe<Scalars['Int']['output']>;
  ownershipType?: Maybe<Scalars['String']['output']>;
  partOwnership?: Maybe<GQLPartOwnership>;
  placeholderImage?: Maybe<Scalars['String']['output']>;
  priceHistory?: Maybe<Array<Maybe<GQLBrokerEstatePriceHistory>>>;
  propertyType?: Maybe<Scalars['String']['output']>;
  riskCheckmark?: Maybe<Scalars['Boolean']['output']>;
  sellPreference?: Maybe<Scalars['String']['output']>;
  sellers: Array<GQLBrokerEstateSeller>;
  showings: Array<GQLBrokerEstateShowing>;
  soldDate?: Maybe<Scalars['DateTime']['output']>;
  stats?: Maybe<GQLEstateStats>;
  status: Scalars['Int']['output'];
  sumArea?: Maybe<GQLSumArea>;
  takeOverDate?: Maybe<Scalars['DateTime']['output']>;
  type?: Maybe<Scalars['String']['output']>;
  upcomingEvents: Array<GQLEstateActivity>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
  userID?: Maybe<Scalars['String']['output']>;
};


export type GQLBrokerEstateExtraContactsArgs = {
  source?: InputMaybe<GQLSource>;
};

export type GQLBrokerEstateCampaign = {
  __typename?: 'BrokerEstateCampaign';
  dateOrdered?: Maybe<Scalars['DateTime']['output']>;
  externalId?: Maybe<Scalars['String']['output']>;
  marketingPackage?: Maybe<Scalars['String']['output']>;
  orderEndDate?: Maybe<Scalars['DateTime']['output']>;
  orderStartDate?: Maybe<Scalars['DateTime']['output']>;
  packageName?: Maybe<Scalars['String']['output']>;
};

export type GQLBrokerEstateCountResponse = {
  __typename?: 'BrokerEstateCountResponse';
  count: Scalars['Int']['output'];
  tab: GQLEstateTabFilter;
};

export type GQLBrokerEstateForm = {
  __typename?: 'BrokerEstateForm';
  link?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  relevantForEstateWithProps?: Maybe<GQLRelevantForEstateWithProps>;
  status?: Maybe<GQLBrokerEstateFormStatus>;
  type?: Maybe<Scalars['String']['output']>;
};

export type GQLBrokerEstateFormStatus = {
  __typename?: 'BrokerEstateFormStatus';
  isNotificationSent?: Maybe<Scalars['Boolean']['output']>;
  signingFinished?: Maybe<Scalars['Boolean']['output']>;
};

export type GQLBrokerEstateImage = {
  __typename?: 'BrokerEstateImage';
  category?: Maybe<Scalars['String']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  large?: Maybe<Scalars['String']['output']>;
  medium?: Maybe<Scalars['String']['output']>;
  sequence?: Maybe<Scalars['Int']['output']>;
  small?: Maybe<Scalars['String']['output']>;
};

export type GQLBrokerEstateLink = {
  __typename?: 'BrokerEstateLink';
  linkType?: Maybe<Scalars['Int']['output']>;
  text?: Maybe<Scalars['String']['output']>;
  url?: Maybe<Scalars['String']['output']>;
};

export type GQLBrokerEstatePagination = {
  __typename?: 'BrokerEstatePagination';
  count?: Maybe<Scalars['Int']['output']>;
  limit?: Maybe<Scalars['Int']['output']>;
  offset?: Maybe<Scalars['Int']['output']>;
  total?: Maybe<Scalars['Int']['output']>;
};

export type GQLBrokerEstatePriceHistory = {
  __typename?: 'BrokerEstatePriceHistory';
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  evPrice?: Maybe<Scalars['Int']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  landIdentificationMatrix?: Maybe<GQLLandIdentificationMatrix>;
  postgresEstateId?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type GQLBrokerEstateSeller = {
  __typename?: 'BrokerEstateSeller';
  address?: Maybe<Scalars['String']['output']>;
  city?: Maybe<Scalars['String']['output']>;
  companyName?: Maybe<Scalars['String']['output']>;
  contactId: Scalars['String']['output'];
  contactType?: Maybe<Scalars['Int']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  mainContact?: Maybe<Scalars['Boolean']['output']>;
  mobilePhone?: Maybe<Scalars['String']['output']>;
  postCode?: Maybe<Scalars['String']['output']>;
  proxy?: Maybe<GQLSellerProxy>;
  proxyId?: Maybe<Scalars['String']['output']>;
  socialSecurityNumber?: Maybe<Scalars['String']['output']>;
};

export type GQLBrokerEstateShowing = {
  __typename?: 'BrokerEstateShowing';
  end?: Maybe<Scalars['DateTime']['output']>;
  showingId?: Maybe<Scalars['String']['output']>;
  start?: Maybe<Scalars['DateTime']['output']>;
};

export type GQLBrokerEstatesResponse = {
  __typename?: 'BrokerEstatesResponse';
  items: Array<GQLBrokerEstate>;
  pagination?: Maybe<GQLBrokerEstatePagination>;
};

export type GQLBrokerIdWithRole = {
  __typename?: 'BrokerIdWithRole';
  brokerRole: Scalars['Int']['output'];
  employee: GQLBrokerIdWithRoleDetails;
  employeeId: Scalars['String']['output'];
};

export type GQLBrokerIdWithRoleDetails = {
  __typename?: 'BrokerIdWithRoleDetails';
  email?: Maybe<Scalars['String']['output']>;
  image?: Maybe<GQLBrokerImage>;
  mobilePhone?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  title?: Maybe<Scalars['String']['output']>;
};

export type GQLBrokerImage = {
  __typename?: 'BrokerImage';
  large?: Maybe<Scalars['String']['output']>;
  medium?: Maybe<Scalars['String']['output']>;
  small?: Maybe<Scalars['String']['output']>;
};

export type GQLBrokerKtiResponse = {
  __typename?: 'BrokerKtiResponse';
  broker?: Maybe<GQLKtiResponse>;
  brokerDepartment?: Maybe<GQLKtiResponse>;
  nordvik?: Maybe<GQLKtiResponse>;
};

export type GQLBrokerPartner = {
  __typename?: 'BrokerPartner';
  category?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  hidden?: Maybe<Scalars['Boolean']['output']>;
  id: Scalars['String']['output'];
  images: Array<Scalars['String']['output']>;
  instagram?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  profilePicture?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
  website?: Maybe<Scalars['String']['output']>;
};

export type GQLBrokerPartnerCreateInput = {
  category?: InputMaybe<Scalars['String']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  images?: InputMaybe<Array<Scalars['String']['input']>>;
  instagram?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
  profilePicture?: InputMaybe<Scalars['String']['input']>;
  website?: InputMaybe<Scalars['String']['input']>;
};

export type GQLBrokerPartnerUpdateInput = {
  category?: InputMaybe<Scalars['String']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  images?: InputMaybe<Array<Scalars['String']['input']>>;
  instagram?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
  profilePicture?: InputMaybe<Scalars['String']['input']>;
  website?: InputMaybe<Scalars['String']['input']>;
};

export type GQLBrokerProfileLinks = {
  __typename?: 'BrokerProfileLinks';
  adLinks: Array<Scalars['String']['output']>;
  mediaLinks: Array<Scalars['String']['output']>;
};

export type GQLBrokerProfileLinksPayload = {
  adLinks: Array<Scalars['String']['input']>;
  mediaLinks: Array<Scalars['String']['input']>;
};

export type GQLBrokerRating = {
  __typename?: 'BrokerRating';
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  employeeId?: Maybe<Scalars['String']['output']>;
  featured?: Maybe<Scalars['Boolean']['output']>;
  rating?: Maybe<Scalars['Float']['output']>;
  ratingId?: Maybe<Scalars['Int']['output']>;
  review?: Maybe<GQLBrokerReview>;
  userName?: Maybe<Scalars['String']['output']>;
};

export enum GQLBrokerRatingSortBy {
  CreatedDate = 'CREATED_DATE',
  Rating = 'RATING'
}

export type GQLBrokerRatingsList = {
  __typename?: 'BrokerRatingsList';
  data: Array<Maybe<GQLBrokerRating>>;
  pagination: GQLPagination;
};

export type GQLBrokerReview = {
  __typename?: 'BrokerReview';
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  reviewId?: Maybe<Scalars['Int']['output']>;
  text?: Maybe<Scalars['String']['output']>;
};

export type GQLBrokerReviewsInput = {
  featured?: InputMaybe<Scalars['Boolean']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  rating?: InputMaybe<Scalars['Int']['input']>;
};

export type GQLBrokerRole = {
  __typename?: 'BrokerRole';
  name?: Maybe<Scalars['String']['output']>;
  source?: Maybe<Scalars['String']['output']>;
  typeId?: Maybe<Scalars['Int']['output']>;
};

export type GQLBrokersResponse = {
  __typename?: 'BrokersResponse';
  items: Array<GQLBroker>;
  totalCount: Scalars['Int']['output'];
};

export type GQLBudgetPost = {
  __typename?: 'BudgetPost';
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  deletedAt?: Maybe<Scalars['DateTime']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  id: Scalars['NodeID']['output'];
  initialPrice?: Maybe<Scalars['Float']['output']>;
  listingAgreementId?: Maybe<Scalars['String']['output']>;
  price?: Maybe<Scalars['Float']['output']>;
  title?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type GQLBudgetPostDescription = {
  __typename?: 'BudgetPostDescription';
  description?: Maybe<Scalars['String']['output']>;
  heading: Scalars['String']['output'];
};

export type GQLBusinessManagerContact = {
  __typename?: 'BusinessManagerContact';
  address?: Maybe<Scalars['String']['output']>;
  city?: Maybe<Scalars['String']['output']>;
  companyName?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
};

export type GQLCmsArticle = {
  __typename?: 'CmsArticle';
  author?: Maybe<GQLCmsArticleAuthor>;
  canUserViewArticle?: Maybe<Scalars['Boolean']['output']>;
  categories: Array<GQLCmsArticleCategory>;
  departments?: Maybe<Array<GQLCmsArticleDepartment>>;
  eventDate?: Maybe<Scalars['Boolean']['output']>;
  excerpt?: Maybe<Scalars['String']['output']>;
  externalUrl?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  image?: Maybe<GQLCmsArticleImage>;
  important?: Maybe<Scalars['Boolean']['output']>;
  modules?: Maybe<Array<Maybe<GQLModule>>>;
  postDate?: Maybe<Scalars['String']['output']>;
  slug: Scalars['String']['output'];
  targetRoles: Array<GQLTargetRole>;
  title?: Maybe<Scalars['String']['output']>;
  type?: Maybe<GQLCmsArticleType>;
  viewerHasRead?: Maybe<Scalars['Boolean']['output']>;
};


export type GQLCmsArticleViewerHasReadArgs = {
  userId?: InputMaybe<Scalars['String']['input']>;
};

export type GQLCmsArticleAuthor = {
  __typename?: 'CmsArticleAuthor';
  avatarUrl?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type GQLCmsArticleCategory = {
  __typename?: 'CmsArticleCategory';
  id: Scalars['String']['output'];
  slug: Scalars['String']['output'];
  title: Scalars['String']['output'];
};

export type GQLCmsArticleDepartment = {
  __typename?: 'CmsArticleDepartment';
  departmentId?: Maybe<Scalars['Int']['output']>;
  title?: Maybe<Scalars['String']['output']>;
};

export type GQLCmsArticleImage = {
  __typename?: 'CmsArticleImage';
  large?: Maybe<Scalars['String']['output']>;
  medium?: Maybe<Scalars['String']['output']>;
  small?: Maybe<Scalars['String']['output']>;
};

export type GQLCmsArticleResponse = {
  __typename?: 'CmsArticleResponse';
  items: Array<GQLCmsArticle>;
  meta: GQLCmsMeta;
};

export enum GQLCmsArticleType {
  Calendar = 'calendar',
  Changelog = 'changelog',
  News = 'news',
  Resource = 'resource',
  Updates = 'updates'
}

export type GQLCmsIncidentsResponse = {
  __typename?: 'CmsIncidentsResponse';
  items: Array<GQLCraftCmsIncident>;
  meta: GQLCmsMeta;
};

export type GQLCmsMeta = {
  __typename?: 'CmsMeta';
  currentPage: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
  totalPages: Scalars['Int']['output'];
};

export type GQLContact = {
  __typename?: 'Contact';
  address?: Maybe<Scalars['String']['output']>;
  city?: Maybe<Scalars['String']['output']>;
  companyName?: Maybe<Scalars['String']['output']>;
  contactId: Scalars['String']['output'];
  contactType: Scalars['Int']['output'];
  deletedAt?: Maybe<Scalars['DateTime']['output']>;
  departmentId?: Maybe<Scalars['Int']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  mobilePhone?: Maybe<Scalars['String']['output']>;
  organisationNumber?: Maybe<Scalars['String']['output']>;
  postalAddress?: Maybe<Scalars['String']['output']>;
  postalCode?: Maybe<Scalars['String']['output']>;
  privatePhone?: Maybe<Scalars['String']['output']>;
  relationName?: Maybe<Scalars['String']['output']>;
  relationType?: Maybe<Scalars['Int']['output']>;
  roleName?: Maybe<Scalars['String']['output']>;
  source?: Maybe<GQLSource>;
  workPhone?: Maybe<Scalars['String']['output']>;
};

export type GQLCraftCmsIncident = {
  __typename?: 'CraftCmsIncident';
  active: Scalars['Boolean']['output'];
  id: Scalars['String']['output'];
  level: GQLCraftCmsIncidentLevel;
  postDate: Scalars['String']['output'];
  resolved: Scalars['Boolean']['output'];
  resolvedComment?: Maybe<Scalars['String']['output']>;
  resolvedDate?: Maybe<Scalars['String']['output']>;
  slug: Scalars['String']['output'];
  title: Scalars['String']['output'];
  updates?: Maybe<Array<GQLCraftCmsIncidentUpdate>>;
};

export enum GQLCraftCmsIncidentLevel {
  Critical = 'critical',
  Error = 'error',
  Serious = 'serious'
}

export type GQLCraftCmsIncidentUpdate = {
  __typename?: 'CraftCmsIncidentUpdate';
  text: Scalars['String']['output'];
  time: Scalars['String']['output'];
};

export type GQLCraftCmsJob = {
  __typename?: 'CraftCmsJob';
  author?: Maybe<GQLCmsArticleAuthor>;
  excerpt?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  image?: Maybe<GQLCmsArticleImage>;
  modules?: Maybe<Array<Maybe<GQLModule>>>;
  postDate: Scalars['String']['output'];
  slug: Scalars['String']['output'];
  title: Scalars['String']['output'];
};

export type GQLCraftCmsJobListingItem = {
  __typename?: 'CraftCmsJobListingItem';
  author?: Maybe<GQLCmsArticleAuthor>;
  excerpt?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  image?: Maybe<GQLCmsArticleImage>;
  postDate: Scalars['String']['output'];
  slug: Scalars['String']['output'];
  title: Scalars['String']['output'];
};

export type GQLCraftCmsJobsListingResponse = {
  __typename?: 'CraftCmsJobsListingResponse';
  items: Array<GQLCraftCmsJobListingItem>;
  meta: GQLCmsMeta;
};

export type GQLCreateAward = {
  name?: InputMaybe<Scalars['String']['input']>;
  origin?: InputMaybe<Scalars['String']['input']>;
  year?: InputMaybe<Scalars['Int']['input']>;
};

export type GQLCreatePageVisitInput = {
  browser?: InputMaybe<Scalars['String']['input']>;
  contactId?: InputMaybe<Scalars['String']['input']>;
  employeeId?: InputMaybe<Scalars['String']['input']>;
  endTime?: InputMaybe<Scalars['DateTime']['input']>;
  estateId: Scalars['String']['input'];
  lastHeartbeat: Scalars['DateTime']['input'];
  location?: InputMaybe<Scalars['String']['input']>;
  pageId: Scalars['String']['input'];
  source?: InputMaybe<Scalars['String']['input']>;
  startTime: Scalars['DateTime']['input'];
};

export type GQLCurrentBrokerRatingsTotal = {
  __typename?: 'CurrentBrokerRatingsTotal';
  allDates?: Maybe<Scalars['Int']['output']>;
  allRatings?: Maybe<Scalars['Int']['output']>;
  currentYear?: Maybe<Scalars['Int']['output']>;
  fiveStar?: Maybe<Scalars['Int']['output']>;
  fourStar?: Maybe<Scalars['Int']['output']>;
  last30Days?: Maybe<Scalars['Int']['output']>;
  lastYear?: Maybe<Scalars['Int']['output']>;
  oneStar?: Maybe<Scalars['Int']['output']>;
  threeStar?: Maybe<Scalars['Int']['output']>;
  twoStar?: Maybe<Scalars['Int']['output']>;
  withReview?: Maybe<Scalars['Int']['output']>;
};

export type GQLDashboardActivity = {
  __typename?: 'DashboardActivity';
  done?: Maybe<Scalars['Boolean']['output']>;
  end?: Maybe<Scalars['DateTime']['output']>;
  estateAddress?: Maybe<Scalars['String']['output']>;
  estateId: Scalars['String']['output'];
  id?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  performedById?: Maybe<Scalars['String']['output']>;
  start?: Maybe<Scalars['DateTime']['output']>;
  type?: Maybe<Scalars['Int']['output']>;
  typeName?: Maybe<Scalars['String']['output']>;
  value?: Maybe<Scalars['String']['output']>;
};

export type GQLDashboardKtiCurrent = {
  __typename?: 'DashboardKtiCurrent';
  name: Scalars['String']['output'];
  ratings: Scalars['Int']['output'];
  value: Scalars['Float']['output'];
};

export type GQLDashboardKtiOthers = {
  __typename?: 'DashboardKtiOthers';
  name: Scalars['String']['output'];
  ratings: Scalars['Int']['output'];
  value: Scalars['Float']['output'];
};

export type GQLDashboardKtiResponse = {
  __typename?: 'DashboardKtiResponse';
  current: GQLDashboardKtiCurrent;
  others: Array<GQLDashboardKtiOthers>;
};

export type GQLDashboardLeadsResponse = {
  __typename?: 'DashboardLeadsResponse';
  budget: Scalars['Float']['output'];
  entries: Array<GQLLeadsEntry>;
};

export type GQLDashboardStatusEntry = {
  __typename?: 'DashboardStatusEntry';
  count: Scalars['Int']['output'];
  status: Scalars['Int']['output'];
  value: Scalars['Float']['output'];
};

export type GQLDashboardToplistResponse = {
  __typename?: 'DashboardToplistResponse';
  current: GQLToplistEntryCurrent;
  section: GQLToplistSection;
  topEntries: Array<GQLToplistEntry>;
};

export enum GQLDashboardType {
  Department = 'department',
  Personal = 'personal'
}

export type GQLDepartment = {
  __typename?: 'Department';
  city?: Maybe<Scalars['String']['output']>;
  departmentId: Scalars['Int']['output'];
  departmentNumber?: Maybe<Scalars['Int']['output']>;
  displayKtiOnEmployee?: Maybe<Scalars['Boolean']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  employees?: Maybe<Array<Maybe<GQLDepartmentEmployee>>>;
  id: Scalars['NodeID']['output'];
  kti?: Maybe<Scalars['Float']['output']>;
  legalName?: Maybe<Scalars['String']['output']>;
  marketName?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  organisationNumber?: Maybe<Scalars['String']['output']>;
  phone?: Maybe<Scalars['String']['output']>;
  postalCode?: Maybe<Scalars['String']['output']>;
  rating?: Maybe<GQLDepartmentRating>;
  slug?: Maybe<Scalars['String']['output']>;
  streetAddress?: Maybe<Scalars['String']['output']>;
};

export type GQLDepartmentEmployee = {
  __typename?: 'DepartmentEmployee';
  email?: Maybe<Scalars['String']['output']>;
  employeeId?: Maybe<Scalars['String']['output']>;
  mobilePhone?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  slug?: Maybe<Scalars['String']['output']>;
  title?: Maybe<Scalars['String']['output']>;
};

export type GQLDepartmentRating = {
  __typename?: 'DepartmentRating';
  average?: Maybe<Scalars['Float']['output']>;
  count?: Maybe<Scalars['Int']['output']>;
  reviewsCount?: Maybe<Scalars['Int']['output']>;
  weighted?: Maybe<Scalars['Float']['output']>;
};

export type GQLDepartmentsResponse = {
  __typename?: 'DepartmentsResponse';
  items: Array<GQLDepartment>;
  totalCount: Scalars['Int']['output'];
};

export type GQLEmailAudit = {
  __typename?: 'EmailAudit';
  contextId?: Maybe<Scalars['String']['output']>;
  contextType?: Maybe<Scalars['String']['output']>;
  createdAt: Scalars['DateTime']['output'];
  fromEmail?: Maybe<Scalars['String']['output']>;
  globalMetadata?: Maybe<Scalars['JSON']['output']>;
  id: Scalars['ID']['output'];
  recipients: Array<GQLEmailAuditRecipient>;
  subject?: Maybe<Scalars['String']['output']>;
  templateName?: Maybe<Scalars['String']['output']>;
  updatedAt: Scalars['DateTime']['output'];
};

export type GQLEmailAuditRecipient = {
  __typename?: 'EmailAuditRecipient';
  bounceType?: Maybe<Scalars['String']['output']>;
  contactId?: Maybe<Scalars['String']['output']>;
  createdAt: Scalars['DateTime']['output'];
  emailAudit?: Maybe<GQLEmailAudit>;
  emailAuditId: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  lastEventAt?: Maybe<Scalars['DateTime']['output']>;
  messageId: Scalars['String']['output'];
  openCount: Scalars['Int']['output'];
  openedAt?: Maybe<Scalars['DateTime']['output']>;
  rawLastEvent?: Maybe<Scalars['JSON']['output']>;
  recipientEmail: Scalars['String']['output'];
  recipientMetadata?: Maybe<Scalars['JSON']['output']>;
  rejectReason?: Maybe<Scalars['String']['output']>;
  sentAt: Scalars['DateTime']['output'];
  status: Scalars['String']['output'];
  updatedAt: Scalars['DateTime']['output'];
};

export type GQLEmailInteraction = {
  __typename?: 'EmailInteraction';
  bounceType?: Maybe<Scalars['String']['output']>;
  contactId?: Maybe<Scalars['String']['output']>;
  emailAuditId: Scalars['String']['output'];
  eventTimestamp: Scalars['DateTime']['output'];
  eventType: Scalars['String']['output'];
  id: Scalars['String']['output'];
  messageId: Scalars['String']['output'];
  openCount?: Maybe<Scalars['Int']['output']>;
  recipientEmail: Scalars['String']['output'];
  rejectReason?: Maybe<Scalars['String']['output']>;
};

export type GQLEstateActivity = {
  __typename?: 'EstateActivity';
  done?: Maybe<Scalars['Boolean']['output']>;
  end?: Maybe<Scalars['DateTime']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  performedById?: Maybe<Scalars['String']['output']>;
  start?: Maybe<Scalars['DateTime']['output']>;
  type?: Maybe<Scalars['Int']['output']>;
  typeName?: Maybe<Scalars['String']['output']>;
  value?: Maybe<Scalars['String']['output']>;
};

export type GQLEstateAd = {
  __typename?: 'EstateAd';
  link?: Maybe<Scalars['String']['output']>;
  source?: Maybe<GQLEstateAdSource>;
};

export enum GQLEstateAdSource {
  Finn = 'FINN',
  Hjem = 'HJEM',
  Nordvikbolig = 'NORDVIKBOLIG'
}

export type GQLEstateBroker = {
  __typename?: 'EstateBroker';
  email?: Maybe<Scalars['String']['output']>;
  employeeId?: Maybe<Scalars['String']['output']>;
  employeeRoles?: Maybe<Array<Maybe<GQLBrokerRole>>>;
  image?: Maybe<GQLBrokerImage>;
  mobilePhone?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  role?: Maybe<Scalars['Int']['output']>;
  slug?: Maybe<Scalars['String']['output']>;
  title?: Maybe<Scalars['String']['output']>;
  workPhone?: Maybe<Scalars['String']['output']>;
};

export type GQLEstateChecklistItem = {
  __typename?: 'EstateChecklistItem';
  changedBy?: Maybe<Scalars['String']['output']>;
  changedDate?: Maybe<Scalars['DateTime']['output']>;
  firstTag?: Maybe<Scalars['String']['output']>;
  tags?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  value?: Maybe<Scalars['Int']['output']>;
};

export type GQLEstatePrice = {
  __typename?: 'EstatePrice';
  collectiveDebt?: Maybe<Scalars['Float']['output']>;
  priceSuggestion?: Maybe<Scalars['Float']['output']>;
  soldPrice?: Maybe<Scalars['Float']['output']>;
  totalPrice?: Maybe<Scalars['Float']['output']>;
};

export type GQLEstatePriceModel = {
  __typename?: 'EstatePriceModel';
  additionalAgreementOptions?: Maybe<Scalars['Float']['output']>;
  collectiveAssets?: Maybe<Scalars['Float']['output']>;
  collectiveDebt?: Maybe<Scalars['Float']['output']>;
  communityTax?: Maybe<Scalars['Float']['output']>;
  communityTaxYear?: Maybe<Scalars['Int']['output']>;
  estimatedValue?: Maybe<Scalars['Float']['output']>;
  leasingPartyTransportFee?: Maybe<Scalars['Float']['output']>;
  loanFare?: Maybe<Scalars['Float']['output']>;
  originalAgreementPrice?: Maybe<Scalars['Float']['output']>;
  originalExpensesPrice?: Maybe<Scalars['Float']['output']>;
  otherExpenses?: Maybe<Scalars['String']['output']>;
  priceSuggestion?: Maybe<Scalars['Float']['output']>;
  purchaseCostsAmount?: Maybe<Scalars['Float']['output']>;
  salesCostDescription?: Maybe<Scalars['String']['output']>;
  soldPrice?: Maybe<Scalars['Float']['output']>;
  totalPrice?: Maybe<Scalars['Float']['output']>;
  totalPriceExclusiveCostsAndDebt?: Maybe<Scalars['Float']['output']>;
  transportAgreementCosts?: Maybe<Scalars['Float']['output']>;
  waterRate?: Maybe<Scalars['Float']['output']>;
  waterRateDescription?: Maybe<Scalars['String']['output']>;
  waterRateYear?: Maybe<Scalars['Int']['output']>;
  yearlyLeaseFee?: Maybe<Scalars['Float']['output']>;
  yearlySocietyTax?: Maybe<Scalars['Float']['output']>;
};

export type GQLEstateProps = {
  projectRelation?: InputMaybe<Scalars['Int']['input']>;
  status?: InputMaybe<Scalars['Int']['input']>;
};

export type GQLEstateSizeModel = {
  __typename?: 'EstateSizeModel';
  grossArea?: Maybe<Scalars['Float']['output']>;
  primaryRoomArea?: Maybe<Scalars['Float']['output']>;
  primaryRoomAreaDescription?: Maybe<Scalars['String']['output']>;
  usableArea?: Maybe<Scalars['Float']['output']>;
};

export type GQLEstateStats = {
  __typename?: 'EstateStats';
  bidders?: Maybe<Scalars['Int']['output']>;
  bids?: Maybe<Scalars['Int']['output']>;
  followUp?: Maybe<Scalars['Int']['output']>;
  interested?: Maybe<Scalars['Int']['output']>;
  privateShowingsCount?: Maybe<Scalars['Int']['output']>;
  showingParticipants?: Maybe<Scalars['Int']['output']>;
  showingRegistrations?: Maybe<Scalars['Int']['output']>;
  showingRegistrationsTotal?: Maybe<Scalars['Int']['output']>;
  showings?: Maybe<Scalars['Int']['output']>;
  showingsTotal?: Maybe<Scalars['Int']['output']>;
};

export enum GQLEstateTabFilter {
  Archived = 'Archived',
  ForSale = 'ForSale',
  InPreparation = 'InPreparation',
  Requested = 'Requested',
  Sold = 'Sold',
  Valuation = 'Valuation'
}

export type GQLFindEstatesForBrokerFilters = {
  assignmentTypeGroup?: InputMaybe<Array<Scalars['Int']['input']>>;
  estateTypeId?: InputMaybe<Array<Scalars['Int']['input']>>;
  latitude?: InputMaybe<Scalars['Float']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  longitude?: InputMaybe<Scalars['Float']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  priceRange?: InputMaybe<GQLMinMax>;
  radius?: InputMaybe<Scalars['Int']['input']>;
  sizeRange?: InputMaybe<GQLMinMax>;
  sortBy?: InputMaybe<Scalars['String']['input']>;
  statuses?: InputMaybe<Array<Scalars['Int']['input']>>;
};

export type GQLFinnData = {
  __typename?: 'FinnData';
  finnCode?: Maybe<Scalars['String']['output']>;
  finnExpireDate?: Maybe<Scalars['DateTime']['output']>;
  finnPublishDate?: Maybe<Scalars['DateTime']['output']>;
};

export type GQLHallOfFameEntry = {
  __typename?: 'HallOfFameEntry';
  entries?: Maybe<Array<GQLHallOfFameEntryAward>>;
  year?: Maybe<Scalars['Int']['output']>;
};

export type GQLHallOfFameEntryAward = {
  __typename?: 'HallOfFameEntryAward';
  awardId?: Maybe<Scalars['Int']['output']>;
  employee?: Maybe<GQLBroker>;
  name?: Maybe<Scalars['String']['output']>;
  year?: Maybe<Scalars['Int']['output']>;
};

export type GQLImportantTask = {
  __typename?: 'ImportantTask';
  amlUrl?: Maybe<Scalars['String']['output']>;
  eiendomsverdiUrl?: Maybe<Scalars['String']['output']>;
  estateAddress: Scalars['String']['output'];
  estateId: Scalars['String']['output'];
  mainBrokerId: Scalars['String']['output'];
  signUrl?: Maybe<Scalars['String']['output']>;
  type: GQLImportantTaskType;
};

export enum GQLImportantTaskType {
  AmlCheckIncomplete = 'aml_check_incomplete',
  EtakstChecksIncomplete = 'etakst_checks_incomplete',
  SignListingAgreement = 'sign_listing_agreement'
}

export type GQLInspection = {
  __typename?: 'Inspection';
  entries: Array<GQLInspectionEntry>;
  metadata?: Maybe<GQLInspectionMetadata>;
  success?: Maybe<Scalars['Boolean']['output']>;
};

export type GQLInspectionActivity = {
  __typename?: 'InspectionActivity';
  contacts: Array<GQLSimpleContact>;
  description?: Maybe<Scalars['String']['output']>;
  employeeId?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  source?: Maybe<Scalars['String']['output']>;
  timestamp: Scalars['DateTime']['output'];
  type: Scalars['String']['output'];
  userAgent?: Maybe<GQLUserAgent>;
};

export type GQLInspectionEntry = {
  __typename?: 'InspectionEntry';
  id: Scalars['String']['output'];
  postDate?: Maybe<GQLPostDate>;
  title: Scalars['String']['output'];
  url: Scalars['String']['output'];
};

export type GQLInspectionEvent = {
  __typename?: 'InspectionEvent';
  description?: Maybe<Scalars['String']['output']>;
  end?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['String']['output'];
  start: Scalars['DateTime']['output'];
  title?: Maybe<Scalars['String']['output']>;
  type: Scalars['String']['output'];
};

export type GQLInspectionEventInput = {
  description?: InputMaybe<Scalars['String']['input']>;
  end?: InputMaybe<Scalars['String']['input']>;
  id?: InputMaybe<Scalars['String']['input']>;
  start: Scalars['String']['input'];
  title?: InputMaybe<Scalars['String']['input']>;
  type: Scalars['String']['input'];
};

export type GQLInspectionFolder = {
  __typename?: 'InspectionFolder';
  audit?: Maybe<Array<GQLInspectionFolderAudit>>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  createdBy?: Maybe<GQLBroker>;
  deletedAt?: Maybe<Scalars['DateTime']['output']>;
  estateId: Scalars['String']['output'];
  excludedEmployees: Array<Scalars['String']['output']>;
  excludedPartners: Array<GQLBrokerPartner>;
  id: Scalars['String']['output'];
  listingAgreement?: Maybe<GQLListingAgreement>;
  listingAgreementActive: Scalars['Boolean']['output'];
  listingAgreementSentAt?: Maybe<Scalars['DateTime']['output']>;
  notes?: Maybe<Scalars['String']['output']>;
  publishedAt?: Maybe<Scalars['DateTime']['output']>;
  publishedBy?: Maybe<GQLBroker>;
  recipients?: Maybe<Array<Scalars['String']['output']>>;
  relevantLinks: Array<Scalars['String']['output']>;
  sentAt?: Maybe<Scalars['DateTime']['output']>;
  sentBy?: Maybe<GQLBroker>;
  title?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
  updatedBy?: Maybe<GQLBroker>;
};

export type GQLInspectionFolderAudit = {
  __typename?: 'InspectionFolderAudit';
  channels: Array<Scalars['String']['output']>;
  emailAuditId?: Maybe<Scalars['String']['output']>;
  estateId?: Maybe<Scalars['String']['output']>;
  extraData?: Maybe<Scalars['JSON']['output']>;
  id: Scalars['Int']['output'];
  listingAgreementActive?: Maybe<Scalars['Boolean']['output']>;
  modifiedTemplate?: Maybe<Scalars['Boolean']['output']>;
  recipientContactIds: Array<Scalars['String']['output']>;
  recipients?: Maybe<Array<GQLAuditRecipient>>;
  sentAt?: Maybe<Scalars['DateTime']['output']>;
  sentBy?: Maybe<GQLBroker>;
  templateId?: Maybe<Scalars['String']['output']>;
};

export type GQLInspectionLead = {
  __typename?: 'InspectionLead';
  comment?: Maybe<Scalars['String']['output']>;
  contactId: Scalars['String']['output'];
  createdAt: Scalars['DateTime']['output'];
  createdByContactId?: Maybe<Scalars['String']['output']>;
  createdByEmployeeId?: Maybe<Scalars['String']['output']>;
  estateId: Scalars['String']['output'];
  id: Scalars['String']['output'];
  leadType: GQLInspectionLeadType;
  successful: Scalars['Boolean']['output'];
  updatedAt: Scalars['DateTime']['output'];
};

export enum GQLInspectionLeadType {
  Financing = 'FINANCING'
}

export type GQLInspectionMetadata = {
  __typename?: 'InspectionMetadata';
  count?: Maybe<Scalars['Int']['output']>;
};

export type GQLInteractionFilter = {
  employeeIds?: InputMaybe<Array<Scalars['String']['input']>>;
  estateIds?: InputMaybe<Array<Scalars['String']['input']>>;
  hasEventTypes?: InputMaybe<Array<GQLListingAgreementInteractionType>>;
  hasInteractionSince?: InputMaybe<Scalars['DateTime']['input']>;
  hasNotEventTypes?: InputMaybe<Array<GQLListingAgreementInteractionType>>;
  signed?: InputMaybe<Scalars['Boolean']['input']>;
};

export type GQLKeyFigureEntry = {
  __typename?: 'KeyFigureEntry';
  format?: Maybe<Scalars['String']['output']>;
  label: Scalars['String']['output'];
  lastValue?: Maybe<Scalars['Float']['output']>;
  type?: Maybe<GQLToplistSection>;
  value: Scalars['Float']['output'];
};

export type GQLKtiResponse = {
  __typename?: 'KtiResponse';
  averageRating?: Maybe<Scalars['Float']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  ratingCount?: Maybe<Scalars['String']['output']>;
  reviewCount?: Maybe<Scalars['Int']['output']>;
  reviews?: Maybe<Array<GQLReview>>;
};

export type GQLLandIdentificationMatrix = {
  __typename?: 'LandIdentificationMatrix';
  bnr?: Maybe<Scalars['Int']['output']>;
  fnr?: Maybe<Scalars['Int']['output']>;
  gnr?: Maybe<Scalars['Int']['output']>;
  knr?: Maybe<Scalars['Int']['output']>;
  ownPart?: Maybe<Scalars['String']['output']>;
  snr?: Maybe<Scalars['Int']['output']>;
};

export type GQLLeadsEntry = {
  __typename?: 'LeadsEntry';
  actual: Scalars['Float']['output'];
  budget: Scalars['Float']['output'];
  current: Scalars['Boolean']['output'];
  departmentId?: Maybe<Scalars['Int']['output']>;
  employeeCount: Scalars['Int']['output'];
  id?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  value: Scalars['Float']['output'];
};

export type GQLListingAgreement = {
  __typename?: 'ListingAgreement';
  accessTokens?: Maybe<Array<Maybe<GQLAccessToken>>>;
  brokerSigners?: Maybe<Array<GQLSigner>>;
  budgetPosts: Array<GQLBudgetPost>;
  canStartSigning?: Maybe<Scalars['Boolean']['output']>;
  commission?: Maybe<Scalars['Float']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  deadline?: Maybe<Scalars['DateTime']['output']>;
  deadlineHasBeenExceeded?: Maybe<Scalars['Boolean']['output']>;
  deletedAt?: Maybe<Scalars['DateTime']['output']>;
  feePercentage?: Maybe<Scalars['Float']['output']>;
  generalTerms?: Maybe<Scalars['String']['output']>;
  hasStorebrandLead: Scalars['Boolean']['output'];
  id: Scalars['NodeID']['output'];
  initiatedSigningAt?: Maybe<Scalars['DateTime']['output']>;
  interactions: Array<GQLListingAgreementInteraction>;
  marketingPackage?: Maybe<Scalars['String']['output']>;
  offerSellerLink?: Maybe<Scalars['String']['output']>;
  sellerInsurance?: Maybe<Scalars['Boolean']['output']>;
  sellerSigners?: Maybe<Array<GQLSigner>>;
  sentToClientAt?: Maybe<Scalars['DateTime']['output']>;
  signedAt?: Maybe<Scalars['DateTime']['output']>;
  signers: Array<GQLSigner>;
  signicatDocument?: Maybe<GQLSignicatDocument>;
  signicatDocumentId?: Maybe<Scalars['String']['output']>;
  status?: Maybe<GQLListingAgreementStatus>;
  suggestedPrice?: Maybe<Scalars['Float']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type GQLListingAgreementInteraction = {
  __typename?: 'ListingAgreementInteraction';
  broker?: Maybe<GQLBroker>;
  employeeId?: Maybe<Scalars['String']['output']>;
  eventTimestamp: Scalars['DateTime']['output'];
  eventType: GQLListingAgreementInteractionType;
  extraData?: Maybe<Scalars['JSON']['output']>;
  id: Scalars['Int']['output'];
  listingAgreementsId: Scalars['String']['output'];
  name?: Maybe<Scalars['String']['output']>;
  seller?: Maybe<GQLListingAgreementInteractionSeller>;
  sellerId?: Maybe<Scalars['String']['output']>;
};

export type GQLListingAgreementInteractionSeller = {
  __typename?: 'ListingAgreementInteractionSeller';
  contactId?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
};

export type GQLListingAgreementInteractionSummary = {
  __typename?: 'ListingAgreementInteractionSummary';
  estate?: Maybe<GQLBrokerEstate>;
  estateId: Scalars['String']['output'];
  interactions: Array<GQLListingAgreementInteraction>;
};

export enum GQLListingAgreementInteractionType {
  Created = 'created',
  EmailOpened = 'email_opened',
  EmailSent = 'email_sent',
  EtakstClicked = 'etakst_clicked',
  EtakstSent = 'etakst_sent',
  Expired = 'expired',
  FinancingRequested = 'financing_requested',
  SentToSeller = 'sent_to_seller',
  Signed = 'signed',
  SignedBySeller = 'signed_by_seller',
  StartSigning = 'start_signing',
  TipSent = 'tip_sent',
  Viewed = 'viewed',
  Withdrawn = 'withdrawn'
}

export type GQLListingAgreementInteractions = {
  __typename?: 'ListingAgreementInteractions';
  estateId: Scalars['String']['output'];
  interactions: Array<GQLListingAgreementInteraction>;
  name: Scalars['String']['output'];
};

export enum GQLListingAgreementStatus {
  Created = 'Created',
  Expired = 'Expired',
  PartialSigned = 'PartialSigned',
  Sent = 'Sent',
  Signed = 'Signed',
  SignedViaNext = 'SignedViaNext',
  Signing = 'Signing'
}

export type GQLLocation = {
  __typename?: 'Location';
  coordinates?: Maybe<Array<Scalars['Float']['output']>>;
  type?: Maybe<Scalars['String']['output']>;
};

export type GQLMarketingChannel = {
  __typename?: 'MarketingChannel';
  id: Scalars['String']['output'];
  title: Scalars['String']['output'];
};

export type GQLMarketingPackage = {
  __typename?: 'MarketingPackage';
  active?: Maybe<Scalars['Boolean']['output']>;
  channels: Array<GQLMarketingChannel>;
  clicks?: Maybe<Scalars['String']['output']>;
  id: Scalars['NodeID']['output'];
  name?: Maybe<Scalars['String']['output']>;
  packageId?: Maybe<Scalars['Int']['output']>;
  price?: Maybe<Scalars['Float']['output']>;
  productTag?: Maybe<Scalars['String']['output']>;
  public?: Maybe<Scalars['Boolean']['output']>;
  shortName?: Maybe<Scalars['String']['output']>;
  views?: Maybe<Scalars['String']['output']>;
};

export type GQLMarketingStart = {
  __typename?: 'MarketingStart';
  date?: Maybe<Scalars['DateTime']['output']>;
  source?: Maybe<Scalars['String']['output']>;
};

export type GQLMinMax = {
  max: Scalars['Int']['input'];
  min: Scalars['Int']['input'];
};

export type GQLModule = {
  __typename?: 'Module';
  accordion?: Maybe<Array<Maybe<GQLAccordion>>>;
  body?: Maybe<Scalars['String']['output']>;
  type: Scalars['String']['output'];
};

export type GQLMutation = {
  __typename?: 'Mutation';
  addAward: Scalars['Boolean']['output'];
  addInspectionEvents: Scalars['Boolean']['output'];
  clearInspectionEvents: Scalars['Boolean']['output'];
  createBrokerPartner: GQLBrokerPartner;
  createPageVisit?: Maybe<GQLPageVisit>;
  deleteBrokerPartner: Scalars['Boolean']['output'];
  deleteInspectionEvent: Scalars['Boolean']['output'];
  endPageVisit: Scalars['Boolean']['output'];
  hideAward: Scalars['Boolean']['output'];
  hideBrokerPartner?: Maybe<GQLBrokerPartner>;
  markNewsAsRead?: Maybe<Scalars['Boolean']['output']>;
  pageVisitHeartbeat?: Maybe<GQLPageVisit>;
  removeAward: Scalars['Boolean']['output'];
  reorderBrokerPartners: Scalars['Boolean']['output'];
  resetFlagForAllUsers: Scalars['Boolean']['output'];
  resetForm?: Maybe<Scalars['Boolean']['output']>;
  sendInspectionLead: GQLInspectionLead;
  updateAward: Scalars['Boolean']['output'];
  updateBroker: Scalars['Boolean']['output'];
  updateBrokerPartner?: Maybe<GQLBrokerPartner>;
  updateBrokerProfileLinks: Scalars['Boolean']['output'];
  updateInspectionEvent: Scalars['Boolean']['output'];
  updateInspectionEvents: Scalars['Boolean']['output'];
  updateInspectionFolderNotes?: Maybe<GQLInspectionFolder>;
  upsertEstatePublishDate: Scalars['Boolean']['output'];
  userResetAllFlags: Scalars['Boolean']['output'];
  userResetFlag: Scalars['Boolean']['output'];
  userSetFlag: Scalars['Boolean']['output'];
};


export type GQLMutationAddAwardArgs = {
  input: GQLCreateAward;
};


export type GQLMutationAddInspectionEventsArgs = {
  estateId: Scalars['String']['input'];
  events: Array<GQLInspectionEventInput>;
};


export type GQLMutationClearInspectionEventsArgs = {
  estateId: Scalars['String']['input'];
};


export type GQLMutationCreateBrokerPartnerArgs = {
  input: GQLBrokerPartnerCreateInput;
};


export type GQLMutationCreatePageVisitArgs = {
  input: GQLCreatePageVisitInput;
};


export type GQLMutationDeleteBrokerPartnerArgs = {
  id: Scalars['String']['input'];
};


export type GQLMutationDeleteInspectionEventArgs = {
  eventId: Scalars['String']['input'];
};


export type GQLMutationEndPageVisitArgs = {
  pageId: Scalars['String']['input'];
};


export type GQLMutationHideAwardArgs = {
  hidden: Scalars['Boolean']['input'];
  id: Scalars['String']['input'];
};


export type GQLMutationHideBrokerPartnerArgs = {
  hidden: Scalars['Boolean']['input'];
  id: Scalars['String']['input'];
};


export type GQLMutationMarkNewsAsReadArgs = {
  newsId: Scalars['String']['input'];
};


export type GQLMutationPageVisitHeartbeatArgs = {
  estateId: Scalars['String']['input'];
  pageId: Scalars['String']['input'];
};


export type GQLMutationRemoveAwardArgs = {
  id: Scalars['String']['input'];
};


export type GQLMutationReorderBrokerPartnersArgs = {
  ids: Array<Scalars['String']['input']>;
};


export type GQLMutationResetFlagForAllUsersArgs = {
  flag: Scalars['String']['input'];
};


export type GQLMutationResetFormArgs = {
  estateId: Scalars['String']['input'];
  formType: Scalars['String']['input'];
};


export type GQLMutationSendInspectionLeadArgs = {
  contactId: Scalars['String']['input'];
  estateId: Scalars['String']['input'];
  leadType: GQLInspectionLeadType;
  source?: InputMaybe<Scalars['String']['input']>;
};


export type GQLMutationUpdateAwardArgs = {
  input: GQLUpdateAward;
};


export type GQLMutationUpdateBrokerArgs = {
  input: GQLUpdateBrokerPayload;
};


export type GQLMutationUpdateBrokerPartnerArgs = {
  input: GQLBrokerPartnerUpdateInput;
};


export type GQLMutationUpdateBrokerProfileLinksArgs = {
  input: GQLBrokerProfileLinksPayload;
};


export type GQLMutationUpdateInspectionEventArgs = {
  event: GQLInspectionEventInput;
  eventId: Scalars['String']['input'];
};


export type GQLMutationUpdateInspectionEventsArgs = {
  estateId: Scalars['String']['input'];
  events: Array<GQLInspectionEventInput>;
};


export type GQLMutationUpdateInspectionFolderNotesArgs = {
  estateId: Scalars['String']['input'];
  notes: Scalars['String']['input'];
};


export type GQLMutationUpsertEstatePublishDateArgs = {
  estateId: Scalars['String']['input'];
  publishDate?: InputMaybe<Scalars['DateTime']['input']>;
};


export type GQLMutationUserResetFlagArgs = {
  flag: Scalars['String']['input'];
};


export type GQLMutationUserSetFlagArgs = {
  flag: Scalars['String']['input'];
  value: Scalars['Boolean']['input'];
};

export type GQLNextDocument = {
  __typename?: 'NextDocument';
  approvalDate?: Maybe<Scalars['DateTime']['output']>;
  approvedBy?: Maybe<Scalars['String']['output']>;
  docType: Scalars['Int']['output'];
  documentId: Scalars['String']['output'];
  extension: Scalars['String']['output'];
  head: Scalars['String']['output'];
  lastChanged: Scalars['DateTime']['output'];
  signStatus?: Maybe<Scalars['Int']['output']>;
};

export type GQLNordvikAward = {
  __typename?: 'NordvikAward';
  awardId?: Maybe<Scalars['String']['output']>;
  hidden?: Maybe<Scalars['Boolean']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  origin?: Maybe<Scalars['String']['output']>;
  private?: Maybe<Scalars['Boolean']['output']>;
  year?: Maybe<Scalars['Int']['output']>;
};

export type GQLPageVisit = {
  __typename?: 'PageVisit';
  browser?: Maybe<Scalars['String']['output']>;
  contactId?: Maybe<Scalars['String']['output']>;
  contactName?: Maybe<Scalars['String']['output']>;
  employeeId?: Maybe<Scalars['String']['output']>;
  endTime?: Maybe<Scalars['DateTime']['output']>;
  estateId: Scalars['String']['output'];
  id: Scalars['Int']['output'];
  lastHeartbeat: Scalars['DateTime']['output'];
  location?: Maybe<Scalars['String']['output']>;
  pageId: Scalars['String']['output'];
  source?: Maybe<Scalars['String']['output']>;
  startTime: Scalars['DateTime']['output'];
  totalTimeSpent: Scalars['Int']['output'];
  userAgent?: Maybe<GQLUserAgent>;
};

export type GQLPagination = {
  __typename?: 'Pagination';
  count?: Maybe<Scalars['Int']['output']>;
  limit?: Maybe<Scalars['Int']['output']>;
  offset?: Maybe<Scalars['Int']['output']>;
  total?: Maybe<Scalars['Int']['output']>;
};

export type GQLPartOwnership = {
  __typename?: 'PartOwnership';
  businessManagerContactId?: Maybe<Scalars['String']['output']>;
  contactId?: Maybe<Scalars['String']['output']>;
  estateHousingCooperativeStockHousingUnitNumber?: Maybe<Scalars['String']['output']>;
  estateHousingCooperativeStockNumber?: Maybe<Scalars['String']['output']>;
  partAbout?: Maybe<Scalars['String']['output']>;
  partName?: Maybe<Scalars['String']['output']>;
  partNumber?: Maybe<Scalars['Int']['output']>;
  partOrgNumber?: Maybe<Scalars['String']['output']>;
};

export enum GQLPersonalInfoOrigin {
  Eid = 'eid',
  Unknown = 'unknown',
  UserFormInput = 'userFormInput'
}

export type GQLPostDate = {
  __typename?: 'PostDate';
  date: Scalars['String']['output'];
  timezone: Scalars['String']['output'];
  timezone_type: Scalars['Int']['output'];
};

export type GQLPriceIndex = {
  __typename?: 'PriceIndex';
  area?: Maybe<Scalars['String']['output']>;
  avgSalesTime?: Maybe<Scalars['Float']['output']>;
  avgSqmPrice?: Maybe<Scalars['Float']['output']>;
  date: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  indexChange1Month?: Maybe<Scalars['Float']['output']>;
  indexChange1Quarter?: Maybe<Scalars['Float']['output']>;
  indexChange4Quarter?: Maybe<Scalars['Float']['output']>;
  indexChange5Years?: Maybe<Scalars['Float']['output']>;
  indexChange10Years?: Maybe<Scalars['Float']['output']>;
  indexChange12Months?: Maybe<Scalars['Float']['output']>;
  region?: Maybe<Scalars['String']['output']>;
  type: Scalars['String']['output'];
};

export type GQLPriceStatistics = {
  __typename?: 'PriceStatistics';
  indexes: Array<GQLPriceIndex>;
  secondaryIndexes: Array<GQLPriceIndex>;
};

export type GQLQuery = {
  __typename?: 'Query';
  activitySummary: GQLActivitySummary;
  allDepartments?: Maybe<GQLDepartmentsResponse>;
  allListingAgreementInteractions: Array<GQLListingAgreementInteractionSummary>;
  assignmentDocumentStatus: GQLAssignmentDocumentStatusItem;
  brokerByEmail?: Maybe<GQLBroker>;
  brokerByEmployeeId?: Maybe<GQLBroker>;
  brokerPartner?: Maybe<GQLBrokerPartner>;
  brokerPartners: Array<GQLBrokerPartner>;
  brokers?: Maybe<GQLBrokersResponse>;
  budgetPostDescriptions: Array<GQLBudgetPostDescription>;
  cmsArticleBySlug?: Maybe<GQLCmsArticle>;
  cmsArticleCategories: Array<GQLCmsArticleCategory>;
  cmsArticles: GQLCmsArticleResponse;
  cmsChangelogs: GQLCmsArticleResponse;
  cmsIncidentBySlug?: Maybe<GQLCraftCmsIncident>;
  cmsIncidents: GQLCmsIncidentsResponse;
  cmsJobBySlug?: Maybe<GQLCraftCmsJob>;
  cmsJobsListing: GQLCraftCmsJobsListingResponse;
  contact?: Maybe<GQLContact>;
  currentBroker?: Maybe<GQLBroker>;
  currentBrokerKti: GQLBrokerKtiResponse;
  currentBrokerPartners: Array<GQLBrokerPartner>;
  currentBrokerProfileLinks?: Maybe<GQLBrokerProfileLinks>;
  currentBrokerRatings?: Maybe<GQLBrokerRatingsList>;
  currentBrokerRatingsTotal?: Maybe<GQLCurrentBrokerRatingsTotal>;
  dashboardAverageCommission: GQLAverageCommissionResponse;
  dashboardCache: Scalars['Boolean']['output'];
  dashboardCacheForEmployee: Scalars['Boolean']['output'];
  dashboardExpectedRevenue: Array<GQLDashboardStatusEntry>;
  dashboardImportantTasks: Array<GQLImportantTask>;
  dashboardKeyFigures: Array<GQLKeyFigureEntry>;
  dashboardKti: GQLDashboardKtiResponse;
  dashboardLeads: GQLDashboardLeadsResponse;
  dashboardRevenue: GQLRevenueResponse;
  dashboardToplist: GQLDashboardToplistResponse;
  dashboardUpcomingActivities: Array<GQLDashboardActivity>;
  department?: Maybe<GQLDepartment>;
  emailAuditsByEstateId: Array<GQLEmailAudit>;
  emailInteractionsForEstate: Array<GQLEmailInteraction>;
  estate?: Maybe<GQLBrokerEstate>;
  estateFormsByEstateId: Array<GQLBrokerEstateForm>;
  estatePriceHistories: Array<GQLBrokerEstatePriceHistory>;
  estates: Array<GQLBrokerEstate>;
  estatesForBrokerById: GQLBrokerEstatesResponse;
  estatesForBrokerIdCount: Array<GQLBrokerEstateCountResponse>;
  estatesForDepartment: GQLBrokerEstatesResponse;
  findEstates: Array<GQLBrokerEstate>;
  findEstatesForBroker: Array<GQLBrokerEstate>;
  hallOfFame: Array<GQLHallOfFameEntry>;
  inspectionEvents: Array<GQLInspectionEvent>;
  inspectionFolder?: Maybe<GQLInspectionFolder>;
  inspectionLeadsForEstate: Array<GQLInspectionLead>;
  listingAgreementByDocumentId?: Maybe<GQLListingAgreement>;
  listingAgreementByEstateId?: Maybe<GQLListingAgreement>;
  listingAgreementInteractions: Array<GQLListingAgreementInteraction>;
  listingAgreementInteractionsForBroker: Array<GQLListingAgreementInteractions>;
  listingAgreementInteractionsForCurrentBroker: Array<GQLListingAgreementInteractions>;
  listingAgreementInteractionsForEstate: Array<GQLListingAgreementInteraction>;
  mainBrokerPartners: Array<GQLBrokerPartner>;
  marketingPackages: Array<GQLMarketingPackage>;
  pageVisit?: Maybe<GQLPageVisit>;
  pageVisitHeartbeat?: Maybe<GQLPageVisit>;
  pageVisits: Array<GQLPageVisit>;
  priceStatistics: GQLPriceStatistics;
  ratings: GQLRatingResponse;
  readArticles: Array<GQLReadArticle>;
  storebrandDuplicateCheck: GQLStorebrandDuplicateCheckResult;
  syncEstateWithVitec: Scalars['Boolean']['output'];
  toplist: GQLToplistResponse;
  userHasFlag: Scalars['Boolean']['output'];
  userNotifications: GQLUserNotificationsResponse;
};


export type GQLQueryActivitySummaryArgs = {
  estateId: Scalars['String']['input'];
};


export type GQLQueryAllListingAgreementInteractionsArgs = {
  input?: InputMaybe<GQLInteractionFilter>;
};


export type GQLQueryAssignmentDocumentStatusArgs = {
  estateId: Scalars['String']['input'];
  type: GQLAssignmentDocumentType;
};


export type GQLQueryBrokerByEmailArgs = {
  email: Scalars['String']['input'];
};


export type GQLQueryBrokerByEmployeeIdArgs = {
  employeeId: Scalars['String']['input'];
};


export type GQLQueryBrokerPartnerArgs = {
  id: Scalars['String']['input'];
};


export type GQLQueryBrokerPartnersArgs = {
  employeeId: Scalars['String']['input'];
};


export type GQLQueryCmsArticleBySlugArgs = {
  slug: Scalars['String']['input'];
};


export type GQLQueryCmsArticleCategoriesArgs = {
  type?: InputMaybe<GQLCmsArticleType>;
};


export type GQLQueryCmsArticlesArgs = {
  categorySlug?: InputMaybe<Scalars['String']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  searchQuery?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<GQLCmsArticleType>;
  userId?: InputMaybe<Scalars['String']['input']>;
};


export type GQLQueryCmsChangelogsArgs = {
  categorySlug?: InputMaybe<Scalars['String']['input']>;
  dateFrom?: InputMaybe<Scalars['DateTime']['input']>;
  dateTo?: InputMaybe<Scalars['DateTime']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  searchQuery?: InputMaybe<Scalars['String']['input']>;
};


export type GQLQueryCmsIncidentBySlugArgs = {
  slug: Scalars['String']['input'];
};


export type GQLQueryCmsIncidentsArgs = {
  active?: InputMaybe<Scalars['Boolean']['input']>;
};


export type GQLQueryCmsJobBySlugArgs = {
  slug: Scalars['String']['input'];
};


export type GQLQueryContactArgs = {
  contactId: Scalars['String']['input'];
  source?: InputMaybe<GQLSource>;
};


export type GQLQueryCurrentBrokerKtiArgs = {
  includeDepartment?: InputMaybe<Scalars['Boolean']['input']>;
  includeNordvik?: InputMaybe<Scalars['Boolean']['input']>;
  includeReviews?: InputMaybe<Scalars['Boolean']['input']>;
};


export type GQLQueryCurrentBrokerRatingsArgs = {
  dateFrom?: InputMaybe<Scalars['DateTime']['input']>;
  dateTo?: InputMaybe<Scalars['DateTime']['input']>;
  featured?: InputMaybe<Scalars['Boolean']['input']>;
  hasReview?: InputMaybe<Scalars['Boolean']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  rating?: InputMaybe<Scalars['Int']['input']>;
  sortBy?: InputMaybe<GQLBrokerRatingSortBy>;
  sortDir?: InputMaybe<GQLSortDirection>;
};


export type GQLQueryCurrentBrokerRatingsTotalArgs = {
  dateFrom?: InputMaybe<Scalars['DateTime']['input']>;
  dateTo?: InputMaybe<Scalars['DateTime']['input']>;
  featured?: InputMaybe<Scalars['Boolean']['input']>;
  hasReview?: InputMaybe<Scalars['Boolean']['input']>;
  rating?: InputMaybe<Scalars['Int']['input']>;
};


export type GQLQueryDashboardAverageCommissionArgs = {
  type: GQLDashboardType;
};


export type GQLQueryDashboardCacheForEmployeeArgs = {
  employeeId: Scalars['String']['input'];
  section: GQLSection;
};


export type GQLQueryDashboardExpectedRevenueArgs = {
  type: GQLDashboardType;
};


export type GQLQueryDashboardKeyFiguresArgs = {
  period: Scalars['String']['input'];
  type: GQLDashboardType;
};


export type GQLQueryDashboardLeadsArgs = {
  period?: InputMaybe<Scalars['String']['input']>;
  type: GQLDashboardType;
};


export type GQLQueryDashboardRevenueArgs = {
  type: GQLDashboardType;
};


export type GQLQueryDashboardToplistArgs = {
  amountOfEntries?: InputMaybe<Scalars['Int']['input']>;
  excludeCurrent?: InputMaybe<Scalars['Boolean']['input']>;
  period?: InputMaybe<Scalars['String']['input']>;
  section: GQLToplistSection;
  type: GQLDashboardType;
};


export type GQLQueryDashboardUpcomingActivitiesArgs = {
  type: GQLDashboardType;
};


export type GQLQueryDepartmentArgs = {
  departmentId: Scalars['Int']['input'];
};


export type GQLQueryEmailAuditsByEstateIdArgs = {
  estateId: Scalars['String']['input'];
};


export type GQLQueryEmailInteractionsForEstateArgs = {
  estateId: Scalars['String']['input'];
};


export type GQLQueryEstateArgs = {
  id: Scalars['String']['input'];
};


export type GQLQueryEstateFormsByEstateIdArgs = {
  estateId: Scalars['String']['input'];
  estateProps?: InputMaybe<GQLEstateProps>;
};


export type GQLQueryEstatesForBrokerByIdArgs = {
  archived?: InputMaybe<Scalars['Boolean']['input']>;
  assignmentTypeGroup?: InputMaybe<Array<Scalars['Int']['input']>>;
  brokerId?: InputMaybe<Scalars['String']['input']>;
  disableCache?: InputMaybe<Scalars['Boolean']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
  sortBy?: InputMaybe<GQLSortEstateBy>;
  tabs: Array<GQLEstateTabFilter>;
};


export type GQLQueryEstatesForBrokerIdCountArgs = {
  brokerId?: InputMaybe<Scalars['String']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  tabs: Array<GQLEstateTabFilter>;
};


export type GQLQueryEstatesForDepartmentArgs = {
  archived?: InputMaybe<Scalars['Boolean']['input']>;
  assignmentTypeGroup?: InputMaybe<Array<Scalars['Int']['input']>>;
  brokerIds?: InputMaybe<Array<Scalars['String']['input']>>;
  departmentId: Scalars['Int']['input'];
  email?: InputMaybe<Scalars['String']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
  sortBy?: InputMaybe<GQLSortEstateBy>;
  tabs: Array<GQLEstateTabFilter>;
};


export type GQLQueryFindEstatesArgs = {
  filters: GQLFindEstatesFilters;
};


export type GQLQueryFindEstatesForBrokerArgs = {
  employeeId: Scalars['String']['input'];
  filters?: InputMaybe<GQLFindEstatesForBrokerFilters>;
};


export type GQLQueryInspectionEventsArgs = {
  estateId: Scalars['String']['input'];
};


export type GQLQueryInspectionFolderArgs = {
  estateId: Scalars['String']['input'];
};


export type GQLQueryInspectionLeadsForEstateArgs = {
  estateId: Scalars['String']['input'];
  leadType?: InputMaybe<GQLInspectionLeadType>;
};


export type GQLQueryListingAgreementByDocumentIdArgs = {
  documentId: Scalars['String']['input'];
};


export type GQLQueryListingAgreementByEstateIdArgs = {
  estateId: Scalars['String']['input'];
};


export type GQLQueryListingAgreementInteractionsArgs = {
  listingAgreementId: Scalars['String']['input'];
};


export type GQLQueryListingAgreementInteractionsForBrokerArgs = {
  employeeId: Scalars['String']['input'];
};


export type GQLQueryListingAgreementInteractionsForEstateArgs = {
  estateId: Scalars['String']['input'];
};


export type GQLQueryMainBrokerPartnersArgs = {
  estateId: Scalars['String']['input'];
};


export type GQLQueryMarketingPackagesArgs = {
  active: Scalars['Boolean']['input'];
  publicVisible?: InputMaybe<Scalars['Boolean']['input']>;
  type: Scalars['String']['input'];
};


export type GQLQueryPageVisitArgs = {
  id: Scalars['Int']['input'];
};


export type GQLQueryPageVisitHeartbeatArgs = {
  estateId: Scalars['String']['input'];
  pageId: Scalars['String']['input'];
};


export type GQLQueryPageVisitsArgs = {
  estateId: Scalars['String']['input'];
  includeSubPages?: InputMaybe<Scalars['Boolean']['input']>;
};


export type GQLQueryPriceStatisticsArgs = {
  postalCode: Scalars['String']['input'];
  years?: InputMaybe<Scalars['Float']['input']>;
};


export type GQLQueryRatingsArgs = {
  employeeId?: InputMaybe<Scalars['String']['input']>;
  ytd?: InputMaybe<Scalars['Boolean']['input']>;
};


export type GQLQueryStorebrandDuplicateCheckArgs = {
  input: GQLStorebrandDuplicateCheckInput;
};


export type GQLQuerySyncEstateWithVitecArgs = {
  estateId: Scalars['String']['input'];
};


export type GQLQueryToplistArgs = {
  cron?: InputMaybe<Scalars['Boolean']['input']>;
  departmentId?: InputMaybe<Scalars['Int']['input']>;
  employeeId?: InputMaybe<Scalars['String']['input']>;
  endDate?: InputMaybe<Scalars['Date']['input']>;
  estateType?: InputMaybe<Scalars['String']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  marketingPackage?: InputMaybe<Scalars['String']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  partner?: InputMaybe<Scalars['String']['input']>;
  period?: InputMaybe<Scalars['String']['input']>;
  roles?: InputMaybe<Array<GQLToplistRole>>;
  section?: InputMaybe<GQLToplistSection>;
  startDate?: InputMaybe<Scalars['Date']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
};


export type GQLQueryUserHasFlagArgs = {
  flag: Scalars['String']['input'];
};

export type GQLRating = {
  __typename?: 'Rating';
  average?: Maybe<Scalars['Float']['output']>;
  count?: Maybe<Scalars['Int']['output']>;
  reviewsCount?: Maybe<Scalars['Int']['output']>;
  weighted?: Maybe<Scalars['Float']['output']>;
};

export type GQLRatingResponse = {
  __typename?: 'RatingResponse';
  average?: Maybe<Scalars['Float']['output']>;
  count?: Maybe<Scalars['Int']['output']>;
};

export type GQLReadArticle = {
  __typename?: 'ReadArticle';
  id: Scalars['String']['output'];
  readAt: Scalars['DateTime']['output'];
};

export type GQLRelevantForEstateWithProps = {
  __typename?: 'RelevantForEstateWithProps';
  projectRelation?: Maybe<Scalars['Int']['output']>;
  status?: Maybe<Scalars['Int']['output']>;
};

export type GQLRevenueEntry = {
  __typename?: 'RevenueEntry';
  value: Scalars['Float']['output'];
  year: Scalars['Int']['output'];
};

export type GQLRevenueMonth = {
  __typename?: 'RevenueMonth';
  budget?: Maybe<GQLRevenueEntry>;
  current?: Maybe<GQLRevenueEntry>;
  month: Scalars['Int']['output'];
  previous: GQLRevenueEntry;
};

export type GQLRevenueResponse = {
  __typename?: 'RevenueResponse';
  budgetTotal: Scalars['Float']['output'];
  currentYearBudget: Scalars['Float']['output'];
  currentYearTotal: Scalars['Int']['output'];
  entries: Array<GQLRevenueMonth>;
  hitBudgetPercentage: Scalars['Float']['output'];
  percentageChange: Scalars['Float']['output'];
  previousYearTotal?: Maybe<Scalars['Float']['output']>;
  previousYearUntilNow?: Maybe<Scalars['Float']['output']>;
};

export type GQLReview = {
  __typename?: 'Review';
  agentSatisfaction?: Maybe<Scalars['Int']['output']>;
  createdAt?: Maybe<Scalars['Date']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  lang?: Maybe<Scalars['String']['output']>;
  rating?: Maybe<Scalars['Int']['output']>;
  recommendation?: Maybe<Scalars['Int']['output']>;
  reviewerShortName?: Maybe<Scalars['String']['output']>;
  text?: Maybe<Scalars['String']['output']>;
};

export enum GQLSection {
  AverageCommission = 'averageCommission',
  KeyFigures = 'keyFigures',
  Revenue = 'revenue'
}

export type GQLSellerProxy = {
  __typename?: 'SellerProxy';
  companyName?: Maybe<Scalars['String']['output']>;
  contactId: Scalars['String']['output'];
  email?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  mobilePhone?: Maybe<Scalars['String']['output']>;
};

export enum GQLSignatureMechanism {
  Handwritten = 'handwritten',
  HandwrittenWithIdentification = 'handwritten_with_identification',
  Identification = 'identification',
  Pkisignature = 'pkisignature'
}

export type GQLSigner = {
  __typename?: 'Signer';
  email: Scalars['String']['output'];
  externalSignerId: Scalars['String']['output'];
  firstName?: Maybe<Scalars['String']['output']>;
  id: Scalars['NodeID']['output'];
  lastName?: Maybe<Scalars['String']['output']>;
  listingAgreementId: Scalars['String']['output'];
  phone?: Maybe<Scalars['String']['output']>;
  signedAt?: Maybe<Scalars['DateTime']['output']>;
  title?: Maybe<Scalars['String']['output']>;
  url?: Maybe<Scalars['String']['output']>;
};

export type GQLSignerSummary = {
  __typename?: 'SignerSummary';
  contactId: Scalars['String']['output'];
  lastHeartbeat?: Maybe<Scalars['DateTime']['output']>;
  name: Scalars['String']['output'];
  totalTimeSpent: Scalars['Int']['output'];
  visitsCount: Scalars['Int']['output'];
};

export type GQLSignicatDocument = {
  __typename?: 'SignicatDocument';
  accountId?: Maybe<Scalars['String']['output']>;
  attachments?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  created?: Maybe<Scalars['DateTime']['output']>;
  currentSignatures?: Maybe<Scalars['Int']['output']>;
  deadline?: Maybe<Scalars['DateTime']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  documentId?: Maybe<Scalars['String']['output']>;
  documentSignatures?: Maybe<Array<Maybe<GQLSignicatExtendedDocumentSignature>>>;
  externalId?: Maybe<Scalars['String']['output']>;
  lastUpdated?: Maybe<Scalars['DateTime']['output']>;
  requiredSignatures?: Maybe<Scalars['Int']['output']>;
  signedDate?: Maybe<Scalars['DateTime']['output']>;
  signers?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  status?: Maybe<GQLSignicatDocumentStatus>;
  tags?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  title?: Maybe<Scalars['String']['output']>;
};

export type GQLSignicatDocumentStatus = {
  __typename?: 'SignicatDocumentStatus';
  documentStatus?: Maybe<GQLSignicatDocumentStatusEnum>;
};

export enum GQLSignicatDocumentStatusEnum {
  Canceled = 'canceled',
  Expired = 'expired',
  Partialsigned = 'partialsigned',
  Signed = 'signed',
  Unsigned = 'unsigned',
  WaitingForAttachments = 'waiting_for_attachments'
}

export type GQLSignicatExtendedDocumentSignature = {
  __typename?: 'SignicatExtendedDocumentSignature';
  clientIp?: Maybe<Scalars['String']['output']>;
  dateOfBirth?: Maybe<Scalars['String']['output']>;
  externalSignerId?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  fullName?: Maybe<Scalars['String']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  mechanism?: Maybe<GQLSignatureMechanism>;
  middleName?: Maybe<Scalars['String']['output']>;
  personalInfoOrigin?: Maybe<GQLPersonalInfoOrigin>;
  signatureMethod?: Maybe<GQLSignicatSignatureMethod>;
  signatureMethodUniqueId?: Maybe<Scalars['String']['output']>;
  signedTime?: Maybe<Scalars['DateTime']['output']>;
  signerId?: Maybe<Scalars['String']['output']>;
  socialSecurityNumber?: Maybe<GQLSignicatSocialSecurityNumber>;
};

export enum GQLSignicatSignatureMethod {
  DkNemid = 'dk_nemid',
  FiEid = 'fi_eid',
  Mitid = 'mitid',
  NoBankidMobile = 'no_bankid_mobile',
  NoBankidNetcentric = 'no_bankid_netcentric',
  NoBankidOidc = 'no_bankid_oidc',
  NoBuypass = 'no_buypass',
  SeBankid = 'se_bankid',
  SmsOtp = 'sms_otp',
  Unknown = 'unknown'
}

export type GQLSignicatSocialSecurityNumber = {
  __typename?: 'SignicatSocialSecurityNumber';
  countryCode?: Maybe<Scalars['String']['output']>;
  value?: Maybe<Scalars['String']['output']>;
};

export type GQLSimpleContact = {
  __typename?: 'SimpleContact';
  email?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
};

export enum GQLSortDirection {
  Asc = 'Asc',
  Desc = 'Desc'
}

export enum GQLSortEstateBy {
  ChangedDate = 'changedDate'
}

export enum GQLSortOrder {
  Ascending = 'Ascending',
  Descending = 'Descending'
}

export enum GQLSource {
  Next = 'Next',
  Nordvik = 'Nordvik'
}

export type GQLStorebrandDuplicateCheckInput = {
  estateId: Scalars['String']['input'];
};

export type GQLStorebrandDuplicateCheckResult = {
  __typename?: 'StorebrandDuplicateCheckResult';
  hasDuplicates: Scalars['Boolean']['output'];
};

export type GQLSumArea = {
  __typename?: 'SumArea';
  bra: Scalars['Float']['output'];
  braB: Scalars['Float']['output'];
  braE: Scalars['Float']['output'];
  braI: Scalars['Float']['output'];
  braS: Scalars['Float']['output'];
  bta: Scalars['Float']['output'];
  pRom: Scalars['Float']['output'];
  sRom: Scalars['Float']['output'];
  tba: Scalars['Float']['output'];
};

export type GQLTargetRole = {
  __typename?: 'TargetRole';
  id?: Maybe<Scalars['Int']['output']>;
  roleTypeId?: Maybe<Scalars['Int']['output']>;
  slug?: Maybe<Scalars['String']['output']>;
  title?: Maybe<Scalars['String']['output']>;
};

export type GQLToplistEntry = {
  __typename?: 'ToplistEntry';
  avatarUrl?: Maybe<Scalars['String']['output']>;
  count?: Maybe<Scalars['Int']['output']>;
  department?: Maybe<Scalars['String']['output']>;
  departmentId?: Maybe<Scalars['Int']['output']>;
  id?: Maybe<Scalars['NodeID']['output']>;
  imageUrl?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  reviews?: Maybe<Scalars['Int']['output']>;
  value: Scalars['Float']['output'];
};

export type GQLToplistEntryCurrent = {
  __typename?: 'ToplistEntryCurrent';
  imageUrl?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  position?: Maybe<Scalars['Int']['output']>;
  value: Scalars['Float']['output'];
};

export type GQLToplistResponse = {
  __typename?: 'ToplistResponse';
  items: Array<GQLToplistEntry>;
  totalCount: Scalars['Int']['output'];
};

export enum GQLToplistRole {
  PowerOfAttorney = 'powerOfAttorney',
  Realtor = 'realtor'
}

export enum GQLToplistSection {
  Commission = 'commission',
  Inspection = 'inspection',
  Kti = 'kti',
  Marketing = 'marketing',
  Partner = 'partner',
  Signed = 'signed',
  Sold = 'sold',
  Valuation = 'valuation'
}

export type GQLUpdateAward = {
  id: Scalars['String']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  origin?: InputMaybe<Scalars['String']['input']>;
  year?: InputMaybe<Scalars['Int']['input']>;
};

export type GQLUpdateUsp = {
  description?: InputMaybe<Scalars['String']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
};

export type GQLUserAgent = {
  __typename?: 'UserAgent';
  browser?: Maybe<GQLUserAgentBrowser>;
  cpu?: Maybe<Scalars['JSON']['output']>;
  device?: Maybe<GQLUserAgentDevice>;
  engine?: Maybe<GQLUserAgentEngine>;
  isBot?: Maybe<Scalars['Boolean']['output']>;
  os?: Maybe<GQLUserAgentOs>;
  ua?: Maybe<Scalars['String']['output']>;
};

export type GQLUserAgentBrowser = {
  __typename?: 'UserAgentBrowser';
  major?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  version?: Maybe<Scalars['String']['output']>;
};

export type GQLUserAgentDevice = {
  __typename?: 'UserAgentDevice';
  model?: Maybe<Scalars['String']['output']>;
  vendor?: Maybe<Scalars['String']['output']>;
};

export type GQLUserAgentEngine = {
  __typename?: 'UserAgentEngine';
  name?: Maybe<Scalars['String']['output']>;
  version?: Maybe<Scalars['String']['output']>;
};

export type GQLUserAgentOs = {
  __typename?: 'UserAgentOS';
  name?: Maybe<Scalars['String']['output']>;
  version?: Maybe<Scalars['String']['output']>;
};

export type GQLUserNotificationsResponse = {
  __typename?: 'UserNotificationsResponse';
  newsCount: Scalars['Int']['output'];
  totalCount: Scalars['Int']['output'];
};

export type GQLUsp = {
  __typename?: 'Usp';
  description?: Maybe<Scalars['String']['output']>;
  title?: Maybe<Scalars['String']['output']>;
};

export type GQLFindEstatesFilters = {
  baseType?: InputMaybe<Array<Scalars['String']['input']>>;
  city?: InputMaybe<Scalars['String']['input']>;
  estateType?: InputMaybe<Array<Scalars['String']['input']>>;
  latitude?: InputMaybe<Scalars['Float']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  longitude?: InputMaybe<Scalars['Float']['input']>;
  noBedRooms?: InputMaybe<Scalars['String']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  price?: InputMaybe<GQLMinMax>;
  radius?: InputMaybe<Scalars['Float']['input']>;
  size?: InputMaybe<GQLMinMax>;
  soldDateAfter?: InputMaybe<Scalars['DateTime']['input']>;
  sortBy?: InputMaybe<Scalars['String']['input']>;
  statuses?: InputMaybe<Array<Scalars['Int']['input']>>;
};

export type GQLUpdateBrokerPayload = {
  awards?: InputMaybe<Array<GQLUpdateAward>>;
  instagram?: InputMaybe<Scalars['String']['input']>;
  usp?: InputMaybe<Array<GQLUpdateUsp>>;
};

export type GQLNotesQueryVariables = Exact<{
  estateId: Scalars['String']['input'];
}>;


export type GQLNotesQuery = { __typename?: 'Query', inspectionFolder?: { __typename?: 'InspectionFolder', id: string, notes?: string | undefined } | undefined };

export type GQLUpdateNotesMutationVariables = Exact<{
  estateId: Scalars['String']['input'];
  notes: Scalars['String']['input'];
}>;


export type GQLUpdateNotesMutation = { __typename?: 'Mutation', updateInspectionFolderNotes?: { __typename?: 'InspectionFolder', id: string, notes?: string | undefined } | undefined };

export type GQLStorebrandDuplicateCheckQueryVariables = Exact<{
  input: GQLStorebrandDuplicateCheckInput;
}>;


export type GQLStorebrandDuplicateCheckQuery = { __typename?: 'Query', storebrandDuplicateCheck: { __typename?: 'StorebrandDuplicateCheckResult', hasDuplicates: boolean } };

export type GQLAreaStatisticsQueryVariables = Exact<{
  postalCode: Scalars['String']['input'];
  years: Scalars['Float']['input'];
}>;


export type GQLAreaStatisticsQuery = { __typename?: 'Query', priceStatistics: { __typename?: 'PriceStatistics', indexes: Array<{ __typename?: 'PriceIndex', id: string, type: string, date: string, avgSalesTime?: number | undefined, region?: string | undefined, area?: string | undefined, avgSqmPrice?: number | undefined, indexChange12Months?: number | undefined, indexChange4Quarter?: number | undefined, indexChange5Years?: number | undefined, indexChange10Years?: number | undefined }>, secondaryIndexes: Array<{ __typename?: 'PriceIndex', id: string, type: string, date: string, avgSalesTime?: number | undefined, region?: string | undefined, area?: string | undefined, avgSqmPrice?: number | undefined, indexChange12Months?: number | undefined, indexChange4Quarter?: number | undefined, indexChange5Years?: number | undefined, indexChange10Years?: number | undefined }> } };

export type GQLBefaringRelevantEstatesQueryVariables = Exact<{
  estateId: Scalars['String']['input'];
}>;


export type GQLBefaringRelevantEstatesQuery = { __typename?: 'Query', inspectionFolder?: { __typename?: 'InspectionFolder', relevantLinks: Array<string> } | undefined };

export type GQLFindEstatesQueryVariables = Exact<{
  filters: GQLFindEstatesFilters;
}>;


export type GQLFindEstatesQuery = { __typename?: 'Query', findEstates: Array<{ __typename?: 'BrokerEstate', estateId: string, estateType?: string | undefined, estateTypeId?: string | undefined, noOfBedRooms?: number | undefined, soldDate?: string | undefined, departmentId?: number | undefined, latitude?: number | undefined, longitude?: number | undefined, hjemUrl?: string | undefined, heading?: string | undefined, address?: { __typename?: 'BrokerAddress', streetAddress?: string | undefined, zipCode?: string | undefined, city?: string | undefined } | undefined, estatePriceModel?: { __typename?: 'EstatePriceModel', soldPrice?: number | undefined, priceSuggestion?: number | undefined } | undefined, estatePrice?: { __typename?: 'EstatePrice', soldPrice?: number | undefined, priceSuggestion?: number | undefined } | undefined, brokersIdWithRoles?: Array<{ __typename?: 'BrokerIdWithRole', employeeId: string } | undefined> | undefined, stats?: { __typename?: 'EstateStats', interested?: number | undefined } | undefined, sumArea?: { __typename?: 'SumArea', braI: number, pRom: number, bra: number } | undefined, areaSize?: { __typename?: 'AreaSize', BRAItotal?: number | undefined } | undefined, finn?: { __typename?: 'FinnData', finnCode?: string | undefined } | undefined, images?: Array<{ __typename?: 'BrokerEstateImage', medium?: string | undefined }> | undefined }> };

export type GQLGetBrokerEstateQueryVariables = Exact<{
  estateId: Scalars['String']['input'];
}>;


export type GQLGetBrokerEstateQuery = { __typename?: 'Query', estate?: { __typename?: 'BrokerEstate', createdAt?: string | undefined, linkToNext?: string | undefined, assignmentNumber?: string | undefined, status: number, isValuation?: boolean | undefined, ownership?: number | undefined, ownershipType?: string | undefined, estateId: string, inspectionDate?: string | undefined, estateTypeExternal?: number | undefined, estateType?: string | undefined, estateTypeId?: string | undefined, noOfBedRooms?: number | undefined, latitude?: number | undefined, longitude?: number | undefined, propertyType?: string | undefined, hasCompanySeller?: boolean | undefined, matrikkel: Array<{ __typename?: 'LandIdentificationMatrix', gnr?: number | undefined, bnr?: number | undefined, snr?: number | undefined, fnr?: number | undefined, knr?: number | undefined, ownPart?: string | undefined } | undefined>, broker?: { __typename?: 'Broker', name?: string | undefined, employeeId: string, id: any, email: string, aboutMe?: string | undefined, kti?: number | undefined, employeeRoles?: Array<{ __typename?: 'BrokerRole', source?: string | undefined, typeId?: number | undefined, name?: string | undefined } | undefined> | undefined } | undefined, estatePrice?: { __typename?: 'EstatePrice', totalPrice?: number | undefined, priceSuggestion?: number | undefined, collectiveDebt?: number | undefined, soldPrice?: number | undefined } | undefined, mainBroker?: { __typename?: 'Broker', name?: string | undefined, title?: string | undefined, email: string, mobilePhone?: string | undefined, image?: { __typename?: 'BrokerImage', medium?: string | undefined } | undefined } | undefined, areaSize?: { __typename?: 'AreaSize', BRAItotal?: number | undefined } | undefined, estatePriceModel?: { __typename?: 'EstatePriceModel', priceSuggestion?: number | undefined, soldPrice?: number | undefined } | undefined, sumArea?: { __typename?: 'SumArea', bra: number, braI: number, pRom: number } | undefined, partOwnership?: { __typename?: 'PartOwnership', partName?: string | undefined, partNumber?: number | undefined, partOrgNumber?: string | undefined, estateHousingCooperativeStockHousingUnitNumber?: string | undefined, estateHousingCooperativeStockNumber?: string | undefined } | undefined, businessManagerContact?: { __typename?: 'BusinessManagerContact', companyName?: string | undefined } | undefined, brokersIdWithRoles?: Array<{ __typename?: 'BrokerIdWithRole', employeeId: string, brokerRole: number, employee: { __typename?: 'BrokerIdWithRoleDetails', title?: string | undefined, name?: string | undefined, email?: string | undefined, mobilePhone?: string | undefined, image?: { __typename?: 'BrokerImage', small?: string | undefined } | undefined } } | undefined> | undefined, landIdentificationMatrix?: { __typename?: 'LandIdentificationMatrix', gnr?: number | undefined, bnr?: number | undefined, knr?: number | undefined, snr?: number | undefined, ownPart?: string | undefined } | undefined, department?: { __typename?: 'Department', departmentId: number, departmentNumber?: number | undefined, name: string, legalName?: string | undefined, organisationNumber?: string | undefined, phone?: string | undefined, streetAddress?: string | undefined, postalCode?: string | undefined, city?: string | undefined, email?: string | undefined, employees?: Array<{ __typename?: 'DepartmentEmployee', employeeId?: string | undefined, name?: string | undefined, email?: string | undefined, mobilePhone?: string | undefined, title?: string | undefined, slug?: string | undefined } | undefined> | undefined } | undefined, address?: { __typename?: 'BrokerAddress', streetAddress?: string | undefined, city?: string | undefined, zipCode?: string | undefined, municipality?: string | undefined } | undefined, brokers?: Array<{ __typename?: 'EstateBroker', employeeId?: string | undefined, name?: string | undefined, mobilePhone?: string | undefined, email?: string | undefined, slug?: string | undefined, role?: number | undefined, title?: string | undefined, employeeRoles?: Array<{ __typename?: 'BrokerRole', source?: string | undefined, typeId?: number | undefined, name?: string | undefined } | undefined> | undefined, image?: { __typename?: 'BrokerImage', small?: string | undefined } | undefined } | undefined> | undefined, mainSeller?: { __typename?: 'BrokerEstateSeller', contactId: string, firstName?: string | undefined, lastName?: string | undefined } | undefined, sellers: Array<{ __typename?: 'BrokerEstateSeller', contactId: string, firstName?: string | undefined, lastName?: string | undefined, socialSecurityNumber?: string | undefined, email?: string | undefined, mobilePhone?: string | undefined, mainContact?: boolean | undefined, companyName?: string | undefined, contactType?: number | undefined, proxyId?: string | undefined }>, companyContacts: Array<{ __typename?: 'Contact', contactId: string, departmentId?: number | undefined, contactType: number, companyName?: string | undefined, organisationNumber?: string | undefined, firstName?: string | undefined, lastName?: string | undefined, mobilePhone?: string | undefined, privatePhone?: string | undefined, workPhone?: string | undefined, email?: string | undefined, address?: string | undefined, postalAddress?: string | undefined, postalCode?: string | undefined, city?: string | undefined, deletedAt?: string | undefined, relationName?: string | undefined, roleName?: string | undefined, relationType?: number | undefined }>, extraContacts: Array<{ __typename?: 'Contact', contactId: string, departmentId?: number | undefined, contactType: number, companyName?: string | undefined, organisationNumber?: string | undefined, firstName?: string | undefined, lastName?: string | undefined, mobilePhone?: string | undefined, privatePhone?: string | undefined, workPhone?: string | undefined, email?: string | undefined, address?: string | undefined, postalAddress?: string | undefined, postalCode?: string | undefined, city?: string | undefined, deletedAt?: string | undefined, relationName?: string | undefined, relationType?: number | undefined }>, inspectionFolder?: { __typename?: 'InspectionFolder', id: string, publishedAt?: string | undefined, listingAgreementActive: boolean, audit?: Array<{ __typename?: 'InspectionFolderAudit', id: number, sentAt?: string | undefined, listingAgreementActive?: boolean | undefined }> | undefined } | undefined, listingAgreement?: { __typename?: 'ListingAgreement', id: any, feePercentage?: number | undefined, commission?: number | undefined, suggestedPrice?: number | undefined, sentToClientAt?: string | undefined, status?: GQLListingAgreementStatus | undefined, offerSellerLink?: string | undefined, signers: Array<{ __typename?: 'Signer', id: any, externalSignerId: string, signedAt?: string | undefined, title?: string | undefined, email: string }> } | undefined, activities: Array<{ __typename?: 'EstateActivity', type?: number | undefined, start?: string | undefined, end?: string | undefined }> } | undefined };

export type GQLGetBrokerEstateDepartmentQueryVariables = Exact<{
  estateId: Scalars['String']['input'];
}>;


export type GQLGetBrokerEstateDepartmentQuery = { __typename?: 'Query', estate?: { __typename?: 'BrokerEstate', department?: { __typename?: 'Department', departmentNumber?: number | undefined, legalName?: string | undefined } | undefined, listingAgreement?: { __typename?: 'ListingAgreement', feePercentage?: number | undefined, suggestedPrice?: number | undefined, commission?: number | undefined } | undefined } | undefined };

export type GQLAgreementQueryVariables = Exact<{
  documentId: Scalars['String']['input'];
}>;


export type GQLAgreementQuery = { __typename?: 'Query', listingAgreementByDocumentId?: { __typename?: 'ListingAgreement', signers: Array<{ __typename?: 'Signer', id: any, title?: string | undefined, email: string, phone?: string | undefined, signedAt?: string | undefined, firstName?: string | undefined, lastName?: string | undefined, url?: string | undefined, externalSignerId: string }> } | undefined };

export type GQLEstateDepartmentQueryVariables = Exact<{
  estateId: Scalars['String']['input'];
}>;


export type GQLEstateDepartmentQuery = { __typename?: 'Query', estate?: { __typename?: 'BrokerEstate', departmentId?: number | undefined } | undefined };

export type GQLMarketingPackagesQueryVariables = Exact<{
  type: Scalars['String']['input'];
  publicVisible?: InputMaybe<Scalars['Boolean']['input']>;
  active: Scalars['Boolean']['input'];
}>;


export type GQLMarketingPackagesQuery = { __typename?: 'Query', marketingPackages: Array<{ __typename?: 'MarketingPackage', id: any, name?: string | undefined, shortName?: string | undefined, packageId?: number | undefined, productTag?: string | undefined, price?: number | undefined, views?: string | undefined, clicks?: string | undefined, channels: Array<{ __typename?: 'MarketingChannel', title: string, id: string }> }> };

export type GQLInspectionEventsQueryVariables = Exact<{
  estateId: Scalars['String']['input'];
}>;


export type GQLInspectionEventsQuery = { __typename?: 'Query', inspectionEvents: Array<{ __typename?: 'InspectionEvent', id: string, title?: string | undefined, start: string, end?: string | undefined, type: string }>, estate?: { __typename?: 'BrokerEstate', sellers: Array<{ __typename?: 'BrokerEstateSeller', contactId: string }> } | undefined };

export type GQLAddInspectionEventsMutationVariables = Exact<{
  estateId: Scalars['String']['input'];
  events: Array<GQLInspectionEventInput> | GQLInspectionEventInput;
}>;


export type GQLAddInspectionEventsMutation = { __typename?: 'Mutation', addInspectionEvents: boolean };

export type GQLDeleteInspectionEventMutationVariables = Exact<{
  eventId: Scalars['String']['input'];
}>;


export type GQLDeleteInspectionEventMutation = { __typename?: 'Mutation', deleteInspectionEvent: boolean };

export type GQLUpdateInspectionEventMutationVariables = Exact<{
  eventId: Scalars['String']['input'];
  event: GQLInspectionEventInput;
}>;


export type GQLUpdateInspectionEventMutation = { __typename?: 'Mutation', updateInspectionEvent: boolean };

export type GQLClearInspectionEventsMutationVariables = Exact<{
  estateId: Scalars['String']['input'];
}>;


export type GQLClearInspectionEventsMutation = { __typename?: 'Mutation', clearInspectionEvents: boolean };

export type GQLUpdateInspectionEventsMutationVariables = Exact<{
  estateId: Scalars['String']['input'];
  events: Array<GQLInspectionEventInput> | GQLInspectionEventInput;
}>;


export type GQLUpdateInspectionEventsMutation = { __typename?: 'Mutation', updateInspectionEvents: boolean };

export type GQLListingAgreementTeamQueryVariables = Exact<{
  estateId: Scalars['String']['input'];
}>;


export type GQLListingAgreementTeamQuery = { __typename?: 'Query', estate?: { __typename?: 'BrokerEstate', mainBroker?: { __typename?: 'Broker', name?: string | undefined, title?: string | undefined, instagram?: string | undefined, aboutMe?: string | undefined, mobilePhone?: string | undefined, email: string, image?: { __typename?: 'BrokerImage', medium?: string | undefined } | undefined, department?: { __typename?: 'Department', name: string, displayKtiOnEmployee?: boolean | undefined } | undefined, rating?: { __typename?: 'Rating', average?: number | undefined, count?: number | undefined } | undefined, usp?: Array<{ __typename?: 'Usp', title?: string | undefined, description?: string | undefined }> | undefined, awards?: Array<{ __typename?: 'Award', id?: string | undefined, name?: string | undefined, origin?: string | undefined, year?: number | undefined }> | undefined, nordvikAwards?: Array<{ __typename?: 'NordvikAward', awardId?: string | undefined, name?: string | undefined, origin?: string | undefined, year?: number | undefined, hidden?: boolean | undefined, private?: boolean | undefined }> | undefined, links?: { __typename?: 'BrokerProfileLinks', adLinks: Array<string>, mediaLinks: Array<string> } | undefined, featuredReviews: Array<{ __typename?: 'BrokerRating', userName?: string | undefined, rating?: number | undefined, createdAt?: string | undefined, review?: { __typename?: 'BrokerReview', text?: string | undefined } | undefined }>, fallbackReviews: Array<{ __typename?: 'BrokerRating', userName?: string | undefined, rating?: number | undefined, createdAt?: string | undefined, review?: { __typename?: 'BrokerReview', text?: string | undefined } | undefined }>, team?: Array<{ __typename?: 'BrokerPartner', id: string, name: string, instagram?: string | undefined, website?: string | undefined, description?: string | undefined, category?: string | undefined, images: Array<string>, createdAt?: string | undefined, updatedAt?: string | undefined, profilePicture?: string | undefined, hidden?: boolean | undefined }> | undefined } | undefined, assistantBroker?: { __typename?: 'Broker', employeeId: string, name?: string | undefined, title?: string | undefined, mobilePhone?: string | undefined, email: string, image?: { __typename?: 'BrokerImage', small?: string | undefined } | undefined } | undefined, inspectionFolder?: { __typename?: 'InspectionFolder', excludedEmployees: Array<string>, excludedPartners: Array<{ __typename?: 'BrokerPartner', id: string }> } | undefined } | undefined };

export type GQLUserNotificationsQueryVariables = Exact<{ [key: string]: never; }>;


export type GQLUserNotificationsQuery = { __typename?: 'Query', userNotifications: { __typename?: 'UserNotificationsResponse', totalCount: number, newsCount: number } };

export type GQLMarkNewsAsReadMutationVariables = Exact<{
  newsId: Scalars['String']['input'];
}>;


export type GQLMarkNewsAsReadMutation = { __typename?: 'Mutation', markNewsAsRead?: boolean | undefined };

export type GQLBrokersQueryVariables = Exact<{ [key: string]: never; }>;


export type GQLBrokersQuery = { __typename?: 'Query', brokers?: { __typename?: 'BrokersResponse', totalCount: number, items: Array<{ __typename?: 'Broker', id: any, employeeId: string, name?: string | undefined, email: string, employeeActive?: boolean | undefined, department?: { __typename?: 'Department', id: any, name: string, departmentId: number } | undefined, image?: { __typename?: 'BrokerImage', small?: string | undefined } | undefined }> } | undefined };

export type GQLDashboardAverageCommissionQueryVariables = Exact<{
  type: GQLDashboardType;
}>;


export type GQLDashboardAverageCommissionQuery = { __typename?: 'Query', dashboardAverageCommission: { __typename?: 'AverageCommissionResponse', departmentName: string, reference: { __typename?: 'AverageCommissionEntry', type: string, commission?: number | undefined, price: number, salesCount?: number | undefined }, compare: { __typename?: 'AverageCommissionEntry', type: string, commission?: number | undefined, price: number, salesCount?: number | undefined } } };

export type GQLDashboardExpectedRevenueQueryVariables = Exact<{
  type: GQLDashboardType;
}>;


export type GQLDashboardExpectedRevenueQuery = { __typename?: 'Query', dashboardExpectedRevenue: Array<{ __typename?: 'DashboardStatusEntry', status: number, value: number, count: number }> };

export type GQLDashboardKeyFiguresQueryVariables = Exact<{
  type: GQLDashboardType;
  period: Scalars['String']['input'];
}>;


export type GQLDashboardKeyFiguresQuery = { __typename?: 'Query', keyFigures: Array<{ __typename?: 'KeyFigureEntry', type?: GQLToplistSection | undefined, value: number, lastValue?: number | undefined, label: string, format?: string | undefined }>, dashboardLeads: { __typename?: 'DashboardLeadsResponse', budget: number, entries: Array<{ __typename?: 'LeadsEntry', name: string, value: number, departmentId?: number | undefined, budget: number, actual: number, current: boolean, employeeCount: number }> } };

export type GQLDashboardKtiQueryVariables = Exact<{ [key: string]: never; }>;


export type GQLDashboardKtiQuery = { __typename?: 'Query', dashboardKti: { __typename?: 'DashboardKtiResponse', current: { __typename?: 'DashboardKtiCurrent', name: string, value: number, ratings: number }, others: Array<{ __typename?: 'DashboardKtiOthers', name: string, value: number }> } };

export type GQLDashboardRevenueQueryVariables = Exact<{
  type: GQLDashboardType;
}>;


export type GQLDashboardRevenueQuery = { __typename?: 'Query', dashboardRevenue: { __typename?: 'RevenueResponse', currentYearTotal: number, percentageChange: number, hitBudgetPercentage: number, budgetTotal: number, currentYearBudget: number, previousYearTotal?: number | undefined, previousYearUntilNow?: number | undefined, entries: Array<{ __typename?: 'RevenueMonth', month: number, current?: { __typename?: 'RevenueEntry', year: number, value: number } | undefined, budget?: { __typename?: 'RevenueEntry', year: number, value: number } | undefined, previous: { __typename?: 'RevenueEntry', year: number, value: number } }> } };

export type GQLDashboardToplistQueryVariables = Exact<{
  section: GQLToplistSection;
  type: GQLDashboardType;
}>;


export type GQLDashboardToplistQuery = { __typename?: 'Query', dashboardToplist: { __typename?: 'DashboardToplistResponse', section: GQLToplistSection, current: { __typename?: 'ToplistEntryCurrent', name: string, value: number, imageUrl?: string | undefined, position?: number | undefined }, topEntries: Array<{ __typename?: 'ToplistEntry', name: string, value: number, imageUrl?: string | undefined }> } };

export type GQLDashboardUpcomingActivitiesQueryVariables = Exact<{
  type: GQLDashboardType;
}>;


export type GQLDashboardUpcomingActivitiesQuery = { __typename?: 'Query', dashboardUpcomingActivities: Array<{ __typename?: 'DashboardActivity', start?: string | undefined, end?: string | undefined, type?: number | undefined, typeName?: string | undefined, name?: string | undefined, performedById?: string | undefined, value?: string | undefined, done?: boolean | undefined, id?: string | undefined, estateId: string, estateAddress?: string | undefined }> };

export type GQLDashboardImportantTasksQueryVariables = Exact<{ [key: string]: never; }>;


export type GQLDashboardImportantTasksQuery = { __typename?: 'Query', dashboardImportantTasks: Array<{ __typename?: 'ImportantTask', type: GQLImportantTaskType, estateId: string, estateAddress: string, mainBrokerId: string, signUrl?: string | undefined, eiendomsverdiUrl?: string | undefined, amlUrl?: string | undefined }> };

export type GQLIncidentFragment = { __typename?: 'CraftCmsIncident', id: string, slug: string, title: string, postDate: string, resolved: boolean, resolvedDate?: string | undefined, resolvedComment?: string | undefined, level: GQLCraftCmsIncidentLevel, active: boolean, updates?: Array<{ __typename?: 'CraftCmsIncidentUpdate', time: string, text: string }> | undefined };

export type GQLIncidentsQueryVariables = Exact<{
  active?: InputMaybe<Scalars['Boolean']['input']>;
}>;


export type GQLIncidentsQuery = { __typename?: 'Query', cmsIncidents: { __typename?: 'CmsIncidentsResponse', items: Array<{ __typename?: 'CraftCmsIncident', id: string, slug: string, title: string, postDate: string, resolved: boolean, resolvedDate?: string | undefined, resolvedComment?: string | undefined, level: GQLCraftCmsIncidentLevel, active: boolean, updates?: Array<{ __typename?: 'CraftCmsIncidentUpdate', time: string, text: string }> | undefined }>, meta: { __typename?: 'CmsMeta', total: number, currentPage: number, totalPages: number } } };

export type GQLIncidentBySlugQueryVariables = Exact<{
  slug: Scalars['String']['input'];
}>;


export type GQLIncidentBySlugQuery = { __typename?: 'Query', cmsIncidentBySlug?: { __typename?: 'CraftCmsIncident', id: string, slug: string, title: string, postDate: string, resolved: boolean, resolvedDate?: string | undefined, resolvedComment?: string | undefined, level: GQLCraftCmsIncidentLevel, active: boolean, updates?: Array<{ __typename?: 'CraftCmsIncidentUpdate', time: string, text: string }> | undefined } | undefined };

export type GQLKtiPageRatingsQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  dateFrom?: InputMaybe<Scalars['DateTime']['input']>;
  dateTo?: InputMaybe<Scalars['DateTime']['input']>;
  hasReview?: InputMaybe<Scalars['Boolean']['input']>;
  sortBy?: InputMaybe<GQLBrokerRatingSortBy>;
  sortDir?: InputMaybe<GQLSortDirection>;
  featured?: InputMaybe<Scalars['Boolean']['input']>;
  rating?: InputMaybe<Scalars['Int']['input']>;
}>;


export type GQLKtiPageRatingsQuery = { __typename?: 'Query', currentBrokerRatings?: { __typename?: 'BrokerRatingsList', data: Array<{ __typename?: 'BrokerRating', ratingId?: number | undefined, createdAt?: string | undefined, employeeId?: string | undefined, rating?: number | undefined, userName?: string | undefined, featured?: boolean | undefined, review?: { __typename?: 'BrokerReview', reviewId?: number | undefined, text?: string | undefined, createdAt?: string | undefined } | undefined } | undefined>, pagination: { __typename?: 'Pagination', count?: number | undefined, limit?: number | undefined, total?: number | undefined, offset?: number | undefined } } | undefined };

export type GQLBrokerRatingsTotalQueryVariables = Exact<{
  dateFrom?: InputMaybe<Scalars['DateTime']['input']>;
  dateTo?: InputMaybe<Scalars['DateTime']['input']>;
  hasReview?: InputMaybe<Scalars['Boolean']['input']>;
  featured?: InputMaybe<Scalars['Boolean']['input']>;
  rating?: InputMaybe<Scalars['Int']['input']>;
}>;


export type GQLBrokerRatingsTotalQuery = { __typename?: 'Query', currentBrokerRatingsTotal?: { __typename?: 'CurrentBrokerRatingsTotal', allDates?: number | undefined, allRatings?: number | undefined, fiveStar?: number | undefined, fourStar?: number | undefined, threeStar?: number | undefined, twoStar?: number | undefined, oneStar?: number | undefined, withReview?: number | undefined, last30Days?: number | undefined, lastYear?: number | undefined, currentYear?: number | undefined } | undefined };

export type GQLKtiPageBrokerRatingsCurrentYearQueryVariables = Exact<{ [key: string]: never; }>;


export type GQLKtiPageBrokerRatingsCurrentYearQuery = { __typename?: 'Query', currentBroker?: { __typename?: 'Broker', rating?: { __typename?: 'Rating', weighted?: number | undefined } | undefined } | undefined, ratings: { __typename?: 'RatingResponse', average?: number | undefined, count?: number | undefined } };

export type GQLKtiPageAllSectionsQueryVariables = Exact<{ [key: string]: never; }>;


export type GQLKtiPageAllSectionsQuery = { __typename?: 'Query', currentBrokerKti: { __typename?: 'BrokerKtiResponse', broker?: { __typename?: 'KtiResponse', name?: string | undefined, averageRating?: number | undefined, ratingCount?: string | undefined, reviewCount?: number | undefined } | undefined, brokerDepartment?: { __typename?: 'KtiResponse', name?: string | undefined, averageRating?: number | undefined, ratingCount?: string | undefined, reviewCount?: number | undefined } | undefined, nordvik?: { __typename?: 'KtiResponse', name?: string | undefined, averageRating?: number | undefined, ratingCount?: string | undefined, reviewCount?: number | undefined } | undefined } };

export type GQLJobsListingQueryVariables = Exact<{ [key: string]: never; }>;


export type GQLJobsListingQuery = { __typename?: 'Query', cmsJobsListing: { __typename?: 'CraftCmsJobsListingResponse', items: Array<{ __typename?: 'CraftCmsJobListingItem', id: string, slug: string, title: string, excerpt?: string | undefined, postDate: string, image?: { __typename?: 'CmsArticleImage', small?: string | undefined, medium?: string | undefined, large?: string | undefined } | undefined, author?: { __typename?: 'CmsArticleAuthor', name?: string | undefined, email?: string | undefined, avatarUrl?: string | undefined } | undefined }>, meta: { __typename?: 'CmsMeta', total: number, currentPage: number, totalPages: number } } };

export type GQLJobListingBySlugQueryVariables = Exact<{
  slug: Scalars['String']['input'];
}>;


export type GQLJobListingBySlugQuery = { __typename?: 'Query', cmsJobBySlug?: { __typename?: 'CraftCmsJob', id: string, slug: string, title: string, excerpt?: string | undefined, postDate: string, image?: { __typename?: 'CmsArticleImage', small?: string | undefined, medium?: string | undefined, large?: string | undefined } | undefined, author?: { __typename?: 'CmsArticleAuthor', name?: string | undefined, email?: string | undefined, avatarUrl?: string | undefined } | undefined, modules?: Array<{ __typename?: 'Module', type: string, body?: string | undefined } | undefined> | undefined } | undefined };

export type GQLArticleCategoryFragment = { __typename?: 'CmsArticleCategory', id: string, slug: string, title: string };

export type GQLArticlePreviewFragment = { __typename?: 'CmsArticle', id: string, slug: string, title?: string | undefined, postDate?: string | undefined, externalUrl?: string | undefined, excerpt?: string | undefined, author?: { __typename?: 'CmsArticleAuthor', name?: string | undefined, email?: string | undefined, avatarUrl?: string | undefined } | undefined, categories: Array<{ __typename?: 'CmsArticleCategory', id: string, slug: string, title: string }>, departments?: Array<{ __typename?: 'CmsArticleDepartment', departmentId?: number | undefined, title?: string | undefined }> | undefined, targetRoles: Array<{ __typename?: 'TargetRole', id?: number | undefined, slug?: string | undefined, title?: string | undefined, roleTypeId?: number | undefined }>, image?: { __typename?: 'CmsArticleImage', small?: string | undefined, medium?: string | undefined, large?: string | undefined } | undefined, modules?: Array<{ __typename?: 'Module', type: string, body?: string | undefined, accordion?: Array<{ __typename?: 'Accordion', header?: string | undefined, text?: string | undefined } | undefined> | undefined } | undefined> | undefined };

export type GQLArticlesListFragment = { __typename?: 'CmsArticle', id: string, slug: string, title?: string | undefined, postDate?: string | undefined, externalUrl?: string | undefined, excerpt?: string | undefined, author?: { __typename?: 'CmsArticleAuthor', name?: string | undefined, email?: string | undefined, avatarUrl?: string | undefined } | undefined, categories: Array<{ __typename?: 'CmsArticleCategory', id: string, slug: string, title: string }>, departments?: Array<{ __typename?: 'CmsArticleDepartment', departmentId?: number | undefined, title?: string | undefined }> | undefined, targetRoles: Array<{ __typename?: 'TargetRole', id?: number | undefined, slug?: string | undefined, title?: string | undefined, roleTypeId?: number | undefined }>, image?: { __typename?: 'CmsArticleImage', small?: string | undefined, medium?: string | undefined, large?: string | undefined } | undefined };

export type GQLArticlesMetaFragment = { __typename?: 'CmsMeta', total: number, currentPage: number, totalPages: number };

export type GQLCmsArticlesQueryVariables = Exact<{
  categorySlug?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<GQLCmsArticleType>;
  includeViewerHasRead: Scalars['Boolean']['input'];
  searchQuery?: InputMaybe<Scalars['String']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
}>;


export type GQLCmsArticlesQuery = { __typename?: 'Query', cmsArticles: { __typename?: 'CmsArticleResponse', meta: { __typename?: 'CmsMeta', total: number, currentPage: number, totalPages: number }, items: Array<{ __typename?: 'CmsArticle', viewerHasRead?: boolean | undefined, id: string, slug: string, title?: string | undefined, postDate?: string | undefined, externalUrl?: string | undefined, excerpt?: string | undefined, author?: { __typename?: 'CmsArticleAuthor', name?: string | undefined, email?: string | undefined, avatarUrl?: string | undefined } | undefined, categories: Array<{ __typename?: 'CmsArticleCategory', id: string, slug: string, title: string }>, departments?: Array<{ __typename?: 'CmsArticleDepartment', departmentId?: number | undefined, title?: string | undefined }> | undefined, targetRoles: Array<{ __typename?: 'TargetRole', id?: number | undefined, slug?: string | undefined, title?: string | undefined, roleTypeId?: number | undefined }>, image?: { __typename?: 'CmsArticleImage', small?: string | undefined, medium?: string | undefined, large?: string | undefined } | undefined }> } };

export type GQLCmsArticleCategoriesQueryVariables = Exact<{
  type?: InputMaybe<GQLCmsArticleType>;
}>;


export type GQLCmsArticleCategoriesQuery = { __typename?: 'Query', cmsArticleCategories: Array<{ __typename?: 'CmsArticleCategory', id: string, slug: string, title: string }> };

export type GQLArticleItemQueryVariables = Exact<{
  slug: Scalars['String']['input'];
}>;


export type GQLArticleItemQuery = { __typename?: 'Query', cmsArticleBySlug?: { __typename?: 'CmsArticle', id: string, slug: string, title?: string | undefined, postDate?: string | undefined, externalUrl?: string | undefined, excerpt?: string | undefined, author?: { __typename?: 'CmsArticleAuthor', name?: string | undefined, email?: string | undefined, avatarUrl?: string | undefined } | undefined, categories: Array<{ __typename?: 'CmsArticleCategory', id: string, slug: string, title: string }>, departments?: Array<{ __typename?: 'CmsArticleDepartment', departmentId?: number | undefined, title?: string | undefined }> | undefined, targetRoles: Array<{ __typename?: 'TargetRole', id?: number | undefined, slug?: string | undefined, title?: string | undefined, roleTypeId?: number | undefined }>, image?: { __typename?: 'CmsArticleImage', small?: string | undefined, medium?: string | undefined, large?: string | undefined } | undefined, modules?: Array<{ __typename?: 'Module', type: string, body?: string | undefined, accordion?: Array<{ __typename?: 'Accordion', header?: string | undefined, text?: string | undefined } | undefined> | undefined } | undefined> | undefined } | undefined };

export type GQLChangelogPreviewFragment = { __typename?: 'CmsArticle', id: string, slug: string, title?: string | undefined, postDate?: string | undefined, externalUrl?: string | undefined, author?: { __typename?: 'CmsArticleAuthor', name?: string | undefined, email?: string | undefined, avatarUrl?: string | undefined } | undefined, categories: Array<{ __typename?: 'CmsArticleCategory', id: string, slug: string, title: string }>, departments?: Array<{ __typename?: 'CmsArticleDepartment', departmentId?: number | undefined, title?: string | undefined }> | undefined, targetRoles: Array<{ __typename?: 'TargetRole', id?: number | undefined, slug?: string | undefined, title?: string | undefined, roleTypeId?: number | undefined }>, image?: { __typename?: 'CmsArticleImage', small?: string | undefined, medium?: string | undefined, large?: string | undefined } | undefined, modules?: Array<{ __typename?: 'Module', type: string, body?: string | undefined, accordion?: Array<{ __typename?: 'Accordion', header?: string | undefined, text?: string | undefined } | undefined> | undefined } | undefined> | undefined };

export type GQLCmsChangelogsQueryVariables = Exact<{
  categorySlug?: InputMaybe<Scalars['String']['input']>;
  searchQuery?: InputMaybe<Scalars['String']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  dateFrom?: InputMaybe<Scalars['DateTime']['input']>;
  dateTo?: InputMaybe<Scalars['DateTime']['input']>;
}>;


export type GQLCmsChangelogsQuery = { __typename?: 'Query', cmsChangelogs: { __typename?: 'CmsArticleResponse', meta: { __typename?: 'CmsMeta', total: number, currentPage: number, totalPages: number }, items: Array<{ __typename?: 'CmsArticle', id: string, slug: string, title?: string | undefined, postDate?: string | undefined, externalUrl?: string | undefined, author?: { __typename?: 'CmsArticleAuthor', name?: string | undefined, email?: string | undefined, avatarUrl?: string | undefined } | undefined, categories: Array<{ __typename?: 'CmsArticleCategory', id: string, slug: string, title: string }>, departments?: Array<{ __typename?: 'CmsArticleDepartment', departmentId?: number | undefined, title?: string | undefined }> | undefined, targetRoles: Array<{ __typename?: 'TargetRole', id?: number | undefined, slug?: string | undefined, title?: string | undefined, roleTypeId?: number | undefined }>, image?: { __typename?: 'CmsArticleImage', small?: string | undefined, medium?: string | undefined, large?: string | undefined } | undefined, modules?: Array<{ __typename?: 'Module', type: string, body?: string | undefined, accordion?: Array<{ __typename?: 'Accordion', header?: string | undefined, text?: string | undefined } | undefined> | undefined } | undefined> | undefined }> }, readArticles: Array<{ __typename?: 'ReadArticle', id: string, readAt: string }> };

export type GQLCmsChangelogsCategoriesQueryVariables = Exact<{ [key: string]: never; }>;


export type GQLCmsChangelogsCategoriesQuery = { __typename?: 'Query', cmsArticleCategories: Array<{ __typename?: 'CmsArticleCategory', id: string, slug: string, title: string }> };

export type GQLChangelogItemQueryVariables = Exact<{
  slug: Scalars['String']['input'];
}>;


export type GQLChangelogItemQuery = { __typename?: 'Query', cmsArticleBySlug?: { __typename?: 'CmsArticle', id: string, slug: string, title?: string | undefined, postDate?: string | undefined, externalUrl?: string | undefined, excerpt?: string | undefined, author?: { __typename?: 'CmsArticleAuthor', name?: string | undefined, email?: string | undefined, avatarUrl?: string | undefined } | undefined, categories: Array<{ __typename?: 'CmsArticleCategory', id: string, slug: string, title: string }>, departments?: Array<{ __typename?: 'CmsArticleDepartment', departmentId?: number | undefined, title?: string | undefined }> | undefined, targetRoles: Array<{ __typename?: 'TargetRole', id?: number | undefined, slug?: string | undefined, title?: string | undefined, roleTypeId?: number | undefined }>, image?: { __typename?: 'CmsArticleImage', small?: string | undefined, medium?: string | undefined, large?: string | undefined } | undefined, modules?: Array<{ __typename?: 'Module', type: string, body?: string | undefined, accordion?: Array<{ __typename?: 'Accordion', header?: string | undefined, text?: string | undefined } | undefined> | undefined } | undefined> | undefined } | undefined };

export type GQLEstateByIdQueryVariables = Exact<{
  id: Scalars['String']['input'];
}>;


export type GQLEstateByIdQuery = { __typename?: 'Query', estate?: { __typename?: 'BrokerEstate', status: number, id: any, estateId: string, latitude?: number | undefined, longitude?: number | undefined, assignmentNumber?: string | undefined, assignmentType?: number | undefined, isValuation?: boolean | undefined, isEtakstPublished?: boolean | undefined, ownAssignmentType?: string | undefined, noOfBedRooms?: number | undefined, noOfRooms?: number | undefined, soldDate?: string | undefined, commissionAcceptedDate?: string | undefined, linkToNext?: string | undefined, placeholderImage?: string | undefined, hjemUrl?: string | undefined, address?: { __typename?: 'BrokerAddress', streetAddress?: string | undefined, city?: string | undefined, zipCode?: string | undefined, municipality?: string | undefined } | undefined, finn?: { __typename?: 'FinnData', finnExpireDate?: string | undefined, finnPublishDate?: string | undefined } | undefined, estatePrice?: { __typename?: 'EstatePrice', totalPrice?: number | undefined, priceSuggestion?: number | undefined, soldPrice?: number | undefined } | undefined, sumArea?: { __typename?: 'SumArea', braI: number, pRom: number } | undefined, areaSize?: { __typename?: 'AreaSize', BRAItotal?: number | undefined } | undefined, mainImage?: { __typename?: 'BrokerEstateImage', large?: string | undefined } | undefined, links: Array<{ __typename?: 'BrokerEstateLink', linkType?: number | undefined, url?: string | undefined, text?: string | undefined }>, sellers: Array<{ __typename?: 'BrokerEstateSeller', firstName?: string | undefined, lastName?: string | undefined, email?: string | undefined, mobilePhone?: string | undefined, mainContact?: boolean | undefined, contactId: string }>, showings: Array<{ __typename?: 'BrokerEstateShowing', start?: string | undefined, end?: string | undefined, showingId?: string | undefined }>, activities: Array<{ __typename?: 'EstateActivity', start?: string | undefined, end?: string | undefined, type?: number | undefined, typeName?: string | undefined, name?: string | undefined, performedById?: string | undefined, done?: boolean | undefined, id?: string | undefined, value?: string | undefined }>, upcomingEvents: Array<{ __typename?: 'EstateActivity', start?: string | undefined, end?: string | undefined, type?: number | undefined, typeName?: string | undefined, name?: string | undefined, performedById?: string | undefined, done?: boolean | undefined, id?: string | undefined }>, inspectionFolder?: { __typename?: 'InspectionFolder', id: string, publishedAt?: string | undefined } | undefined, listingAgreement?: { __typename?: 'ListingAgreement', id: any, updatedAt?: string | undefined, createdAt?: string | undefined, signicatDocumentId?: string | undefined, feePercentage?: number | undefined, suggestedPrice?: number | undefined, deadline?: string | undefined, signedAt?: string | undefined, initiatedSigningAt?: string | undefined, sentToClientAt?: string | undefined, status?: GQLListingAgreementStatus | undefined, deadlineHasBeenExceeded?: boolean | undefined, commission?: number | undefined, signers: Array<{ __typename?: 'Signer', id: any, externalSignerId: string, url?: string | undefined, signedAt?: string | undefined, title?: string | undefined, email: string, firstName?: string | undefined, lastName?: string | undefined }> } | undefined, brokers?: Array<{ __typename?: 'EstateBroker', employeeId?: string | undefined, name?: string | undefined, mobilePhone?: string | undefined, email?: string | undefined, slug?: string | undefined, role?: number | undefined, title?: string | undefined, employeeRoles?: Array<{ __typename?: 'BrokerRole', source?: string | undefined, typeId?: number | undefined, name?: string | undefined } | undefined> | undefined, image?: { __typename?: 'BrokerImage', small?: string | undefined } | undefined } | undefined> | undefined, brokersIdWithRoles?: Array<{ __typename?: 'BrokerIdWithRole', employeeId: string, brokerRole: number, employee: { __typename?: 'BrokerIdWithRoleDetails', title?: string | undefined, name?: string | undefined, email?: string | undefined, mobilePhone?: string | undefined, image?: { __typename?: 'BrokerImage', small?: string | undefined } | undefined } } | undefined> | undefined } | undefined };

export type GQLEstateCampaignsByIdQueryVariables = Exact<{
  id: Scalars['String']['input'];
}>;


export type GQLEstateCampaignsByIdQuery = { __typename?: 'Query', estate?: { __typename?: 'BrokerEstate', campaigns: Array<{ __typename?: 'BrokerEstateCampaign', packageName?: string | undefined, marketingPackage?: string | undefined, dateOrdered?: string | undefined, orderStartDate?: string | undefined, orderEndDate?: string | undefined, externalId?: string | undefined }> } | undefined };

export type GQLEstateExtrasByIdQueryVariables = Exact<{
  id: Scalars['String']['input'];
  estateProps?: InputMaybe<GQLEstateProps>;
}>;


export type GQLEstateExtrasByIdQuery = { __typename?: 'Query', forms: Array<{ __typename?: 'BrokerEstateForm', type?: string | undefined, name?: string | undefined, link?: string | undefined, status?: { __typename?: 'BrokerEstateFormStatus', signingFinished?: boolean | undefined, isNotificationSent?: boolean | undefined } | undefined, relevantForEstateWithProps?: { __typename?: 'RelevantForEstateWithProps', status?: number | undefined, projectRelation?: number | undefined } | undefined }>, oa?: { __typename?: 'ListingAgreement', id: any, createdAt?: string | undefined, status?: GQLListingAgreementStatus | undefined, signicatDocumentId?: string | undefined, signedAt?: string | undefined, sentToClientAt?: string | undefined, initiatedSigningAt?: string | undefined, deadline?: string | undefined, brokerSigners?: Array<{ __typename?: 'Signer', id: any, externalSignerId: string, url?: string | undefined, signedAt?: string | undefined, title?: string | undefined, email: string, phone?: string | undefined, firstName?: string | undefined, lastName?: string | undefined }> | undefined, sellerSigners?: Array<{ __typename?: 'Signer', id: any, externalSignerId: string, url?: string | undefined, signedAt?: string | undefined, title?: string | undefined, email: string, phone?: string | undefined, firstName?: string | undefined, lastName?: string | undefined }> | undefined, accessTokens?: Array<{ __typename?: 'AccessToken', id: any, createdAt?: string | undefined } | undefined> | undefined } | undefined };

export type GQLEstateFormsByIdQueryVariables = Exact<{
  id: Scalars['String']['input'];
}>;


export type GQLEstateFormsByIdQuery = { __typename?: 'Query', estate?: { __typename?: 'BrokerEstate', forms: Array<{ __typename?: 'BrokerEstateForm', type?: string | undefined, name?: string | undefined, link?: string | undefined, status?: { __typename?: 'BrokerEstateFormStatus', signingFinished?: boolean | undefined, isNotificationSent?: boolean | undefined } | undefined, relevantForEstateWithProps?: { __typename?: 'RelevantForEstateWithProps', status?: number | undefined, projectRelation?: number | undefined } | undefined }> } | undefined };

export type GQLEstateInspectionByIdQueryVariables = Exact<{
  id: Scalars['String']['input'];
}>;


export type GQLEstateInspectionByIdQuery = { __typename?: 'Query', estate?: { __typename?: 'BrokerEstate', hasInspection?: boolean | undefined, inspection?: { __typename?: 'Inspection', success?: boolean | undefined, entries: Array<{ __typename?: 'InspectionEntry', id: string, title: string, url: string, postDate?: { __typename?: 'PostDate', date: string, timezone_type: number, timezone: string } | undefined }> } | undefined } | undefined };

export type GQLSyncEstateWithVitecQueryVariables = Exact<{
  estateId: Scalars['String']['input'];
}>;


export type GQLSyncEstateWithVitecQuery = { __typename?: 'Query', syncEstateWithVitec: boolean };

export type GQLEstateResetFormMutationVariables = Exact<{
  estateId: Scalars['String']['input'];
  formType: Scalars['String']['input'];
}>;


export type GQLEstateResetFormMutation = { __typename?: 'Mutation', resetForm?: boolean | undefined };

export type GQLAgreementAndInspectionQueryVariables = Exact<{
  estateId: Scalars['String']['input'];
}>;


export type GQLAgreementAndInspectionQuery = { __typename?: 'Query', estate?: { __typename?: 'BrokerEstate', ownershipType?: string | undefined, estateId: string, hasCompanySeller?: boolean | undefined, inspectionDate?: string | undefined, estateTypeExternal?: number | undefined, isValuation?: boolean | undefined, ownership?: number | undefined, estateType?: string | undefined, estateTypeId?: string | undefined, status: number, linkToNext?: string | undefined, matrikkel: Array<{ __typename?: 'LandIdentificationMatrix', gnr?: number | undefined, bnr?: number | undefined, snr?: number | undefined, ownPart?: string | undefined } | undefined>, landIdentificationMatrix?: { __typename?: 'LandIdentificationMatrix', gnr?: number | undefined, bnr?: number | undefined, knr?: number | undefined, snr?: number | undefined, ownPart?: string | undefined } | undefined, partOwnership?: { __typename?: 'PartOwnership', partName?: string | undefined, partNumber?: number | undefined, partOrgNumber?: string | undefined, estateHousingCooperativeStockHousingUnitNumber?: string | undefined, estateHousingCooperativeStockNumber?: string | undefined } | undefined, estatePrice?: { __typename?: 'EstatePrice', priceSuggestion?: number | undefined, collectiveDebt?: number | undefined } | undefined, estatePriceModel?: { __typename?: 'EstatePriceModel', collectiveDebt?: number | undefined, estimatedValue?: number | undefined, priceSuggestion?: number | undefined, totalPrice?: number | undefined } | undefined, department?: { __typename?: 'Department', departmentId: number, departmentNumber?: number | undefined, name: string, legalName?: string | undefined, organisationNumber?: string | undefined, phone?: string | undefined, streetAddress?: string | undefined, postalCode?: string | undefined, city?: string | undefined, email?: string | undefined, employees?: Array<{ __typename?: 'DepartmentEmployee', employeeId?: string | undefined, name?: string | undefined, email?: string | undefined, mobilePhone?: string | undefined, title?: string | undefined, slug?: string | undefined } | undefined> | undefined } | undefined, address?: { __typename?: 'BrokerAddress', streetAddress?: string | undefined, city?: string | undefined, zipCode?: string | undefined, municipality?: string | undefined } | undefined, activities: Array<{ __typename?: 'EstateActivity', start?: string | undefined, end?: string | undefined, type?: number | undefined, typeName?: string | undefined, name?: string | undefined, performedById?: string | undefined, done?: boolean | undefined, id?: string | undefined, value?: string | undefined }>, inspectionFolder?: { __typename?: 'InspectionFolder', id: string, sentAt?: string | undefined, publishedAt?: string | undefined, listingAgreementSentAt?: string | undefined, listingAgreementActive: boolean, audit?: Array<{ __typename?: 'InspectionFolderAudit', id: number, sentAt?: string | undefined, listingAgreementActive?: boolean | undefined }> | undefined } | undefined, listingAgreement?: { __typename?: 'ListingAgreement', id: any, updatedAt?: string | undefined, createdAt?: string | undefined, signicatDocumentId?: string | undefined, feePercentage?: number | undefined, suggestedPrice?: number | undefined, deadline?: string | undefined, signedAt?: string | undefined, initiatedSigningAt?: string | undefined, sentToClientAt?: string | undefined, status?: GQLListingAgreementStatus | undefined, canStartSigning?: boolean | undefined, offerSellerLink?: string | undefined, deadlineHasBeenExceeded?: boolean | undefined, commission?: number | undefined, hasStorebrandLead: boolean, signers: Array<{ __typename?: 'Signer', id: any, externalSignerId: string, url?: string | undefined, signedAt?: string | undefined, title?: string | undefined, email: string, firstName?: string | undefined, lastName?: string | undefined }>, interactions: Array<{ __typename?: 'ListingAgreementInteraction', id: number, eventType: GQLListingAgreementInteractionType, eventTimestamp: string }> } | undefined, mainSeller?: { __typename?: 'BrokerEstateSeller', contactId: string, firstName?: string | undefined, lastName?: string | undefined } | undefined, sellers: Array<{ __typename?: 'BrokerEstateSeller', firstName?: string | undefined, lastName?: string | undefined, email?: string | undefined, mobilePhone?: string | undefined, mainContact?: boolean | undefined, contactId: string, contactType?: number | undefined, proxyId?: string | undefined }>, companyContacts: Array<{ __typename?: 'Contact', contactId: string, departmentId?: number | undefined, contactType: number, companyName?: string | undefined, organisationNumber?: string | undefined, firstName?: string | undefined, lastName?: string | undefined, mobilePhone?: string | undefined, privatePhone?: string | undefined, workPhone?: string | undefined, email?: string | undefined, address?: string | undefined, postalAddress?: string | undefined, postalCode?: string | undefined, city?: string | undefined, deletedAt?: string | undefined, relationName?: string | undefined, roleName?: string | undefined, relationType?: number | undefined }>, extraContacts: Array<{ __typename?: 'Contact', contactId: string, departmentId?: number | undefined, contactType: number, companyName?: string | undefined, organisationNumber?: string | undefined, firstName?: string | undefined, lastName?: string | undefined, mobilePhone?: string | undefined, privatePhone?: string | undefined, workPhone?: string | undefined, email?: string | undefined, address?: string | undefined, postalAddress?: string | undefined, postalCode?: string | undefined, city?: string | undefined, deletedAt?: string | undefined, relationName?: string | undefined, relationType?: number | undefined }>, broker?: { __typename?: 'Broker', id: any, employeeId: string, name?: string | undefined, email: string } | undefined, brokers?: Array<{ __typename?: 'EstateBroker', employeeId?: string | undefined, name?: string | undefined, mobilePhone?: string | undefined, email?: string | undefined, slug?: string | undefined, role?: number | undefined, title?: string | undefined, employeeRoles?: Array<{ __typename?: 'BrokerRole', source?: string | undefined, typeId?: number | undefined, name?: string | undefined } | undefined> | undefined, image?: { __typename?: 'BrokerImage', small?: string | undefined } | undefined } | undefined> | undefined, brokersIdWithRoles?: Array<{ __typename?: 'BrokerIdWithRole', employeeId: string, brokerRole: number, employee: { __typename?: 'BrokerIdWithRoleDetails', title?: string | undefined, name?: string | undefined, email?: string | undefined, mobilePhone?: string | undefined, image?: { __typename?: 'BrokerImage', small?: string | undefined } | undefined } } | undefined> | undefined } | undefined };

export type GQLCombinedInteractionsQueryVariables = Exact<{
  estateId: Scalars['String']['input'];
  includeSubPages?: InputMaybe<Scalars['Boolean']['input']>;
}>;


export type GQLCombinedInteractionsQuery = { __typename?: 'Query', estate?: { __typename?: 'BrokerEstate', estateId: string, hasCompanySeller?: boolean | undefined, isValuation?: boolean | undefined, sellers: Array<{ __typename?: 'BrokerEstateSeller', firstName?: string | undefined, lastName?: string | undefined, email?: string | undefined, mobilePhone?: string | undefined, mainContact?: boolean | undefined, contactId: string, proxyId?: string | undefined, proxy?: { __typename?: 'SellerProxy', contactId: string, firstName?: string | undefined } | undefined }>, companyContacts: Array<{ __typename?: 'Contact', contactId: string, firstName?: string | undefined, lastName?: string | undefined, email?: string | undefined, mobilePhone?: string | undefined }>, extraContacts: Array<{ __typename?: 'Contact', contactId: string, firstName?: string | undefined, lastName?: string | undefined, email?: string | undefined, mobilePhone?: string | undefined, deletedAt?: string | undefined }>, inspectionFolder?: { __typename?: 'InspectionFolder', id: string, audit?: Array<{ __typename?: 'InspectionFolderAudit', id: number, recipientContactIds: Array<string>, listingAgreementActive?: boolean | undefined, sentAt?: string | undefined, channels: Array<string>, extraData?: any | undefined, templateId?: string | undefined, modifiedTemplate?: boolean | undefined, emailAuditId?: string | undefined, recipients?: Array<{ __typename?: 'AuditRecipient', contactId?: string | undefined, email?: string | undefined, mobilePhone?: string | undefined, lastName?: string | undefined, firstName?: string | undefined }> | undefined, sentBy?: { __typename?: 'Broker', name?: string | undefined } | undefined }> | undefined } | undefined, listingAgreement?: { __typename?: 'ListingAgreement', id: any, deadline?: string | undefined, status?: GQLListingAgreementStatus | undefined, deadlineHasBeenExceeded?: boolean | undefined, sellerSigners?: Array<{ __typename?: 'Signer', id: any, externalSignerId: string, url?: string | undefined, signedAt?: string | undefined, title?: string | undefined, email: string, firstName?: string | undefined, lastName?: string | undefined }> | undefined, brokerSigners?: Array<{ __typename?: 'Signer', id: any, externalSignerId: string, signedAt?: string | undefined }> | undefined } | undefined } | undefined, listingAgreementInteractionsForEstate: Array<{ __typename?: 'ListingAgreementInteraction', id: number, listingAgreementsId: string, sellerId?: string | undefined, eventType: GQLListingAgreementInteractionType, eventTimestamp: string, extraData?: any | undefined, name?: string | undefined, seller?: { __typename?: 'ListingAgreementInteractionSeller', firstName?: string | undefined, lastName?: string | undefined } | undefined }>, emailInteractionsForEstate: Array<{ __typename?: 'EmailInteraction', id: string, eventType: string, eventTimestamp: string, recipientEmail: string, contactId?: string | undefined, messageId: string, openCount?: number | undefined, emailAuditId: string, bounceType?: string | undefined, rejectReason?: string | undefined }>, pageVisits: Array<{ __typename?: 'PageVisit', id: number, contactId?: string | undefined, contactName?: string | undefined, source?: string | undefined, employeeId?: string | undefined, totalTimeSpent: number, lastHeartbeat: string, startTime: string, endTime?: string | undefined, pageId: string, browser?: string | undefined, userAgent?: { __typename?: 'UserAgent', os?: { __typename?: 'UserAgentOS', name?: string | undefined } | undefined, browser?: { __typename?: 'UserAgentBrowser', name?: string | undefined } | undefined } | undefined }> };

export type GQLListingAgreementInteractionsQueryVariables = Exact<{
  estateId: Scalars['String']['input'];
}>;


export type GQLListingAgreementInteractionsQuery = { __typename?: 'Query', estate?: { __typename?: 'BrokerEstate', estateId: string, hasCompanySeller?: boolean | undefined, sellers: Array<{ __typename?: 'BrokerEstateSeller', firstName?: string | undefined, lastName?: string | undefined, email?: string | undefined, mobilePhone?: string | undefined, mainContact?: boolean | undefined, contactId: string, proxyId?: string | undefined, proxy?: { __typename?: 'SellerProxy', contactId: string, firstName?: string | undefined } | undefined }>, companyContacts: Array<{ __typename?: 'Contact', contactId: string, firstName?: string | undefined, lastName?: string | undefined, email?: string | undefined, mobilePhone?: string | undefined }>, extraContacts: Array<{ __typename?: 'Contact', firstName?: string | undefined, lastName?: string | undefined, email?: string | undefined, mobilePhone?: string | undefined, contactId: string, deletedAt?: string | undefined, relationName?: string | undefined }>, listingAgreement?: { __typename?: 'ListingAgreement', id: any, deadline?: string | undefined, status?: GQLListingAgreementStatus | undefined, deadlineHasBeenExceeded?: boolean | undefined, sellerSigners?: Array<{ __typename?: 'Signer', id: any, externalSignerId: string, url?: string | undefined, signedAt?: string | undefined, title?: string | undefined, email: string, firstName?: string | undefined, lastName?: string | undefined }> | undefined, brokerSigners?: Array<{ __typename?: 'Signer', id: any, externalSignerId: string, signedAt?: string | undefined }> | undefined } | undefined } | undefined, listingAgreementInteractionsForEstate: Array<{ __typename?: 'ListingAgreementInteraction', id: number, listingAgreementsId: string, sellerId?: string | undefined, eventType: GQLListingAgreementInteractionType, eventTimestamp: string, extraData?: any | undefined, name?: string | undefined, seller?: { __typename?: 'ListingAgreementInteractionSeller', firstName?: string | undefined, lastName?: string | undefined } | undefined }>, pageVisits: Array<{ __typename?: 'PageVisit', id: number, contactId?: string | undefined, employeeId?: string | undefined, totalTimeSpent: number, lastHeartbeat: string, startTime: string, endTime?: string | undefined, pageId: string, browser?: string | undefined, location?: string | undefined, contactName?: string | undefined }> };

export type GQLEditInspectionFolderQueryVariables = Exact<{
  estateId: Scalars['String']['input'];
}>;


export type GQLEditInspectionFolderQuery = { __typename?: 'Query', inspectionFolder?: { __typename?: 'InspectionFolder', id: string, listingAgreementActive: boolean, excludedEmployees: Array<string>, relevantLinks: Array<string>, sentAt?: string | undefined, listingAgreementSentAt?: string | undefined, excludedPartners: Array<{ __typename?: 'BrokerPartner', id: string }> } | undefined, estate?: { __typename?: 'BrokerEstate', assistantBroker?: { __typename?: 'Broker', employeeId: string, name?: string | undefined, title?: string | undefined, mobilePhone?: string | undefined, email: string, image?: { __typename?: 'BrokerImage', small?: string | undefined } | undefined } | undefined, mainBroker?: { __typename?: 'Broker', team?: Array<{ __typename?: 'BrokerPartner', id: string, name: string, category?: string | undefined, createdAt?: string | undefined, profilePicture?: string | undefined }> | undefined } | undefined } | undefined };

export type GQLOaSignedAtQueryVariables = Exact<{
  estateId: Scalars['String']['input'];
}>;


export type GQLOaSignedAtQuery = { __typename?: 'Query', listingAgreementByEstateId?: { __typename?: 'ListingAgreement', signedAt?: string | undefined } | undefined };

export type GQLPageVisitsQueryVariables = Exact<{
  estateId: Scalars['String']['input'];
  includeSubPages: Scalars['Boolean']['input'];
}>;


export type GQLPageVisitsQuery = { __typename?: 'Query', pageVisits: Array<{ __typename?: 'PageVisit', id: number, contactId?: string | undefined, employeeId?: string | undefined, totalTimeSpent: number, lastHeartbeat: string, startTime: string, endTime?: string | undefined, pageId: string, browser?: string | undefined, location?: string | undefined, contactName?: string | undefined }> };

export type GQLEstateInteractionsAndPageVisitsQueryVariables = Exact<{
  estateId: Scalars['String']['input'];
  includeSubPages?: InputMaybe<Scalars['Boolean']['input']>;
}>;


export type GQLEstateInteractionsAndPageVisitsQuery = { __typename?: 'Query', pageVisits: Array<{ __typename?: 'PageVisit', id: number, contactId?: string | undefined, employeeId?: string | undefined, totalTimeSpent: number, lastHeartbeat: string, startTime: string, endTime?: string | undefined, pageId: string, browser?: string | undefined, location?: string | undefined, contactName?: string | undefined }>, listingAgreementInteractionsForEstate: Array<{ __typename?: 'ListingAgreementInteraction', id: number, listingAgreementsId: string, sellerId?: string | undefined, eventType: GQLListingAgreementInteractionType, eventTimestamp: string, extraData?: any | undefined, name?: string | undefined, seller?: { __typename?: 'ListingAgreementInteractionSeller', firstName?: string | undefined, lastName?: string | undefined } | undefined }>, listingAgreementByEstateId?: { __typename?: 'ListingAgreement', signedAt?: string | undefined, sentToClientAt?: string | undefined, status?: GQLListingAgreementStatus | undefined } | undefined };

export type GQLActivitySummaryQueryVariables = Exact<{
  estateId: Scalars['String']['input'];
}>;


export type GQLActivitySummaryQuery = { __typename?: 'Query', activitySummary: { __typename?: 'ActivitySummary', visitsCount: number, visitorCount: number, totalTimeSpent: number, signedCount: number, signersCount: number, recipientsCount: number } };

export type GQLDepartmentEmployeesQueryVariables = Exact<{
  departmentId: Scalars['Int']['input'];
}>;


export type GQLDepartmentEmployeesQuery = { __typename?: 'Query', department?: { __typename?: 'Department', departmentId: number, employees?: Array<{ __typename?: 'DepartmentEmployee', employeeId?: string | undefined, name?: string | undefined } | undefined> | undefined } | undefined };

export type GQLEstateDrawerDetailsQueryVariables = Exact<{
  id: Scalars['String']['input'];
}>;


export type GQLEstateDrawerDetailsQuery = { __typename?: 'Query', estate?: { __typename?: 'BrokerEstate', status: number, estateId: string, assignmentNumber?: string | undefined, noOfBedRooms?: number | undefined, soldDate?: string | undefined, commissionAcceptedDate?: string | undefined, linkToNext?: string | undefined, placeholderImage?: string | undefined, hjemUrl?: string | undefined, address?: { __typename?: 'BrokerAddress', streetAddress?: string | undefined, city?: string | undefined } | undefined, marketingStart?: { __typename?: 'MarketingStart', date?: string | undefined, source?: string | undefined } | undefined, estatePrice?: { __typename?: 'EstatePrice', totalPrice?: number | undefined, priceSuggestion?: number | undefined, soldPrice?: number | undefined } | undefined, sumArea?: { __typename?: 'SumArea', braI: number, pRom: number } | undefined, links: Array<{ __typename?: 'BrokerEstateLink', linkType?: number | undefined, url?: string | undefined, text?: string | undefined }>, mainImage?: { __typename?: 'BrokerEstateImage', large?: string | undefined } | undefined, sellers: Array<{ __typename?: 'BrokerEstateSeller', firstName?: string | undefined, lastName?: string | undefined, email?: string | undefined, mobilePhone?: string | undefined, mainContact?: boolean | undefined, contactId: string }>, showings: Array<{ __typename?: 'BrokerEstateShowing', start?: string | undefined, end?: string | undefined, showingId?: string | undefined }>, checklist: Array<{ __typename?: 'EstateChecklistItem', firstTag?: string | undefined, value?: number | undefined }>, brokers?: Array<{ __typename?: 'EstateBroker', role?: number | undefined, name?: string | undefined, employeeId?: string | undefined, image?: { __typename?: 'BrokerImage', small?: string | undefined } | undefined, employeeRoles?: Array<{ __typename?: 'BrokerRole', typeId?: number | undefined } | undefined> | undefined } | undefined> | undefined, ads: Array<{ __typename?: 'EstateAd', source?: GQLEstateAdSource | undefined, link?: string | undefined }> } | undefined, events: Array<{ __typename?: 'InspectionEvent', id: string, title?: string | undefined, start: string, end?: string | undefined, type: string, description?: string | undefined }> };

export type GQLUpsertEstatePublishDateMutationVariables = Exact<{
  estateId: Scalars['String']['input'];
  publishDate?: InputMaybe<Scalars['DateTime']['input']>;
}>;


export type GQLUpsertEstatePublishDateMutation = { __typename?: 'Mutation', upsertEstatePublishDate: boolean };

export type GQLNewAssignmentsOverviewQueryVariables = Exact<{
  brokerId: Scalars['String']['input'];
  departmentId: Scalars['Int']['input'];
  limit: Scalars['Int']['input'];
  offset: Scalars['Int']['input'];
  tabs: Array<GQLEstateTabFilter> | GQLEstateTabFilter;
  archived?: InputMaybe<Scalars['Boolean']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
  officeView?: InputMaybe<Scalars['Boolean']['input']>;
  brokerIds?: InputMaybe<Array<Scalars['String']['input']> | Scalars['String']['input']>;
  sortBy?: InputMaybe<GQLSortEstateBy>;
}>;


export type GQLNewAssignmentsOverviewQuery = { __typename?: 'Query', broker?: { __typename?: 'BrokerEstatesResponse', pagination?: { __typename?: 'BrokerEstatePagination', total?: number | undefined, count?: number | undefined, offset?: number | undefined, limit?: number | undefined } | undefined, items: Array<{ __typename?: 'BrokerEstate', estateId: string, status: number, isValuation?: boolean | undefined, soldDate?: string | undefined, linkToNext?: string | undefined, assignmentNumber?: string | undefined, changedDate?: string | undefined, mainImage?: { __typename?: 'BrokerEstateImage', small?: string | undefined } | undefined, checklist: Array<{ __typename?: 'EstateChecklistItem', firstTag?: string | undefined, value?: number | undefined }>, marketingStart?: { __typename?: 'MarketingStart', date?: string | undefined, source?: string | undefined } | undefined, showings: Array<{ __typename?: 'BrokerEstateShowing', start?: string | undefined, end?: string | undefined, showingId?: string | undefined }>, brokers?: Array<{ __typename?: 'EstateBroker', name?: string | undefined, email?: string | undefined, role?: number | undefined, employeeId?: string | undefined, image?: { __typename?: 'BrokerImage', small?: string | undefined } | undefined } | undefined> | undefined, address?: { __typename?: 'BrokerAddress', streetAddress?: string | undefined } | undefined, inspectionEvents?: Array<{ __typename?: 'InspectionEvent', id: string, title?: string | undefined, start: string, end?: string | undefined, type: string, description?: string | undefined }> | undefined }> }, office?: { __typename?: 'BrokerEstatesResponse', pagination?: { __typename?: 'BrokerEstatePagination', total?: number | undefined, count?: number | undefined, offset?: number | undefined, limit?: number | undefined } | undefined, items: Array<{ __typename?: 'BrokerEstate', estateId: string, status: number, isValuation?: boolean | undefined, soldDate?: string | undefined, linkToNext?: string | undefined, assignmentNumber?: string | undefined, changedDate?: string | undefined, mainImage?: { __typename?: 'BrokerEstateImage', small?: string | undefined } | undefined, checklist: Array<{ __typename?: 'EstateChecklistItem', firstTag?: string | undefined, value?: number | undefined }>, marketingStart?: { __typename?: 'MarketingStart', date?: string | undefined, source?: string | undefined } | undefined, showings: Array<{ __typename?: 'BrokerEstateShowing', start?: string | undefined, end?: string | undefined, showingId?: string | undefined }>, brokers?: Array<{ __typename?: 'EstateBroker', name?: string | undefined, email?: string | undefined, role?: number | undefined, employeeId?: string | undefined, image?: { __typename?: 'BrokerImage', small?: string | undefined } | undefined } | undefined> | undefined, address?: { __typename?: 'BrokerAddress', streetAddress?: string | undefined } | undefined, inspectionEvents?: Array<{ __typename?: 'InspectionEvent', id: string, title?: string | undefined, start: string, end?: string | undefined, type: string, description?: string | undefined }> | undefined }> } };

export type GQLEstatesOverviewItemFragment = { __typename?: 'BrokerEstate', estateId: string, status: number, isValuation?: boolean | undefined, soldDate?: string | undefined, linkToNext?: string | undefined, assignmentNumber?: string | undefined, changedDate?: string | undefined, mainImage?: { __typename?: 'BrokerEstateImage', small?: string | undefined } | undefined, checklist: Array<{ __typename?: 'EstateChecklistItem', firstTag?: string | undefined, value?: number | undefined }>, marketingStart?: { __typename?: 'MarketingStart', date?: string | undefined, source?: string | undefined } | undefined, showings: Array<{ __typename?: 'BrokerEstateShowing', start?: string | undefined, end?: string | undefined, showingId?: string | undefined }>, brokers?: Array<{ __typename?: 'EstateBroker', name?: string | undefined, email?: string | undefined, role?: number | undefined, employeeId?: string | undefined, image?: { __typename?: 'BrokerImage', small?: string | undefined } | undefined } | undefined> | undefined, address?: { __typename?: 'BrokerAddress', streetAddress?: string | undefined } | undefined, inspectionEvents?: Array<{ __typename?: 'InspectionEvent', id: string, title?: string | undefined, start: string, end?: string | undefined, type: string, description?: string | undefined }> | undefined };

export type GQLEstatesForBrokerByIdQueryVariables = Exact<{
  brokerId?: InputMaybe<Scalars['String']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  tabs: Array<GQLEstateTabFilter> | GQLEstateTabFilter;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  archived?: InputMaybe<Scalars['Boolean']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
  disableCache?: InputMaybe<Scalars['Boolean']['input']>;
}>;


export type GQLEstatesForBrokerByIdQuery = { __typename?: 'Query', estatesForBrokerById: { __typename?: 'BrokerEstatesResponse', pagination?: { __typename?: 'BrokerEstatePagination', offset?: number | undefined, limit?: number | undefined, count?: number | undefined, total?: number | undefined } | undefined, items: Array<{ __typename?: 'BrokerEstate', id: any, estateId: string, type?: string | undefined, assignmentNumber?: string | undefined, isWithdrawn: boolean, status: number, soldDate?: string | undefined, expireDate?: string | undefined, commissionAcceptedDate?: string | undefined, finn?: { __typename?: 'FinnData', finnCode?: string | undefined, finnExpireDate?: string | undefined, finnPublishDate?: string | undefined } | undefined, address?: { __typename?: 'BrokerAddress', streetAddress?: string | undefined } | undefined, upcomingEvents: Array<{ __typename?: 'EstateActivity', start?: string | undefined, end?: string | undefined, type?: number | undefined, typeName?: string | undefined, name?: string | undefined, done?: boolean | undefined, id?: string | undefined }>, estatePrice?: { __typename?: 'EstatePrice', totalPrice?: number | undefined, priceSuggestion?: number | undefined, soldPrice?: number | undefined } | undefined, mainImage?: { __typename?: 'BrokerEstateImage', medium?: string | undefined } | undefined, sellers: Array<{ __typename?: 'BrokerEstateSeller', firstName?: string | undefined, lastName?: string | undefined, email?: string | undefined, mobilePhone?: string | undefined, mainContact?: boolean | undefined, contactId: string }>, mainSeller?: { __typename?: 'BrokerEstateSeller', contactId: string, firstName?: string | undefined, lastName?: string | undefined, email?: string | undefined, mobilePhone?: string | undefined, mainContact?: boolean | undefined } | undefined, inspectionFolder?: { __typename?: 'InspectionFolder', id: string, sentAt?: string | undefined, createdAt?: string | undefined } | undefined, listingAgreement?: { __typename?: 'ListingAgreement', id: any, createdAt?: string | undefined, status?: GQLListingAgreementStatus | undefined, signicatDocumentId?: string | undefined, signedAt?: string | undefined, sentToClientAt?: string | undefined, initiatedSigningAt?: string | undefined, deadline?: string | undefined, brokerSigners?: Array<{ __typename?: 'Signer', id: any, externalSignerId: string, url?: string | undefined, signedAt?: string | undefined, title?: string | undefined, email: string, phone?: string | undefined, firstName?: string | undefined, lastName?: string | undefined }> | undefined, sellerSigners?: Array<{ __typename?: 'Signer', id: any, externalSignerId: string, url?: string | undefined, signedAt?: string | undefined, title?: string | undefined, email: string, phone?: string | undefined, firstName?: string | undefined, lastName?: string | undefined }> | undefined, accessTokens?: Array<{ __typename?: 'AccessToken', id: any, createdAt?: string | undefined } | undefined> | undefined } | undefined }> } };

export type GQLEstatesOverviewForBrokerByIdQueryVariables = Exact<{
  brokerId?: InputMaybe<Scalars['String']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  tabs: Array<GQLEstateTabFilter> | GQLEstateTabFilter;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  archived?: InputMaybe<Scalars['Boolean']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
  disableCache?: InputMaybe<Scalars['Boolean']['input']>;
  includeEtakstPublished?: InputMaybe<Scalars['Boolean']['input']>;
}>;


export type GQLEstatesOverviewForBrokerByIdQuery = { __typename?: 'Query', estatesForBrokerById: { __typename?: 'BrokerEstatesResponse', pagination?: { __typename?: 'BrokerEstatePagination', offset?: number | undefined, limit?: number | undefined, count?: number | undefined, total?: number | undefined } | undefined, items: Array<{ __typename?: 'BrokerEstate', id: any, estateId: string, type?: string | undefined, assignmentNumber?: string | undefined, isValuation?: boolean | undefined, isWithdrawn: boolean, status: number, soldDate?: string | undefined, expireDate?: string | undefined, commissionAcceptedDate?: string | undefined, isEtakstPublished?: boolean | undefined, finn?: { __typename?: 'FinnData', finnCode?: string | undefined, finnExpireDate?: string | undefined, finnPublishDate?: string | undefined } | undefined, address?: { __typename?: 'BrokerAddress', streetAddress?: string | undefined } | undefined, inspectionFolder?: { __typename?: 'InspectionFolder', notes?: string | undefined } | undefined, upcomingEvents: Array<{ __typename?: 'EstateActivity', start?: string | undefined, end?: string | undefined, type?: number | undefined, typeName?: string | undefined, name?: string | undefined, done?: boolean | undefined, id?: string | undefined }>, estatePrice?: { __typename?: 'EstatePrice', totalPrice?: number | undefined, priceSuggestion?: number | undefined, soldPrice?: number | undefined } | undefined, mainImage?: { __typename?: 'BrokerEstateImage', medium?: string | undefined } | undefined, sellers: Array<{ __typename?: 'BrokerEstateSeller', firstName?: string | undefined, lastName?: string | undefined, email?: string | undefined, mobilePhone?: string | undefined, mainContact?: boolean | undefined, contactId: string }>, mainSeller?: { __typename?: 'BrokerEstateSeller', contactId: string, firstName?: string | undefined, lastName?: string | undefined, email?: string | undefined, mobilePhone?: string | undefined, mainContact?: boolean | undefined } | undefined }> } };

export type GQLEstatesForBrokerIdCountQueryVariables = Exact<{
  brokerId?: InputMaybe<Scalars['String']['input']>;
  tabs: Array<GQLEstateTabFilter> | GQLEstateTabFilter;
  email?: InputMaybe<Scalars['String']['input']>;
}>;


export type GQLEstatesForBrokerIdCountQuery = { __typename?: 'Query', estatesForBrokerIdCount: Array<{ __typename?: 'BrokerEstateCountResponse', tab: GQLEstateTabFilter, count: number }> };

export type GQLEstateListingAgreementQueryVariables = Exact<{
  estateId: Scalars['String']['input'];
}>;


export type GQLEstateListingAgreementQuery = { __typename?: 'Query', listingAgreementByEstateId?: { __typename?: 'ListingAgreement', id: any, createdAt?: string | undefined, status?: GQLListingAgreementStatus | undefined, signicatDocumentId?: string | undefined, signedAt?: string | undefined, sentToClientAt?: string | undefined, initiatedSigningAt?: string | undefined, deadline?: string | undefined, brokerSigners?: Array<{ __typename?: 'Signer', id: any, externalSignerId: string, url?: string | undefined, signedAt?: string | undefined, title?: string | undefined, email: string, phone?: string | undefined, firstName?: string | undefined, lastName?: string | undefined }> | undefined, sellerSigners?: Array<{ __typename?: 'Signer', id: any, externalSignerId: string, url?: string | undefined, signedAt?: string | undefined, title?: string | undefined, email: string, phone?: string | undefined, firstName?: string | undefined, lastName?: string | undefined }> | undefined, accessTokens?: Array<{ __typename?: 'AccessToken', id: any, createdAt?: string | undefined } | undefined> | undefined } | undefined };

export type GQLProfilePreviewByEmployeeIdQueryVariables = Exact<{
  employeeId: Scalars['String']['input'];
}>;


export type GQLProfilePreviewByEmployeeIdQuery = { __typename?: 'Query', brokerByEmployeeId?: { __typename?: 'Broker', name?: string | undefined, title?: string | undefined, instagram?: string | undefined, aboutMe?: string | undefined, mobilePhone?: string | undefined, email: string, image?: { __typename?: 'BrokerImage', medium?: string | undefined } | undefined, department?: { __typename?: 'Department', name: string, displayKtiOnEmployee?: boolean | undefined } | undefined, rating?: { __typename?: 'Rating', average?: number | undefined, count?: number | undefined } | undefined, usp?: Array<{ __typename?: 'Usp', title?: string | undefined, description?: string | undefined }> | undefined, awards?: Array<{ __typename?: 'Award', id?: string | undefined, name?: string | undefined, origin?: string | undefined, year?: number | undefined }> | undefined, nordvikAwards?: Array<{ __typename?: 'NordvikAward', awardId?: string | undefined, name?: string | undefined, origin?: string | undefined, year?: number | undefined, hidden?: boolean | undefined, private?: boolean | undefined }> | undefined, links?: { __typename?: 'BrokerProfileLinks', adLinks: Array<string>, mediaLinks: Array<string> } | undefined, featuredReviews: Array<{ __typename?: 'BrokerRating', userName?: string | undefined, rating?: number | undefined, createdAt?: string | undefined, review?: { __typename?: 'BrokerReview', text?: string | undefined } | undefined }>, fallbackReviews: Array<{ __typename?: 'BrokerRating', userName?: string | undefined, rating?: number | undefined, createdAt?: string | undefined, review?: { __typename?: 'BrokerReview', text?: string | undefined } | undefined }>, team?: Array<{ __typename?: 'BrokerPartner', id: string, name: string, instagram?: string | undefined, website?: string | undefined, description?: string | undefined, category?: string | undefined, images: Array<string>, createdAt?: string | undefined, updatedAt?: string | undefined, profilePicture?: string | undefined, hidden?: boolean | undefined }> | undefined } | undefined };

export type GQLProfileQueryVariables = Exact<{ [key: string]: never; }>;


export type GQLProfileQuery = { __typename?: 'Query', currentBroker?: { __typename?: 'Broker', employeeId: string, title?: string | undefined, name?: string | undefined, instagram?: string | undefined, mobilePhone?: string | undefined, email: string, aboutMe?: string | undefined, usp?: Array<{ __typename?: 'Usp', title?: string | undefined, description?: string | undefined }> | undefined, department?: { __typename?: 'Department', name: string, displayKtiOnEmployee?: boolean | undefined } | undefined, rating?: { __typename?: 'Rating', average?: number | undefined, count?: number | undefined } | undefined, featuredReviews: Array<{ __typename?: 'BrokerRating', userName?: string | undefined, rating?: number | undefined, createdAt?: string | undefined, review?: { __typename?: 'BrokerReview', text?: string | undefined } | undefined }>, fallbackReviews: Array<{ __typename?: 'BrokerRating', userName?: string | undefined, rating?: number | undefined, createdAt?: string | undefined, review?: { __typename?: 'BrokerReview', text?: string | undefined } | undefined }>, awards?: Array<{ __typename?: 'Award', id?: string | undefined, name?: string | undefined, origin?: string | undefined, year?: number | undefined }> | undefined, nordvikAwards?: Array<{ __typename?: 'NordvikAward', awardId?: string | undefined, name?: string | undefined, origin?: string | undefined, year?: number | undefined, hidden?: boolean | undefined, private?: boolean | undefined }> | undefined, image?: { __typename?: 'BrokerImage', medium?: string | undefined } | undefined } | undefined };

export type GQLBrokerProfileLinksQueryVariables = Exact<{ [key: string]: never; }>;


export type GQLBrokerProfileLinksQuery = { __typename?: 'Query', currentBrokerProfileLinks?: { __typename?: 'BrokerProfileLinks', adLinks: Array<string>, mediaLinks: Array<string> } | undefined };

export type GQLUpdateProfileMutationVariables = Exact<{
  input: GQLUpdateBrokerPayload;
}>;


export type GQLUpdateProfileMutation = { __typename?: 'Mutation', updateBroker: boolean };

export type GQLAddAwardMutationVariables = Exact<{
  input: GQLCreateAward;
}>;


export type GQLAddAwardMutation = { __typename?: 'Mutation', addAward: boolean };

export type GQLUpdateAwardMutationVariables = Exact<{
  input: GQLUpdateAward;
}>;


export type GQLUpdateAwardMutation = { __typename?: 'Mutation', updateAward: boolean };

export type GQLRemoveAwardMutationVariables = Exact<{
  id: Scalars['String']['input'];
}>;


export type GQLRemoveAwardMutation = { __typename?: 'Mutation', removeAward: boolean };

export type GQLHideAwardMutationVariables = Exact<{
  id: Scalars['String']['input'];
  hidden: Scalars['Boolean']['input'];
}>;


export type GQLHideAwardMutation = { __typename?: 'Mutation', hideAward: boolean };

export type GQLUpdateLinksMutationVariables = Exact<{
  input: GQLBrokerProfileLinksPayload;
}>;


export type GQLUpdateLinksMutation = { __typename?: 'Mutation', updateBrokerProfileLinks: boolean };

export type GQLAddPartnerMutationVariables = Exact<{
  input: GQLBrokerPartnerCreateInput;
}>;


export type GQLAddPartnerMutation = { __typename?: 'Mutation', createBrokerPartner: { __typename?: 'BrokerPartner', id: string, name: string, instagram?: string | undefined, website?: string | undefined, description?: string | undefined, category?: string | undefined, images: Array<string>, createdAt?: string | undefined, updatedAt?: string | undefined, profilePicture?: string | undefined } };

export type GQLDeletePartnerMutationVariables = Exact<{
  id: Scalars['String']['input'];
}>;


export type GQLDeletePartnerMutation = { __typename?: 'Mutation', deleteBrokerPartner: boolean };

export type GQLUpdateBrokerPartnerMutationVariables = Exact<{
  input: GQLBrokerPartnerUpdateInput;
}>;


export type GQLUpdateBrokerPartnerMutation = { __typename?: 'Mutation', updateBrokerPartner?: { __typename?: 'BrokerPartner', id: string, name: string, instagram?: string | undefined, website?: string | undefined, description?: string | undefined, category?: string | undefined, images: Array<string>, createdAt?: string | undefined, updatedAt?: string | undefined, profilePicture?: string | undefined } | undefined };

export type GQLHideBrokerPartnerMutationVariables = Exact<{
  id: Scalars['String']['input'];
  hidden: Scalars['Boolean']['input'];
}>;


export type GQLHideBrokerPartnerMutation = { __typename?: 'Mutation', hideBrokerPartner?: { __typename?: 'BrokerPartner', id: string, hidden?: boolean | undefined } | undefined };

export type GQLReorderBrokerPartnersMutationVariables = Exact<{
  ids: Array<Scalars['String']['input']> | Scalars['String']['input'];
}>;


export type GQLReorderBrokerPartnersMutation = { __typename?: 'Mutation', reorderBrokerPartners: boolean };

export type GQLGetBrokerPartnersQueryVariables = Exact<{ [key: string]: never; }>;


export type GQLGetBrokerPartnersQuery = { __typename?: 'Query', currentBrokerPartners: Array<{ __typename?: 'BrokerPartner', id: string, name: string, instagram?: string | undefined, website?: string | undefined, description?: string | undefined, category?: string | undefined, images: Array<string>, createdAt?: string | undefined, updatedAt?: string | undefined, profilePicture?: string | undefined, hidden?: boolean | undefined }> };

export type GQLProfilePreviewQueryVariables = Exact<{ [key: string]: never; }>;


export type GQLProfilePreviewQuery = { __typename?: 'Query', currentBroker?: { __typename?: 'Broker', name?: string | undefined, title?: string | undefined, instagram?: string | undefined, aboutMe?: string | undefined, mobilePhone?: string | undefined, email: string, image?: { __typename?: 'BrokerImage', medium?: string | undefined } | undefined, department?: { __typename?: 'Department', name: string, displayKtiOnEmployee?: boolean | undefined } | undefined, rating?: { __typename?: 'Rating', average?: number | undefined, count?: number | undefined } | undefined, usp?: Array<{ __typename?: 'Usp', title?: string | undefined, description?: string | undefined }> | undefined, awards?: Array<{ __typename?: 'Award', id?: string | undefined, name?: string | undefined, origin?: string | undefined, year?: number | undefined }> | undefined, nordvikAwards?: Array<{ __typename?: 'NordvikAward', awardId?: string | undefined, name?: string | undefined, origin?: string | undefined, year?: number | undefined, hidden?: boolean | undefined, private?: boolean | undefined }> | undefined, links?: { __typename?: 'BrokerProfileLinks', adLinks: Array<string>, mediaLinks: Array<string> } | undefined, featuredReviews: Array<{ __typename?: 'BrokerRating', userName?: string | undefined, rating?: number | undefined, createdAt?: string | undefined, review?: { __typename?: 'BrokerReview', text?: string | undefined } | undefined }>, fallbackReviews: Array<{ __typename?: 'BrokerRating', userName?: string | undefined, rating?: number | undefined, createdAt?: string | undefined, review?: { __typename?: 'BrokerReview', text?: string | undefined } | undefined }>, team?: Array<{ __typename?: 'BrokerPartner', id: string, name: string, instagram?: string | undefined, website?: string | undefined, description?: string | undefined, category?: string | undefined, images: Array<string>, createdAt?: string | undefined, updatedAt?: string | undefined, profilePicture?: string | undefined, hidden?: boolean | undefined }> | undefined } | undefined };

export type GQLAllDepartmentsQueryVariables = Exact<{ [key: string]: never; }>;


export type GQLAllDepartmentsQuery = { __typename?: 'Query', allDepartments?: { __typename?: 'DepartmentsResponse', items: Array<{ __typename?: 'Department', departmentId: number, name: string }> } | undefined };

export type GQLToplistQueryVariables = Exact<{
  offset?: InputMaybe<Scalars['Int']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
  estateType?: InputMaybe<Scalars['String']['input']>;
  section?: InputMaybe<GQLToplistSection>;
  startDate?: InputMaybe<Scalars['Date']['input']>;
  endDate?: InputMaybe<Scalars['Date']['input']>;
  departmentId?: InputMaybe<Scalars['Int']['input']>;
  marketingPackage?: InputMaybe<Scalars['String']['input']>;
  roles?: InputMaybe<Array<GQLToplistRole> | GQLToplistRole>;
}>;


export type GQLToplistQuery = { __typename?: 'Query', toplist: { __typename?: 'ToplistResponse', totalCount: number, items: Array<{ __typename?: 'ToplistEntry', id?: any | undefined, name: string, departmentId?: number | undefined, department?: string | undefined, value: number, avatarUrl?: string | undefined, count?: number | undefined, reviews?: number | undefined }> } };

export type GQLBrokerByEmailQueryVariables = Exact<{
  email: Scalars['String']['input'];
}>;


export type GQLBrokerByEmailQuery = { __typename?: 'Query', brokerByEmail?: { __typename?: 'Broker', id: any, employeeId: string, name?: string | undefined, department?: { __typename?: 'Department', departmentId: number } | undefined } | undefined };

export type GQLHallOfFameQueryVariables = Exact<{ [key: string]: never; }>;


export type GQLHallOfFameQuery = { __typename?: 'Query', hallOfFame: Array<{ __typename?: 'HallOfFameEntry', year?: number | undefined, entries?: Array<{ __typename?: 'HallOfFameEntryAward', awardId?: number | undefined, name?: string | undefined, employee?: { __typename?: 'Broker', employeeId: string, id: any, name?: string | undefined, department?: { __typename?: 'Department', name: string } | undefined, image?: { __typename?: 'BrokerImage', medium?: string | undefined } | undefined } | undefined }> | undefined }> };

export type GQLDashboardCacheQueryVariables = Exact<{ [key: string]: never; }>;


export type GQLDashboardCacheQuery = { __typename?: 'Query', dashboardCache: boolean };

export type GQLDashboardCacheForEmployeeQueryVariables = Exact<{
  employeeId: Scalars['String']['input'];
  section: GQLSection;
}>;


export type GQLDashboardCacheForEmployeeQuery = { __typename?: 'Query', dashboardCacheForEmployee: boolean };

export type GQLCurrentBrokerQueryVariables = Exact<{ [key: string]: never; }>;


export type GQLCurrentBrokerQuery = { __typename?: 'Query', currentBroker?: { __typename?: 'Broker', id: any, name?: string | undefined, email: string, createdDate?: string | undefined, employeeId: string, mobilePhone?: string | undefined, image?: { __typename?: 'BrokerImage', medium?: string | undefined } | undefined, department?: { __typename?: 'Department', id: any, name: string, departmentId: number } | undefined } | undefined };

export type GQLTrackVisitQueryVariables = Exact<{
  pageId: Scalars['String']['input'];
  estateId: Scalars['String']['input'];
}>;


export type GQLTrackVisitQuery = { __typename?: 'Query', pageVisitHeartbeat?: { __typename?: 'PageVisit', id: number, lastHeartbeat: string } | undefined };

export type GQLEndVisitMutationVariables = Exact<{
  pageId: Scalars['String']['input'];
}>;


export type GQLEndVisitMutation = { __typename?: 'Mutation', endPageVisit: boolean };

export type GQLBefaringVisitsQueryVariables = Exact<{
  estateId: Scalars['String']['input'];
}>;


export type GQLBefaringVisitsQuery = { __typename?: 'Query', pageVisits: Array<{ __typename?: 'PageVisit', id: number, estateId: string, contactId?: string | undefined, employeeId?: string | undefined, pageId: string, startTime: string, lastHeartbeat: string, endTime?: string | undefined, source?: string | undefined, browser?: string | undefined, location?: string | undefined }> };

export type GQLUserHasFlagQueryVariables = Exact<{
  flag: Scalars['String']['input'];
}>;


export type GQLUserHasFlagQuery = { __typename?: 'Query', userHasFlag: boolean };

export type GQLUserSetFlagMutationVariables = Exact<{
  flag: Scalars['String']['input'];
  value: Scalars['Boolean']['input'];
}>;


export type GQLUserSetFlagMutation = { __typename?: 'Mutation', userSetFlag: boolean };

export type GQLUserResetAllFlagsMutationVariables = Exact<{ [key: string]: never; }>;


export type GQLUserResetAllFlagsMutation = { __typename?: 'Mutation', userResetAllFlags: boolean };

export type GQLBrokerPresentationFragment = { __typename?: 'Broker', name?: string | undefined, title?: string | undefined, instagram?: string | undefined, aboutMe?: string | undefined, mobilePhone?: string | undefined, email: string, image?: { __typename?: 'BrokerImage', medium?: string | undefined } | undefined, department?: { __typename?: 'Department', name: string, displayKtiOnEmployee?: boolean | undefined } | undefined, rating?: { __typename?: 'Rating', average?: number | undefined, count?: number | undefined } | undefined, usp?: Array<{ __typename?: 'Usp', title?: string | undefined, description?: string | undefined }> | undefined, awards?: Array<{ __typename?: 'Award', id?: string | undefined, name?: string | undefined, origin?: string | undefined, year?: number | undefined }> | undefined, nordvikAwards?: Array<{ __typename?: 'NordvikAward', awardId?: string | undefined, name?: string | undefined, origin?: string | undefined, year?: number | undefined, hidden?: boolean | undefined, private?: boolean | undefined }> | undefined, links?: { __typename?: 'BrokerProfileLinks', adLinks: Array<string>, mediaLinks: Array<string> } | undefined, featuredReviews: Array<{ __typename?: 'BrokerRating', userName?: string | undefined, rating?: number | undefined, createdAt?: string | undefined, review?: { __typename?: 'BrokerReview', text?: string | undefined } | undefined }>, fallbackReviews: Array<{ __typename?: 'BrokerRating', userName?: string | undefined, rating?: number | undefined, createdAt?: string | undefined, review?: { __typename?: 'BrokerReview', text?: string | undefined } | undefined }>, team?: Array<{ __typename?: 'BrokerPartner', id: string, name: string, instagram?: string | undefined, website?: string | undefined, description?: string | undefined, category?: string | undefined, images: Array<string>, createdAt?: string | undefined, updatedAt?: string | undefined, profilePicture?: string | undefined, hidden?: boolean | undefined }> | undefined };

export type GQLLeadsQueryVariables = Exact<{
  estateId: Scalars['String']['input'];
  leadType?: InputMaybe<GQLInspectionLeadType>;
}>;


export type GQLLeadsQuery = { __typename?: 'Query', inspectionLeadsForEstate: Array<{ __typename?: 'InspectionLead', id: string, contactId: string, successful: boolean, comment?: string | undefined }> };

export type GQLSendLeadMutationVariables = Exact<{
  estateId: Scalars['String']['input'];
  contactId: Scalars['String']['input'];
  leadType: GQLInspectionLeadType;
  source?: InputMaybe<Scalars['String']['input']>;
}>;


export type GQLSendLeadMutation = { __typename?: 'Mutation', sendInspectionLead: { __typename?: 'InspectionLead', id: string, successful: boolean } };


export const IncidentFragmentDoc = `
    fragment Incident on CraftCmsIncident {
  id
  slug
  title
  postDate
  resolved
  resolvedDate
  resolvedComment
  level
  active
  updates {
    time
    text
  }
}
    `;
export const ArticleCategoryFragmentDoc = `
    fragment ArticleCategory on CmsArticleCategory {
  id
  slug
  title
}
    `;
export const ArticlePreviewFragmentDoc = `
    fragment ArticlePreview on CmsArticle {
  id
  slug
  title
  postDate
  externalUrl
  author {
    name
    email
    avatarUrl
  }
  categories {
    ...ArticleCategory
  }
  departments {
    departmentId
    title
  }
  targetRoles {
    id
    slug
    title
    roleTypeId
  }
  image {
    small
    medium
    large
  }
  excerpt
  modules {
    type
    body
    accordion {
      header
      text
    }
  }
}
    ${ArticleCategoryFragmentDoc}`;
export const ArticlesListFragmentDoc = `
    fragment ArticlesList on CmsArticle {
  id
  slug
  title
  postDate
  externalUrl
  author {
    name
    email
    avatarUrl
  }
  categories {
    ...ArticleCategory
  }
  departments {
    departmentId
    title
  }
  targetRoles {
    id
    slug
    title
    roleTypeId
  }
  image {
    small
    medium
    large
  }
  excerpt
}
    ${ArticleCategoryFragmentDoc}`;
export const ArticlesMetaFragmentDoc = `
    fragment ArticlesMeta on CmsMeta {
  total
  currentPage
  totalPages
}
    `;
export const ChangelogPreviewFragmentDoc = `
    fragment ChangelogPreview on CmsArticle {
  id
  slug
  title
  postDate
  externalUrl
  author {
    name
    email
    avatarUrl
  }
  categories {
    ...ArticleCategory
  }
  departments {
    departmentId
    title
  }
  targetRoles {
    id
    slug
    title
    roleTypeId
  }
  image {
    small
    medium
    large
  }
  modules {
    type
    body
    accordion {
      header
      text
    }
  }
}
    ${ArticleCategoryFragmentDoc}`;
export const EstatesOverviewItemFragmentDoc = `
    fragment EstatesOverviewItem on BrokerEstate {
  mainImage {
    small
  }
  estateId
  status
  isValuation
  checklist {
    firstTag
    value
  }
  marketingStart {
    date
    source
  }
  soldDate
  showings {
    start
    end
    showingId
  }
  brokers {
    name
    email
    role
    employeeId
    image {
      small
    }
  }
  address {
    streetAddress
  }
  linkToNext
  assignmentNumber
  changedDate
  inspectionEvents {
    id
    title
    start
    end
    type
    description
  }
}
    `;
export const BrokerPresentationFragmentDoc = `
    fragment BrokerPresentation on Broker {
  name
  title
  instagram
  aboutMe
  mobilePhone
  email
  image {
    medium
  }
  department {
    name
    displayKtiOnEmployee
  }
  rating {
    average
    count
  }
  usp {
    title
    description
  }
  awards {
    id
    name
    origin
    year
  }
  nordvikAwards {
    awardId
    name
    origin
    year
    hidden
    private
  }
  links {
    adLinks
    mediaLinks
  }
  featuredReviews: reviews(input: {limit: 12, featured: true}) {
    userName
    rating
    createdAt
    review {
      text
    }
  }
  fallbackReviews: reviews(input: {limit: 12, featured: false, rating: 5}) {
    userName
    rating
    createdAt
    review {
      text
    }
  }
  team {
    id
    name
    instagram
    website
    description
    category
    images
    createdAt
    updatedAt
    profilePicture
    hidden
  }
}
    `;
export const NotesDocument = `
    query notes($estateId: String!) {
  inspectionFolder(estateId: $estateId) {
    id
    notes
  }
}
    `;

export const useNotesQuery = <
      TData = GQLNotesQuery,
      TError = unknown
    >(
      variables: GQLNotesQueryVariables,
      options?: Omit<UseQueryOptions<GQLNotesQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLNotesQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLNotesQuery, TError, TData>(
      {
    queryKey: ['notes', variables],
    queryFn: fetchData<GQLNotesQuery, GQLNotesQueryVariables>(NotesDocument, variables),
    ...options
  }
    )};

useNotesQuery.getKey = (variables: GQLNotesQueryVariables) => ['notes', variables];

export const useInfiniteNotesQuery = <
      TData = InfiniteData<GQLNotesQuery>,
      TError = unknown
    >(
      variables: GQLNotesQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLNotesQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLNotesQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLNotesQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['notes.infinite', variables],
      queryFn: (metaData) => fetchData<GQLNotesQuery, GQLNotesQueryVariables>(NotesDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteNotesQuery.getKey = (variables: GQLNotesQueryVariables) => ['notes.infinite', variables];


useNotesQuery.fetcher = (variables: GQLNotesQueryVariables, options?: RequestInit['headers']) => fetchData<GQLNotesQuery, GQLNotesQueryVariables>(NotesDocument, variables, options);

export const UpdateNotesDocument = `
    mutation updateNotes($estateId: String!, $notes: String!) {
  updateInspectionFolderNotes(estateId: $estateId, notes: $notes) {
    id
    notes
  }
}
    `;

export const useUpdateNotesMutation = <
      TError = unknown,
      TContext = unknown
    >(options?: UseMutationOptions<GQLUpdateNotesMutation, TError, GQLUpdateNotesMutationVariables, TContext>) => {
    
    return useMutation<GQLUpdateNotesMutation, TError, GQLUpdateNotesMutationVariables, TContext>(
      {
    mutationKey: ['updateNotes'],
    mutationFn: (variables?: GQLUpdateNotesMutationVariables) => fetchData<GQLUpdateNotesMutation, GQLUpdateNotesMutationVariables>(UpdateNotesDocument, variables)(),
    ...options
  }
    )};

useUpdateNotesMutation.getKey = () => ['updateNotes'];


useUpdateNotesMutation.fetcher = (variables: GQLUpdateNotesMutationVariables, options?: RequestInit['headers']) => fetchData<GQLUpdateNotesMutation, GQLUpdateNotesMutationVariables>(UpdateNotesDocument, variables, options);

export const StorebrandDuplicateCheckDocument = `
    query storebrandDuplicateCheck($input: StorebrandDuplicateCheckInput!) {
  storebrandDuplicateCheck(input: $input) {
    hasDuplicates
  }
}
    `;

export const useStorebrandDuplicateCheckQuery = <
      TData = GQLStorebrandDuplicateCheckQuery,
      TError = unknown
    >(
      variables: GQLStorebrandDuplicateCheckQueryVariables,
      options?: Omit<UseQueryOptions<GQLStorebrandDuplicateCheckQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLStorebrandDuplicateCheckQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLStorebrandDuplicateCheckQuery, TError, TData>(
      {
    queryKey: ['storebrandDuplicateCheck', variables],
    queryFn: fetchData<GQLStorebrandDuplicateCheckQuery, GQLStorebrandDuplicateCheckQueryVariables>(StorebrandDuplicateCheckDocument, variables),
    ...options
  }
    )};

useStorebrandDuplicateCheckQuery.getKey = (variables: GQLStorebrandDuplicateCheckQueryVariables) => ['storebrandDuplicateCheck', variables];

export const useInfiniteStorebrandDuplicateCheckQuery = <
      TData = InfiniteData<GQLStorebrandDuplicateCheckQuery>,
      TError = unknown
    >(
      variables: GQLStorebrandDuplicateCheckQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLStorebrandDuplicateCheckQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLStorebrandDuplicateCheckQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLStorebrandDuplicateCheckQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['storebrandDuplicateCheck.infinite', variables],
      queryFn: (metaData) => fetchData<GQLStorebrandDuplicateCheckQuery, GQLStorebrandDuplicateCheckQueryVariables>(StorebrandDuplicateCheckDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteStorebrandDuplicateCheckQuery.getKey = (variables: GQLStorebrandDuplicateCheckQueryVariables) => ['storebrandDuplicateCheck.infinite', variables];


useStorebrandDuplicateCheckQuery.fetcher = (variables: GQLStorebrandDuplicateCheckQueryVariables, options?: RequestInit['headers']) => fetchData<GQLStorebrandDuplicateCheckQuery, GQLStorebrandDuplicateCheckQueryVariables>(StorebrandDuplicateCheckDocument, variables, options);

export const AreaStatisticsDocument = `
    query areaStatistics($postalCode: String!, $years: Float!) {
  priceStatistics(postalCode: $postalCode, years: $years) {
    indexes {
      id
      type
      date
      avgSalesTime
      region
      area
      avgSqmPrice
      indexChange12Months
      indexChange4Quarter
      indexChange5Years
      indexChange10Years
    }
    secondaryIndexes {
      id
      type
      date
      avgSalesTime
      region
      area
      avgSqmPrice
      indexChange12Months
      indexChange4Quarter
      indexChange5Years
      indexChange10Years
    }
  }
}
    `;

export const useAreaStatisticsQuery = <
      TData = GQLAreaStatisticsQuery,
      TError = unknown
    >(
      variables: GQLAreaStatisticsQueryVariables,
      options?: Omit<UseQueryOptions<GQLAreaStatisticsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLAreaStatisticsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLAreaStatisticsQuery, TError, TData>(
      {
    queryKey: ['areaStatistics', variables],
    queryFn: fetchData<GQLAreaStatisticsQuery, GQLAreaStatisticsQueryVariables>(AreaStatisticsDocument, variables),
    ...options
  }
    )};

useAreaStatisticsQuery.getKey = (variables: GQLAreaStatisticsQueryVariables) => ['areaStatistics', variables];

export const useInfiniteAreaStatisticsQuery = <
      TData = InfiniteData<GQLAreaStatisticsQuery>,
      TError = unknown
    >(
      variables: GQLAreaStatisticsQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLAreaStatisticsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLAreaStatisticsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLAreaStatisticsQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['areaStatistics.infinite', variables],
      queryFn: (metaData) => fetchData<GQLAreaStatisticsQuery, GQLAreaStatisticsQueryVariables>(AreaStatisticsDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteAreaStatisticsQuery.getKey = (variables: GQLAreaStatisticsQueryVariables) => ['areaStatistics.infinite', variables];


useAreaStatisticsQuery.fetcher = (variables: GQLAreaStatisticsQueryVariables, options?: RequestInit['headers']) => fetchData<GQLAreaStatisticsQuery, GQLAreaStatisticsQueryVariables>(AreaStatisticsDocument, variables, options);

export const BefaringRelevantEstatesDocument = `
    query befaringRelevantEstates($estateId: String!) {
  inspectionFolder(estateId: $estateId) {
    relevantLinks
  }
}
    `;

export const useBefaringRelevantEstatesQuery = <
      TData = GQLBefaringRelevantEstatesQuery,
      TError = unknown
    >(
      variables: GQLBefaringRelevantEstatesQueryVariables,
      options?: Omit<UseQueryOptions<GQLBefaringRelevantEstatesQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLBefaringRelevantEstatesQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLBefaringRelevantEstatesQuery, TError, TData>(
      {
    queryKey: ['befaringRelevantEstates', variables],
    queryFn: fetchData<GQLBefaringRelevantEstatesQuery, GQLBefaringRelevantEstatesQueryVariables>(BefaringRelevantEstatesDocument, variables),
    ...options
  }
    )};

useBefaringRelevantEstatesQuery.getKey = (variables: GQLBefaringRelevantEstatesQueryVariables) => ['befaringRelevantEstates', variables];

export const useInfiniteBefaringRelevantEstatesQuery = <
      TData = InfiniteData<GQLBefaringRelevantEstatesQuery>,
      TError = unknown
    >(
      variables: GQLBefaringRelevantEstatesQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLBefaringRelevantEstatesQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLBefaringRelevantEstatesQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLBefaringRelevantEstatesQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['befaringRelevantEstates.infinite', variables],
      queryFn: (metaData) => fetchData<GQLBefaringRelevantEstatesQuery, GQLBefaringRelevantEstatesQueryVariables>(BefaringRelevantEstatesDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteBefaringRelevantEstatesQuery.getKey = (variables: GQLBefaringRelevantEstatesQueryVariables) => ['befaringRelevantEstates.infinite', variables];


useBefaringRelevantEstatesQuery.fetcher = (variables: GQLBefaringRelevantEstatesQueryVariables, options?: RequestInit['headers']) => fetchData<GQLBefaringRelevantEstatesQuery, GQLBefaringRelevantEstatesQueryVariables>(BefaringRelevantEstatesDocument, variables, options);

export const FindEstatesDocument = `
    query findEstates($filters: findEstatesFilters!) {
  findEstates(filters: $filters) {
    estateId
    estateType
    estateTypeId
    address {
      streetAddress
      zipCode
      city
    }
    noOfBedRooms
    estatePriceModel {
      soldPrice
      priceSuggestion
    }
    estatePrice {
      soldPrice
      priceSuggestion
    }
    soldDate
    brokersIdWithRoles {
      employeeId
    }
    departmentId
    latitude
    longitude
    stats {
      interested
    }
    sumArea {
      braI
      pRom
      bra
    }
    areaSize {
      BRAItotal
    }
    hjemUrl
    finn {
      finnCode
    }
    heading
    images {
      medium
    }
  }
}
    `;

export const useFindEstatesQuery = <
      TData = GQLFindEstatesQuery,
      TError = unknown
    >(
      variables: GQLFindEstatesQueryVariables,
      options?: Omit<UseQueryOptions<GQLFindEstatesQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLFindEstatesQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLFindEstatesQuery, TError, TData>(
      {
    queryKey: ['findEstates', variables],
    queryFn: fetchData<GQLFindEstatesQuery, GQLFindEstatesQueryVariables>(FindEstatesDocument, variables),
    ...options
  }
    )};

useFindEstatesQuery.getKey = (variables: GQLFindEstatesQueryVariables) => ['findEstates', variables];

export const useInfiniteFindEstatesQuery = <
      TData = InfiniteData<GQLFindEstatesQuery>,
      TError = unknown
    >(
      variables: GQLFindEstatesQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLFindEstatesQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLFindEstatesQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLFindEstatesQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['findEstates.infinite', variables],
      queryFn: (metaData) => fetchData<GQLFindEstatesQuery, GQLFindEstatesQueryVariables>(FindEstatesDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteFindEstatesQuery.getKey = (variables: GQLFindEstatesQueryVariables) => ['findEstates.infinite', variables];


useFindEstatesQuery.fetcher = (variables: GQLFindEstatesQueryVariables, options?: RequestInit['headers']) => fetchData<GQLFindEstatesQuery, GQLFindEstatesQueryVariables>(FindEstatesDocument, variables, options);

export const GetBrokerEstateDocument = `
    query getBrokerEstate($estateId: String!) {
  estate(id: $estateId) {
    matrikkel {
      gnr
      bnr
      snr
      fnr
      knr
      ownPart
    }
    createdAt
    linkToNext
    assignmentNumber
    status
    isValuation
    broker {
      name
      employeeId
      id
      email
      aboutMe
      kti
      employeeRoles {
        source
        typeId
        name
      }
    }
    isValuation
    ownership
    ownershipType
    estateId
    inspectionDate
    estateTypeExternal
    estateType
    estatePrice {
      totalPrice
      priceSuggestion
      collectiveDebt
      soldPrice
    }
    mainBroker {
      name
      image {
        medium
      }
      title
      email
      mobilePhone
    }
    estateTypeId
    noOfBedRooms
    latitude
    longitude
    areaSize {
      BRAItotal
    }
    estatePriceModel {
      priceSuggestion
      soldPrice
    }
    sumArea {
      bra
      braI
      pRom
    }
    partOwnership {
      partName
      partNumber
      partOrgNumber
      estateHousingCooperativeStockHousingUnitNumber
      estateHousingCooperativeStockNumber
    }
    businessManagerContact {
      companyName
    }
    brokersIdWithRoles {
      employeeId
      brokerRole
      employee {
        title
        name
        email
        mobilePhone
        image {
          small
        }
      }
    }
    propertyType
    landIdentificationMatrix {
      gnr
      bnr
      knr
      snr
      ownPart
    }
    department {
      departmentId
      departmentNumber
      name
      legalName
      organisationNumber
      phone
      streetAddress
      postalCode
      city
      email
      employees {
        employeeId
        name
        email
        mobilePhone
        title
        slug
      }
    }
    address {
      streetAddress
      city
      zipCode
      municipality
    }
    brokers {
      employeeId
      name
      mobilePhone
      email
      slug
      role
      title
      employeeRoles {
        source
        typeId
        name
      }
      image {
        small
      }
    }
    mainSeller {
      contactId
      firstName
      lastName
    }
    sellers {
      contactId
      firstName
      lastName
      socialSecurityNumber
      email
      mobilePhone
      mainContact
      companyName
      contactType
      proxyId
    }
    companyContacts {
      contactId
      departmentId
      contactType
      companyName
      organisationNumber
      firstName
      lastName
      mobilePhone
      privatePhone
      workPhone
      email
      address
      postalAddress
      postalCode
      city
      deletedAt
      relationName
      roleName
      relationType
    }
    extraContacts(source: Nordvik) {
      contactId
      departmentId
      contactType
      companyName
      organisationNumber
      firstName
      lastName
      mobilePhone
      privatePhone
      workPhone
      email
      address
      postalAddress
      postalCode
      city
      deletedAt
      relationName
      relationType
    }
    hasCompanySeller
    inspectionFolder {
      id
      publishedAt
      listingAgreementActive
      audit {
        id
        sentAt
        listingAgreementActive
      }
    }
    listingAgreement {
      id
      feePercentage
      commission
      suggestedPrice
      sentToClientAt
      status
      offerSellerLink
      signers {
        id
        externalSignerId
        signedAt
        title
        email
      }
    }
    activities {
      type
      start
      end
    }
  }
}
    `;

export const useGetBrokerEstateQuery = <
      TData = GQLGetBrokerEstateQuery,
      TError = unknown
    >(
      variables: GQLGetBrokerEstateQueryVariables,
      options?: Omit<UseQueryOptions<GQLGetBrokerEstateQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLGetBrokerEstateQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLGetBrokerEstateQuery, TError, TData>(
      {
    queryKey: ['getBrokerEstate', variables],
    queryFn: fetchData<GQLGetBrokerEstateQuery, GQLGetBrokerEstateQueryVariables>(GetBrokerEstateDocument, variables),
    ...options
  }
    )};

useGetBrokerEstateQuery.getKey = (variables: GQLGetBrokerEstateQueryVariables) => ['getBrokerEstate', variables];

export const useInfiniteGetBrokerEstateQuery = <
      TData = InfiniteData<GQLGetBrokerEstateQuery>,
      TError = unknown
    >(
      variables: GQLGetBrokerEstateQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLGetBrokerEstateQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLGetBrokerEstateQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLGetBrokerEstateQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['getBrokerEstate.infinite', variables],
      queryFn: (metaData) => fetchData<GQLGetBrokerEstateQuery, GQLGetBrokerEstateQueryVariables>(GetBrokerEstateDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteGetBrokerEstateQuery.getKey = (variables: GQLGetBrokerEstateQueryVariables) => ['getBrokerEstate.infinite', variables];


useGetBrokerEstateQuery.fetcher = (variables: GQLGetBrokerEstateQueryVariables, options?: RequestInit['headers']) => fetchData<GQLGetBrokerEstateQuery, GQLGetBrokerEstateQueryVariables>(GetBrokerEstateDocument, variables, options);

export const GetBrokerEstateDepartmentDocument = `
    query getBrokerEstateDepartment($estateId: String!) {
  estate(id: $estateId) {
    department {
      departmentNumber
      legalName
    }
    listingAgreement {
      feePercentage
      suggestedPrice
      commission
    }
  }
}
    `;

export const useGetBrokerEstateDepartmentQuery = <
      TData = GQLGetBrokerEstateDepartmentQuery,
      TError = unknown
    >(
      variables: GQLGetBrokerEstateDepartmentQueryVariables,
      options?: Omit<UseQueryOptions<GQLGetBrokerEstateDepartmentQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLGetBrokerEstateDepartmentQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLGetBrokerEstateDepartmentQuery, TError, TData>(
      {
    queryKey: ['getBrokerEstateDepartment', variables],
    queryFn: fetchData<GQLGetBrokerEstateDepartmentQuery, GQLGetBrokerEstateDepartmentQueryVariables>(GetBrokerEstateDepartmentDocument, variables),
    ...options
  }
    )};

useGetBrokerEstateDepartmentQuery.getKey = (variables: GQLGetBrokerEstateDepartmentQueryVariables) => ['getBrokerEstateDepartment', variables];

export const useInfiniteGetBrokerEstateDepartmentQuery = <
      TData = InfiniteData<GQLGetBrokerEstateDepartmentQuery>,
      TError = unknown
    >(
      variables: GQLGetBrokerEstateDepartmentQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLGetBrokerEstateDepartmentQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLGetBrokerEstateDepartmentQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLGetBrokerEstateDepartmentQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['getBrokerEstateDepartment.infinite', variables],
      queryFn: (metaData) => fetchData<GQLGetBrokerEstateDepartmentQuery, GQLGetBrokerEstateDepartmentQueryVariables>(GetBrokerEstateDepartmentDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteGetBrokerEstateDepartmentQuery.getKey = (variables: GQLGetBrokerEstateDepartmentQueryVariables) => ['getBrokerEstateDepartment.infinite', variables];


useGetBrokerEstateDepartmentQuery.fetcher = (variables: GQLGetBrokerEstateDepartmentQueryVariables, options?: RequestInit['headers']) => fetchData<GQLGetBrokerEstateDepartmentQuery, GQLGetBrokerEstateDepartmentQueryVariables>(GetBrokerEstateDepartmentDocument, variables, options);

export const AgreementDocument = `
    query Agreement($documentId: String!) {
  listingAgreementByDocumentId(documentId: $documentId) {
    signers {
      id
      title
      email
      phone
      signedAt
      firstName
      lastName
      url
      externalSignerId
    }
  }
}
    `;

export const useAgreementQuery = <
      TData = GQLAgreementQuery,
      TError = unknown
    >(
      variables: GQLAgreementQueryVariables,
      options?: Omit<UseQueryOptions<GQLAgreementQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLAgreementQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLAgreementQuery, TError, TData>(
      {
    queryKey: ['Agreement', variables],
    queryFn: fetchData<GQLAgreementQuery, GQLAgreementQueryVariables>(AgreementDocument, variables),
    ...options
  }
    )};

useAgreementQuery.getKey = (variables: GQLAgreementQueryVariables) => ['Agreement', variables];

export const useInfiniteAgreementQuery = <
      TData = InfiniteData<GQLAgreementQuery>,
      TError = unknown
    >(
      variables: GQLAgreementQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLAgreementQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLAgreementQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLAgreementQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['Agreement.infinite', variables],
      queryFn: (metaData) => fetchData<GQLAgreementQuery, GQLAgreementQueryVariables>(AgreementDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteAgreementQuery.getKey = (variables: GQLAgreementQueryVariables) => ['Agreement.infinite', variables];


useAgreementQuery.fetcher = (variables: GQLAgreementQueryVariables, options?: RequestInit['headers']) => fetchData<GQLAgreementQuery, GQLAgreementQueryVariables>(AgreementDocument, variables, options);

export const EstateDepartmentDocument = `
    query estateDepartment($estateId: String!) {
  estate(id: $estateId) {
    departmentId
  }
}
    `;

export const useEstateDepartmentQuery = <
      TData = GQLEstateDepartmentQuery,
      TError = unknown
    >(
      variables: GQLEstateDepartmentQueryVariables,
      options?: Omit<UseQueryOptions<GQLEstateDepartmentQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLEstateDepartmentQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLEstateDepartmentQuery, TError, TData>(
      {
    queryKey: ['estateDepartment', variables],
    queryFn: fetchData<GQLEstateDepartmentQuery, GQLEstateDepartmentQueryVariables>(EstateDepartmentDocument, variables),
    ...options
  }
    )};

useEstateDepartmentQuery.getKey = (variables: GQLEstateDepartmentQueryVariables) => ['estateDepartment', variables];

export const useInfiniteEstateDepartmentQuery = <
      TData = InfiniteData<GQLEstateDepartmentQuery>,
      TError = unknown
    >(
      variables: GQLEstateDepartmentQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLEstateDepartmentQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLEstateDepartmentQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLEstateDepartmentQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['estateDepartment.infinite', variables],
      queryFn: (metaData) => fetchData<GQLEstateDepartmentQuery, GQLEstateDepartmentQueryVariables>(EstateDepartmentDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteEstateDepartmentQuery.getKey = (variables: GQLEstateDepartmentQueryVariables) => ['estateDepartment.infinite', variables];


useEstateDepartmentQuery.fetcher = (variables: GQLEstateDepartmentQueryVariables, options?: RequestInit['headers']) => fetchData<GQLEstateDepartmentQuery, GQLEstateDepartmentQueryVariables>(EstateDepartmentDocument, variables, options);

export const MarketingPackagesDocument = `
    query marketingPackages($type: String!, $publicVisible: Boolean, $active: Boolean!) {
  marketingPackages(type: $type, publicVisible: $publicVisible, active: $active) {
    id
    name
    shortName
    packageId
    productTag
    price
    views
    clicks
    channels {
      title
      id
    }
  }
}
    `;

export const useMarketingPackagesQuery = <
      TData = GQLMarketingPackagesQuery,
      TError = unknown
    >(
      variables: GQLMarketingPackagesQueryVariables,
      options?: Omit<UseQueryOptions<GQLMarketingPackagesQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLMarketingPackagesQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLMarketingPackagesQuery, TError, TData>(
      {
    queryKey: ['marketingPackages', variables],
    queryFn: fetchData<GQLMarketingPackagesQuery, GQLMarketingPackagesQueryVariables>(MarketingPackagesDocument, variables),
    ...options
  }
    )};

useMarketingPackagesQuery.getKey = (variables: GQLMarketingPackagesQueryVariables) => ['marketingPackages', variables];

export const useInfiniteMarketingPackagesQuery = <
      TData = InfiniteData<GQLMarketingPackagesQuery>,
      TError = unknown
    >(
      variables: GQLMarketingPackagesQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLMarketingPackagesQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLMarketingPackagesQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLMarketingPackagesQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['marketingPackages.infinite', variables],
      queryFn: (metaData) => fetchData<GQLMarketingPackagesQuery, GQLMarketingPackagesQueryVariables>(MarketingPackagesDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteMarketingPackagesQuery.getKey = (variables: GQLMarketingPackagesQueryVariables) => ['marketingPackages.infinite', variables];


useMarketingPackagesQuery.fetcher = (variables: GQLMarketingPackagesQueryVariables, options?: RequestInit['headers']) => fetchData<GQLMarketingPackagesQuery, GQLMarketingPackagesQueryVariables>(MarketingPackagesDocument, variables, options);

export const InspectionEventsDocument = `
    query inspectionEvents($estateId: String!) {
  inspectionEvents(estateId: $estateId) {
    id
    title
    start
    end
    type
  }
  estate(id: $estateId) {
    sellers {
      contactId
    }
  }
}
    `;

export const useInspectionEventsQuery = <
      TData = GQLInspectionEventsQuery,
      TError = unknown
    >(
      variables: GQLInspectionEventsQueryVariables,
      options?: Omit<UseQueryOptions<GQLInspectionEventsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLInspectionEventsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLInspectionEventsQuery, TError, TData>(
      {
    queryKey: ['inspectionEvents', variables],
    queryFn: fetchData<GQLInspectionEventsQuery, GQLInspectionEventsQueryVariables>(InspectionEventsDocument, variables),
    ...options
  }
    )};

useInspectionEventsQuery.getKey = (variables: GQLInspectionEventsQueryVariables) => ['inspectionEvents', variables];

export const useInfiniteInspectionEventsQuery = <
      TData = InfiniteData<GQLInspectionEventsQuery>,
      TError = unknown
    >(
      variables: GQLInspectionEventsQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLInspectionEventsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLInspectionEventsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLInspectionEventsQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['inspectionEvents.infinite', variables],
      queryFn: (metaData) => fetchData<GQLInspectionEventsQuery, GQLInspectionEventsQueryVariables>(InspectionEventsDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteInspectionEventsQuery.getKey = (variables: GQLInspectionEventsQueryVariables) => ['inspectionEvents.infinite', variables];


useInspectionEventsQuery.fetcher = (variables: GQLInspectionEventsQueryVariables, options?: RequestInit['headers']) => fetchData<GQLInspectionEventsQuery, GQLInspectionEventsQueryVariables>(InspectionEventsDocument, variables, options);

export const AddInspectionEventsDocument = `
    mutation addInspectionEvents($estateId: String!, $events: [InspectionEventInput!]!) {
  addInspectionEvents(estateId: $estateId, events: $events)
}
    `;

export const useAddInspectionEventsMutation = <
      TError = unknown,
      TContext = unknown
    >(options?: UseMutationOptions<GQLAddInspectionEventsMutation, TError, GQLAddInspectionEventsMutationVariables, TContext>) => {
    
    return useMutation<GQLAddInspectionEventsMutation, TError, GQLAddInspectionEventsMutationVariables, TContext>(
      {
    mutationKey: ['addInspectionEvents'],
    mutationFn: (variables?: GQLAddInspectionEventsMutationVariables) => fetchData<GQLAddInspectionEventsMutation, GQLAddInspectionEventsMutationVariables>(AddInspectionEventsDocument, variables)(),
    ...options
  }
    )};

useAddInspectionEventsMutation.getKey = () => ['addInspectionEvents'];


useAddInspectionEventsMutation.fetcher = (variables: GQLAddInspectionEventsMutationVariables, options?: RequestInit['headers']) => fetchData<GQLAddInspectionEventsMutation, GQLAddInspectionEventsMutationVariables>(AddInspectionEventsDocument, variables, options);

export const DeleteInspectionEventDocument = `
    mutation deleteInspectionEvent($eventId: String!) {
  deleteInspectionEvent(eventId: $eventId)
}
    `;

export const useDeleteInspectionEventMutation = <
      TError = unknown,
      TContext = unknown
    >(options?: UseMutationOptions<GQLDeleteInspectionEventMutation, TError, GQLDeleteInspectionEventMutationVariables, TContext>) => {
    
    return useMutation<GQLDeleteInspectionEventMutation, TError, GQLDeleteInspectionEventMutationVariables, TContext>(
      {
    mutationKey: ['deleteInspectionEvent'],
    mutationFn: (variables?: GQLDeleteInspectionEventMutationVariables) => fetchData<GQLDeleteInspectionEventMutation, GQLDeleteInspectionEventMutationVariables>(DeleteInspectionEventDocument, variables)(),
    ...options
  }
    )};

useDeleteInspectionEventMutation.getKey = () => ['deleteInspectionEvent'];


useDeleteInspectionEventMutation.fetcher = (variables: GQLDeleteInspectionEventMutationVariables, options?: RequestInit['headers']) => fetchData<GQLDeleteInspectionEventMutation, GQLDeleteInspectionEventMutationVariables>(DeleteInspectionEventDocument, variables, options);

export const UpdateInspectionEventDocument = `
    mutation updateInspectionEvent($eventId: String!, $event: InspectionEventInput!) {
  updateInspectionEvent(eventId: $eventId, event: $event)
}
    `;

export const useUpdateInspectionEventMutation = <
      TError = unknown,
      TContext = unknown
    >(options?: UseMutationOptions<GQLUpdateInspectionEventMutation, TError, GQLUpdateInspectionEventMutationVariables, TContext>) => {
    
    return useMutation<GQLUpdateInspectionEventMutation, TError, GQLUpdateInspectionEventMutationVariables, TContext>(
      {
    mutationKey: ['updateInspectionEvent'],
    mutationFn: (variables?: GQLUpdateInspectionEventMutationVariables) => fetchData<GQLUpdateInspectionEventMutation, GQLUpdateInspectionEventMutationVariables>(UpdateInspectionEventDocument, variables)(),
    ...options
  }
    )};

useUpdateInspectionEventMutation.getKey = () => ['updateInspectionEvent'];


useUpdateInspectionEventMutation.fetcher = (variables: GQLUpdateInspectionEventMutationVariables, options?: RequestInit['headers']) => fetchData<GQLUpdateInspectionEventMutation, GQLUpdateInspectionEventMutationVariables>(UpdateInspectionEventDocument, variables, options);

export const ClearInspectionEventsDocument = `
    mutation clearInspectionEvents($estateId: String!) {
  clearInspectionEvents(estateId: $estateId)
}
    `;

export const useClearInspectionEventsMutation = <
      TError = unknown,
      TContext = unknown
    >(options?: UseMutationOptions<GQLClearInspectionEventsMutation, TError, GQLClearInspectionEventsMutationVariables, TContext>) => {
    
    return useMutation<GQLClearInspectionEventsMutation, TError, GQLClearInspectionEventsMutationVariables, TContext>(
      {
    mutationKey: ['clearInspectionEvents'],
    mutationFn: (variables?: GQLClearInspectionEventsMutationVariables) => fetchData<GQLClearInspectionEventsMutation, GQLClearInspectionEventsMutationVariables>(ClearInspectionEventsDocument, variables)(),
    ...options
  }
    )};

useClearInspectionEventsMutation.getKey = () => ['clearInspectionEvents'];


useClearInspectionEventsMutation.fetcher = (variables: GQLClearInspectionEventsMutationVariables, options?: RequestInit['headers']) => fetchData<GQLClearInspectionEventsMutation, GQLClearInspectionEventsMutationVariables>(ClearInspectionEventsDocument, variables, options);

export const UpdateInspectionEventsDocument = `
    mutation updateInspectionEvents($estateId: String!, $events: [InspectionEventInput!]!) {
  updateInspectionEvents(estateId: $estateId, events: $events)
}
    `;

export const useUpdateInspectionEventsMutation = <
      TError = unknown,
      TContext = unknown
    >(options?: UseMutationOptions<GQLUpdateInspectionEventsMutation, TError, GQLUpdateInspectionEventsMutationVariables, TContext>) => {
    
    return useMutation<GQLUpdateInspectionEventsMutation, TError, GQLUpdateInspectionEventsMutationVariables, TContext>(
      {
    mutationKey: ['updateInspectionEvents'],
    mutationFn: (variables?: GQLUpdateInspectionEventsMutationVariables) => fetchData<GQLUpdateInspectionEventsMutation, GQLUpdateInspectionEventsMutationVariables>(UpdateInspectionEventsDocument, variables)(),
    ...options
  }
    )};

useUpdateInspectionEventsMutation.getKey = () => ['updateInspectionEvents'];


useUpdateInspectionEventsMutation.fetcher = (variables: GQLUpdateInspectionEventsMutationVariables, options?: RequestInit['headers']) => fetchData<GQLUpdateInspectionEventsMutation, GQLUpdateInspectionEventsMutationVariables>(UpdateInspectionEventsDocument, variables, options);

export const ListingAgreementTeamDocument = `
    query listingAgreementTeam($estateId: String!) {
  estate(id: $estateId) {
    mainBroker {
      ...BrokerPresentation
    }
    assistantBroker {
      employeeId
      name
      title
      mobilePhone
      email
      image {
        small
      }
    }
    inspectionFolder {
      excludedPartners {
        id
      }
      excludedEmployees
    }
  }
}
    ${BrokerPresentationFragmentDoc}`;

export const useListingAgreementTeamQuery = <
      TData = GQLListingAgreementTeamQuery,
      TError = unknown
    >(
      variables: GQLListingAgreementTeamQueryVariables,
      options?: Omit<UseQueryOptions<GQLListingAgreementTeamQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLListingAgreementTeamQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLListingAgreementTeamQuery, TError, TData>(
      {
    queryKey: ['listingAgreementTeam', variables],
    queryFn: fetchData<GQLListingAgreementTeamQuery, GQLListingAgreementTeamQueryVariables>(ListingAgreementTeamDocument, variables),
    ...options
  }
    )};

useListingAgreementTeamQuery.getKey = (variables: GQLListingAgreementTeamQueryVariables) => ['listingAgreementTeam', variables];

export const useInfiniteListingAgreementTeamQuery = <
      TData = InfiniteData<GQLListingAgreementTeamQuery>,
      TError = unknown
    >(
      variables: GQLListingAgreementTeamQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLListingAgreementTeamQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLListingAgreementTeamQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLListingAgreementTeamQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['listingAgreementTeam.infinite', variables],
      queryFn: (metaData) => fetchData<GQLListingAgreementTeamQuery, GQLListingAgreementTeamQueryVariables>(ListingAgreementTeamDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteListingAgreementTeamQuery.getKey = (variables: GQLListingAgreementTeamQueryVariables) => ['listingAgreementTeam.infinite', variables];


useListingAgreementTeamQuery.fetcher = (variables: GQLListingAgreementTeamQueryVariables, options?: RequestInit['headers']) => fetchData<GQLListingAgreementTeamQuery, GQLListingAgreementTeamQueryVariables>(ListingAgreementTeamDocument, variables, options);

export const UserNotificationsDocument = `
    query UserNotifications {
  userNotifications {
    totalCount
    newsCount
  }
}
    `;

export const useUserNotificationsQuery = <
      TData = GQLUserNotificationsQuery,
      TError = unknown
    >(
      variables?: GQLUserNotificationsQueryVariables,
      options?: Omit<UseQueryOptions<GQLUserNotificationsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLUserNotificationsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLUserNotificationsQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['UserNotifications'] : ['UserNotifications', variables],
    queryFn: fetchData<GQLUserNotificationsQuery, GQLUserNotificationsQueryVariables>(UserNotificationsDocument, variables),
    ...options
  }
    )};

useUserNotificationsQuery.getKey = (variables?: GQLUserNotificationsQueryVariables) => variables === undefined ? ['UserNotifications'] : ['UserNotifications', variables];

export const useInfiniteUserNotificationsQuery = <
      TData = InfiniteData<GQLUserNotificationsQuery>,
      TError = unknown
    >(
      variables: GQLUserNotificationsQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLUserNotificationsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLUserNotificationsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLUserNotificationsQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? variables === undefined ? ['UserNotifications.infinite'] : ['UserNotifications.infinite', variables],
      queryFn: (metaData) => fetchData<GQLUserNotificationsQuery, GQLUserNotificationsQueryVariables>(UserNotificationsDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteUserNotificationsQuery.getKey = (variables?: GQLUserNotificationsQueryVariables) => variables === undefined ? ['UserNotifications.infinite'] : ['UserNotifications.infinite', variables];


useUserNotificationsQuery.fetcher = (variables?: GQLUserNotificationsQueryVariables, options?: RequestInit['headers']) => fetchData<GQLUserNotificationsQuery, GQLUserNotificationsQueryVariables>(UserNotificationsDocument, variables, options);

export const MarkNewsAsReadDocument = `
    mutation MarkNewsAsRead($newsId: String!) {
  markNewsAsRead(newsId: $newsId)
}
    `;

export const useMarkNewsAsReadMutation = <
      TError = unknown,
      TContext = unknown
    >(options?: UseMutationOptions<GQLMarkNewsAsReadMutation, TError, GQLMarkNewsAsReadMutationVariables, TContext>) => {
    
    return useMutation<GQLMarkNewsAsReadMutation, TError, GQLMarkNewsAsReadMutationVariables, TContext>(
      {
    mutationKey: ['MarkNewsAsRead'],
    mutationFn: (variables?: GQLMarkNewsAsReadMutationVariables) => fetchData<GQLMarkNewsAsReadMutation, GQLMarkNewsAsReadMutationVariables>(MarkNewsAsReadDocument, variables)(),
    ...options
  }
    )};

useMarkNewsAsReadMutation.getKey = () => ['MarkNewsAsRead'];


useMarkNewsAsReadMutation.fetcher = (variables: GQLMarkNewsAsReadMutationVariables, options?: RequestInit['headers']) => fetchData<GQLMarkNewsAsReadMutation, GQLMarkNewsAsReadMutationVariables>(MarkNewsAsReadDocument, variables, options);

export const BrokersDocument = `
    query brokers {
  brokers {
    totalCount
    items {
      id
      employeeId
      name
      email
      employeeActive
      department {
        id
        name
        departmentId
      }
      image {
        small
      }
    }
  }
}
    `;

export const useBrokersQuery = <
      TData = GQLBrokersQuery,
      TError = unknown
    >(
      variables?: GQLBrokersQueryVariables,
      options?: Omit<UseQueryOptions<GQLBrokersQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLBrokersQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLBrokersQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['brokers'] : ['brokers', variables],
    queryFn: fetchData<GQLBrokersQuery, GQLBrokersQueryVariables>(BrokersDocument, variables),
    ...options
  }
    )};

useBrokersQuery.getKey = (variables?: GQLBrokersQueryVariables) => variables === undefined ? ['brokers'] : ['brokers', variables];

export const useInfiniteBrokersQuery = <
      TData = InfiniteData<GQLBrokersQuery>,
      TError = unknown
    >(
      variables: GQLBrokersQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLBrokersQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLBrokersQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLBrokersQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? variables === undefined ? ['brokers.infinite'] : ['brokers.infinite', variables],
      queryFn: (metaData) => fetchData<GQLBrokersQuery, GQLBrokersQueryVariables>(BrokersDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteBrokersQuery.getKey = (variables?: GQLBrokersQueryVariables) => variables === undefined ? ['brokers.infinite'] : ['brokers.infinite', variables];


useBrokersQuery.fetcher = (variables?: GQLBrokersQueryVariables, options?: RequestInit['headers']) => fetchData<GQLBrokersQuery, GQLBrokersQueryVariables>(BrokersDocument, variables, options);

export const DashboardAverageCommissionDocument = `
    query DashboardAverageCommission($type: DashboardType!) {
  dashboardAverageCommission(type: $type) {
    departmentName
    reference {
      type
      commission
      price
      salesCount
    }
    compare {
      type
      commission
      price
      salesCount
    }
  }
}
    `;

export const useDashboardAverageCommissionQuery = <
      TData = GQLDashboardAverageCommissionQuery,
      TError = unknown
    >(
      variables: GQLDashboardAverageCommissionQueryVariables,
      options?: Omit<UseQueryOptions<GQLDashboardAverageCommissionQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLDashboardAverageCommissionQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLDashboardAverageCommissionQuery, TError, TData>(
      {
    queryKey: ['DashboardAverageCommission', variables],
    queryFn: fetchData<GQLDashboardAverageCommissionQuery, GQLDashboardAverageCommissionQueryVariables>(DashboardAverageCommissionDocument, variables),
    ...options
  }
    )};

useDashboardAverageCommissionQuery.getKey = (variables: GQLDashboardAverageCommissionQueryVariables) => ['DashboardAverageCommission', variables];

export const useInfiniteDashboardAverageCommissionQuery = <
      TData = InfiniteData<GQLDashboardAverageCommissionQuery>,
      TError = unknown
    >(
      variables: GQLDashboardAverageCommissionQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLDashboardAverageCommissionQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLDashboardAverageCommissionQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLDashboardAverageCommissionQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['DashboardAverageCommission.infinite', variables],
      queryFn: (metaData) => fetchData<GQLDashboardAverageCommissionQuery, GQLDashboardAverageCommissionQueryVariables>(DashboardAverageCommissionDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteDashboardAverageCommissionQuery.getKey = (variables: GQLDashboardAverageCommissionQueryVariables) => ['DashboardAverageCommission.infinite', variables];


useDashboardAverageCommissionQuery.fetcher = (variables: GQLDashboardAverageCommissionQueryVariables, options?: RequestInit['headers']) => fetchData<GQLDashboardAverageCommissionQuery, GQLDashboardAverageCommissionQueryVariables>(DashboardAverageCommissionDocument, variables, options);

export const DashboardExpectedRevenueDocument = `
    query DashboardExpectedRevenue($type: DashboardType!) {
  dashboardExpectedRevenue(type: $type) {
    status
    value
    count
  }
}
    `;

export const useDashboardExpectedRevenueQuery = <
      TData = GQLDashboardExpectedRevenueQuery,
      TError = unknown
    >(
      variables: GQLDashboardExpectedRevenueQueryVariables,
      options?: Omit<UseQueryOptions<GQLDashboardExpectedRevenueQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLDashboardExpectedRevenueQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLDashboardExpectedRevenueQuery, TError, TData>(
      {
    queryKey: ['DashboardExpectedRevenue', variables],
    queryFn: fetchData<GQLDashboardExpectedRevenueQuery, GQLDashboardExpectedRevenueQueryVariables>(DashboardExpectedRevenueDocument, variables),
    ...options
  }
    )};

useDashboardExpectedRevenueQuery.getKey = (variables: GQLDashboardExpectedRevenueQueryVariables) => ['DashboardExpectedRevenue', variables];

export const useInfiniteDashboardExpectedRevenueQuery = <
      TData = InfiniteData<GQLDashboardExpectedRevenueQuery>,
      TError = unknown
    >(
      variables: GQLDashboardExpectedRevenueQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLDashboardExpectedRevenueQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLDashboardExpectedRevenueQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLDashboardExpectedRevenueQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['DashboardExpectedRevenue.infinite', variables],
      queryFn: (metaData) => fetchData<GQLDashboardExpectedRevenueQuery, GQLDashboardExpectedRevenueQueryVariables>(DashboardExpectedRevenueDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteDashboardExpectedRevenueQuery.getKey = (variables: GQLDashboardExpectedRevenueQueryVariables) => ['DashboardExpectedRevenue.infinite', variables];


useDashboardExpectedRevenueQuery.fetcher = (variables: GQLDashboardExpectedRevenueQueryVariables, options?: RequestInit['headers']) => fetchData<GQLDashboardExpectedRevenueQuery, GQLDashboardExpectedRevenueQueryVariables>(DashboardExpectedRevenueDocument, variables, options);

export const DashboardKeyFiguresDocument = `
    query DashboardKeyFigures($type: DashboardType!, $period: String!) {
  keyFigures: dashboardKeyFigures(type: $type, period: $period) {
    type
    value
    lastValue
    label
    format
  }
  dashboardLeads(type: $type, period: $period) {
    budget
    entries {
      name
      value
      departmentId
      budget
      actual
      current
      employeeCount
    }
  }
}
    `;

export const useDashboardKeyFiguresQuery = <
      TData = GQLDashboardKeyFiguresQuery,
      TError = unknown
    >(
      variables: GQLDashboardKeyFiguresQueryVariables,
      options?: Omit<UseQueryOptions<GQLDashboardKeyFiguresQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLDashboardKeyFiguresQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLDashboardKeyFiguresQuery, TError, TData>(
      {
    queryKey: ['DashboardKeyFigures', variables],
    queryFn: fetchData<GQLDashboardKeyFiguresQuery, GQLDashboardKeyFiguresQueryVariables>(DashboardKeyFiguresDocument, variables),
    ...options
  }
    )};

useDashboardKeyFiguresQuery.getKey = (variables: GQLDashboardKeyFiguresQueryVariables) => ['DashboardKeyFigures', variables];

export const useInfiniteDashboardKeyFiguresQuery = <
      TData = InfiniteData<GQLDashboardKeyFiguresQuery>,
      TError = unknown
    >(
      variables: GQLDashboardKeyFiguresQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLDashboardKeyFiguresQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLDashboardKeyFiguresQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLDashboardKeyFiguresQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['DashboardKeyFigures.infinite', variables],
      queryFn: (metaData) => fetchData<GQLDashboardKeyFiguresQuery, GQLDashboardKeyFiguresQueryVariables>(DashboardKeyFiguresDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteDashboardKeyFiguresQuery.getKey = (variables: GQLDashboardKeyFiguresQueryVariables) => ['DashboardKeyFigures.infinite', variables];


useDashboardKeyFiguresQuery.fetcher = (variables: GQLDashboardKeyFiguresQueryVariables, options?: RequestInit['headers']) => fetchData<GQLDashboardKeyFiguresQuery, GQLDashboardKeyFiguresQueryVariables>(DashboardKeyFiguresDocument, variables, options);

export const DashboardKtiDocument = `
    query DashboardKti {
  dashboardKti {
    current {
      name
      value
      ratings
    }
    others {
      name
      value
    }
  }
}
    `;

export const useDashboardKtiQuery = <
      TData = GQLDashboardKtiQuery,
      TError = unknown
    >(
      variables?: GQLDashboardKtiQueryVariables,
      options?: Omit<UseQueryOptions<GQLDashboardKtiQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLDashboardKtiQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLDashboardKtiQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['DashboardKti'] : ['DashboardKti', variables],
    queryFn: fetchData<GQLDashboardKtiQuery, GQLDashboardKtiQueryVariables>(DashboardKtiDocument, variables),
    ...options
  }
    )};

useDashboardKtiQuery.getKey = (variables?: GQLDashboardKtiQueryVariables) => variables === undefined ? ['DashboardKti'] : ['DashboardKti', variables];

export const useInfiniteDashboardKtiQuery = <
      TData = InfiniteData<GQLDashboardKtiQuery>,
      TError = unknown
    >(
      variables: GQLDashboardKtiQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLDashboardKtiQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLDashboardKtiQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLDashboardKtiQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? variables === undefined ? ['DashboardKti.infinite'] : ['DashboardKti.infinite', variables],
      queryFn: (metaData) => fetchData<GQLDashboardKtiQuery, GQLDashboardKtiQueryVariables>(DashboardKtiDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteDashboardKtiQuery.getKey = (variables?: GQLDashboardKtiQueryVariables) => variables === undefined ? ['DashboardKti.infinite'] : ['DashboardKti.infinite', variables];


useDashboardKtiQuery.fetcher = (variables?: GQLDashboardKtiQueryVariables, options?: RequestInit['headers']) => fetchData<GQLDashboardKtiQuery, GQLDashboardKtiQueryVariables>(DashboardKtiDocument, variables, options);

export const DashboardRevenueDocument = `
    query DashboardRevenue($type: DashboardType!) {
  dashboardRevenue(type: $type) {
    currentYearTotal
    percentageChange
    hitBudgetPercentage
    budgetTotal
    currentYearBudget
    previousYearTotal
    previousYearUntilNow
    entries {
      month
      current {
        year
        value
      }
      budget {
        year
        value
      }
      previous {
        year
        value
      }
    }
  }
}
    `;

export const useDashboardRevenueQuery = <
      TData = GQLDashboardRevenueQuery,
      TError = unknown
    >(
      variables: GQLDashboardRevenueQueryVariables,
      options?: Omit<UseQueryOptions<GQLDashboardRevenueQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLDashboardRevenueQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLDashboardRevenueQuery, TError, TData>(
      {
    queryKey: ['DashboardRevenue', variables],
    queryFn: fetchData<GQLDashboardRevenueQuery, GQLDashboardRevenueQueryVariables>(DashboardRevenueDocument, variables),
    ...options
  }
    )};

useDashboardRevenueQuery.getKey = (variables: GQLDashboardRevenueQueryVariables) => ['DashboardRevenue', variables];

export const useInfiniteDashboardRevenueQuery = <
      TData = InfiniteData<GQLDashboardRevenueQuery>,
      TError = unknown
    >(
      variables: GQLDashboardRevenueQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLDashboardRevenueQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLDashboardRevenueQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLDashboardRevenueQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['DashboardRevenue.infinite', variables],
      queryFn: (metaData) => fetchData<GQLDashboardRevenueQuery, GQLDashboardRevenueQueryVariables>(DashboardRevenueDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteDashboardRevenueQuery.getKey = (variables: GQLDashboardRevenueQueryVariables) => ['DashboardRevenue.infinite', variables];


useDashboardRevenueQuery.fetcher = (variables: GQLDashboardRevenueQueryVariables, options?: RequestInit['headers']) => fetchData<GQLDashboardRevenueQuery, GQLDashboardRevenueQueryVariables>(DashboardRevenueDocument, variables, options);

export const DashboardToplistDocument = `
    query DashboardToplist($section: ToplistSection!, $type: DashboardType!) {
  dashboardToplist(section: $section, type: $type) {
    section
    current {
      name
      value
      imageUrl
      position
    }
    topEntries {
      name
      value
      imageUrl
    }
  }
}
    `;

export const useDashboardToplistQuery = <
      TData = GQLDashboardToplistQuery,
      TError = unknown
    >(
      variables: GQLDashboardToplistQueryVariables,
      options?: Omit<UseQueryOptions<GQLDashboardToplistQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLDashboardToplistQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLDashboardToplistQuery, TError, TData>(
      {
    queryKey: ['DashboardToplist', variables],
    queryFn: fetchData<GQLDashboardToplistQuery, GQLDashboardToplistQueryVariables>(DashboardToplistDocument, variables),
    ...options
  }
    )};

useDashboardToplistQuery.getKey = (variables: GQLDashboardToplistQueryVariables) => ['DashboardToplist', variables];

export const useInfiniteDashboardToplistQuery = <
      TData = InfiniteData<GQLDashboardToplistQuery>,
      TError = unknown
    >(
      variables: GQLDashboardToplistQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLDashboardToplistQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLDashboardToplistQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLDashboardToplistQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['DashboardToplist.infinite', variables],
      queryFn: (metaData) => fetchData<GQLDashboardToplistQuery, GQLDashboardToplistQueryVariables>(DashboardToplistDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteDashboardToplistQuery.getKey = (variables: GQLDashboardToplistQueryVariables) => ['DashboardToplist.infinite', variables];


useDashboardToplistQuery.fetcher = (variables: GQLDashboardToplistQueryVariables, options?: RequestInit['headers']) => fetchData<GQLDashboardToplistQuery, GQLDashboardToplistQueryVariables>(DashboardToplistDocument, variables, options);

export const DashboardUpcomingActivitiesDocument = `
    query DashboardUpcomingActivities($type: DashboardType!) {
  dashboardUpcomingActivities(type: $type) {
    start
    end
    type
    typeName
    name
    performedById
    value
    done
    id
    estateId
    estateAddress
  }
}
    `;

export const useDashboardUpcomingActivitiesQuery = <
      TData = GQLDashboardUpcomingActivitiesQuery,
      TError = unknown
    >(
      variables: GQLDashboardUpcomingActivitiesQueryVariables,
      options?: Omit<UseQueryOptions<GQLDashboardUpcomingActivitiesQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLDashboardUpcomingActivitiesQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLDashboardUpcomingActivitiesQuery, TError, TData>(
      {
    queryKey: ['DashboardUpcomingActivities', variables],
    queryFn: fetchData<GQLDashboardUpcomingActivitiesQuery, GQLDashboardUpcomingActivitiesQueryVariables>(DashboardUpcomingActivitiesDocument, variables),
    ...options
  }
    )};

useDashboardUpcomingActivitiesQuery.getKey = (variables: GQLDashboardUpcomingActivitiesQueryVariables) => ['DashboardUpcomingActivities', variables];

export const useInfiniteDashboardUpcomingActivitiesQuery = <
      TData = InfiniteData<GQLDashboardUpcomingActivitiesQuery>,
      TError = unknown
    >(
      variables: GQLDashboardUpcomingActivitiesQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLDashboardUpcomingActivitiesQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLDashboardUpcomingActivitiesQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLDashboardUpcomingActivitiesQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['DashboardUpcomingActivities.infinite', variables],
      queryFn: (metaData) => fetchData<GQLDashboardUpcomingActivitiesQuery, GQLDashboardUpcomingActivitiesQueryVariables>(DashboardUpcomingActivitiesDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteDashboardUpcomingActivitiesQuery.getKey = (variables: GQLDashboardUpcomingActivitiesQueryVariables) => ['DashboardUpcomingActivities.infinite', variables];


useDashboardUpcomingActivitiesQuery.fetcher = (variables: GQLDashboardUpcomingActivitiesQueryVariables, options?: RequestInit['headers']) => fetchData<GQLDashboardUpcomingActivitiesQuery, GQLDashboardUpcomingActivitiesQueryVariables>(DashboardUpcomingActivitiesDocument, variables, options);

export const DashboardImportantTasksDocument = `
    query DashboardImportantTasks {
  dashboardImportantTasks {
    type
    estateId
    estateAddress
    mainBrokerId
    signUrl
    eiendomsverdiUrl
    amlUrl
  }
}
    `;

export const useDashboardImportantTasksQuery = <
      TData = GQLDashboardImportantTasksQuery,
      TError = unknown
    >(
      variables?: GQLDashboardImportantTasksQueryVariables,
      options?: Omit<UseQueryOptions<GQLDashboardImportantTasksQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLDashboardImportantTasksQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLDashboardImportantTasksQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['DashboardImportantTasks'] : ['DashboardImportantTasks', variables],
    queryFn: fetchData<GQLDashboardImportantTasksQuery, GQLDashboardImportantTasksQueryVariables>(DashboardImportantTasksDocument, variables),
    ...options
  }
    )};

useDashboardImportantTasksQuery.getKey = (variables?: GQLDashboardImportantTasksQueryVariables) => variables === undefined ? ['DashboardImportantTasks'] : ['DashboardImportantTasks', variables];

export const useInfiniteDashboardImportantTasksQuery = <
      TData = InfiniteData<GQLDashboardImportantTasksQuery>,
      TError = unknown
    >(
      variables: GQLDashboardImportantTasksQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLDashboardImportantTasksQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLDashboardImportantTasksQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLDashboardImportantTasksQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? variables === undefined ? ['DashboardImportantTasks.infinite'] : ['DashboardImportantTasks.infinite', variables],
      queryFn: (metaData) => fetchData<GQLDashboardImportantTasksQuery, GQLDashboardImportantTasksQueryVariables>(DashboardImportantTasksDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteDashboardImportantTasksQuery.getKey = (variables?: GQLDashboardImportantTasksQueryVariables) => variables === undefined ? ['DashboardImportantTasks.infinite'] : ['DashboardImportantTasks.infinite', variables];


useDashboardImportantTasksQuery.fetcher = (variables?: GQLDashboardImportantTasksQueryVariables, options?: RequestInit['headers']) => fetchData<GQLDashboardImportantTasksQuery, GQLDashboardImportantTasksQueryVariables>(DashboardImportantTasksDocument, variables, options);

export const IncidentsDocument = `
    query Incidents($active: Boolean) {
  cmsIncidents(active: $active) {
    items {
      ...Incident
    }
    meta {
      total
      currentPage
      totalPages
    }
  }
}
    ${IncidentFragmentDoc}`;

export const useIncidentsQuery = <
      TData = GQLIncidentsQuery,
      TError = unknown
    >(
      variables?: GQLIncidentsQueryVariables,
      options?: Omit<UseQueryOptions<GQLIncidentsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLIncidentsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLIncidentsQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['Incidents'] : ['Incidents', variables],
    queryFn: fetchData<GQLIncidentsQuery, GQLIncidentsQueryVariables>(IncidentsDocument, variables),
    ...options
  }
    )};

useIncidentsQuery.getKey = (variables?: GQLIncidentsQueryVariables) => variables === undefined ? ['Incidents'] : ['Incidents', variables];

export const useInfiniteIncidentsQuery = <
      TData = InfiniteData<GQLIncidentsQuery>,
      TError = unknown
    >(
      variables: GQLIncidentsQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLIncidentsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLIncidentsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLIncidentsQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? variables === undefined ? ['Incidents.infinite'] : ['Incidents.infinite', variables],
      queryFn: (metaData) => fetchData<GQLIncidentsQuery, GQLIncidentsQueryVariables>(IncidentsDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteIncidentsQuery.getKey = (variables?: GQLIncidentsQueryVariables) => variables === undefined ? ['Incidents.infinite'] : ['Incidents.infinite', variables];


useIncidentsQuery.fetcher = (variables?: GQLIncidentsQueryVariables, options?: RequestInit['headers']) => fetchData<GQLIncidentsQuery, GQLIncidentsQueryVariables>(IncidentsDocument, variables, options);

export const IncidentBySlugDocument = `
    query IncidentBySlug($slug: String!) {
  cmsIncidentBySlug(slug: $slug) {
    ...Incident
  }
}
    ${IncidentFragmentDoc}`;

export const useIncidentBySlugQuery = <
      TData = GQLIncidentBySlugQuery,
      TError = unknown
    >(
      variables: GQLIncidentBySlugQueryVariables,
      options?: Omit<UseQueryOptions<GQLIncidentBySlugQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLIncidentBySlugQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLIncidentBySlugQuery, TError, TData>(
      {
    queryKey: ['IncidentBySlug', variables],
    queryFn: fetchData<GQLIncidentBySlugQuery, GQLIncidentBySlugQueryVariables>(IncidentBySlugDocument, variables),
    ...options
  }
    )};

useIncidentBySlugQuery.getKey = (variables: GQLIncidentBySlugQueryVariables) => ['IncidentBySlug', variables];

export const useInfiniteIncidentBySlugQuery = <
      TData = InfiniteData<GQLIncidentBySlugQuery>,
      TError = unknown
    >(
      variables: GQLIncidentBySlugQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLIncidentBySlugQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLIncidentBySlugQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLIncidentBySlugQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['IncidentBySlug.infinite', variables],
      queryFn: (metaData) => fetchData<GQLIncidentBySlugQuery, GQLIncidentBySlugQueryVariables>(IncidentBySlugDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteIncidentBySlugQuery.getKey = (variables: GQLIncidentBySlugQueryVariables) => ['IncidentBySlug.infinite', variables];


useIncidentBySlugQuery.fetcher = (variables: GQLIncidentBySlugQueryVariables, options?: RequestInit['headers']) => fetchData<GQLIncidentBySlugQuery, GQLIncidentBySlugQueryVariables>(IncidentBySlugDocument, variables, options);

export const KtiPageRatingsDocument = `
    query KtiPageRatings($limit: Int, $offset: Int, $dateFrom: DateTime, $dateTo: DateTime, $hasReview: Boolean, $sortBy: BrokerRatingSortBy, $sortDir: SortDirection, $featured: Boolean, $rating: Int) {
  currentBrokerRatings(
    limit: $limit
    offset: $offset
    dateFrom: $dateFrom
    dateTo: $dateTo
    hasReview: $hasReview
    sortBy: $sortBy
    sortDir: $sortDir
    featured: $featured
    rating: $rating
  ) {
    data {
      ratingId
      createdAt
      employeeId
      rating
      userName
      featured
      review {
        reviewId
        text
        createdAt
      }
    }
    pagination {
      count
      limit
      total
      offset
    }
  }
}
    `;

export const useKtiPageRatingsQuery = <
      TData = GQLKtiPageRatingsQuery,
      TError = unknown
    >(
      variables?: GQLKtiPageRatingsQueryVariables,
      options?: Omit<UseQueryOptions<GQLKtiPageRatingsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLKtiPageRatingsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLKtiPageRatingsQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['KtiPageRatings'] : ['KtiPageRatings', variables],
    queryFn: fetchData<GQLKtiPageRatingsQuery, GQLKtiPageRatingsQueryVariables>(KtiPageRatingsDocument, variables),
    ...options
  }
    )};

useKtiPageRatingsQuery.getKey = (variables?: GQLKtiPageRatingsQueryVariables) => variables === undefined ? ['KtiPageRatings'] : ['KtiPageRatings', variables];

export const useInfiniteKtiPageRatingsQuery = <
      TData = InfiniteData<GQLKtiPageRatingsQuery>,
      TError = unknown
    >(
      variables: GQLKtiPageRatingsQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLKtiPageRatingsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLKtiPageRatingsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLKtiPageRatingsQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? variables === undefined ? ['KtiPageRatings.infinite'] : ['KtiPageRatings.infinite', variables],
      queryFn: (metaData) => fetchData<GQLKtiPageRatingsQuery, GQLKtiPageRatingsQueryVariables>(KtiPageRatingsDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteKtiPageRatingsQuery.getKey = (variables?: GQLKtiPageRatingsQueryVariables) => variables === undefined ? ['KtiPageRatings.infinite'] : ['KtiPageRatings.infinite', variables];


useKtiPageRatingsQuery.fetcher = (variables?: GQLKtiPageRatingsQueryVariables, options?: RequestInit['headers']) => fetchData<GQLKtiPageRatingsQuery, GQLKtiPageRatingsQueryVariables>(KtiPageRatingsDocument, variables, options);

export const BrokerRatingsTotalDocument = `
    query BrokerRatingsTotal($dateFrom: DateTime, $dateTo: DateTime, $hasReview: Boolean, $featured: Boolean, $rating: Int) {
  currentBrokerRatingsTotal(
    dateFrom: $dateFrom
    dateTo: $dateTo
    hasReview: $hasReview
    featured: $featured
    rating: $rating
  ) {
    allDates
    allRatings
    fiveStar
    fourStar
    threeStar
    twoStar
    oneStar
    withReview
    last30Days
    lastYear
    currentYear
  }
}
    `;

export const useBrokerRatingsTotalQuery = <
      TData = GQLBrokerRatingsTotalQuery,
      TError = unknown
    >(
      variables?: GQLBrokerRatingsTotalQueryVariables,
      options?: Omit<UseQueryOptions<GQLBrokerRatingsTotalQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLBrokerRatingsTotalQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLBrokerRatingsTotalQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['BrokerRatingsTotal'] : ['BrokerRatingsTotal', variables],
    queryFn: fetchData<GQLBrokerRatingsTotalQuery, GQLBrokerRatingsTotalQueryVariables>(BrokerRatingsTotalDocument, variables),
    ...options
  }
    )};

useBrokerRatingsTotalQuery.getKey = (variables?: GQLBrokerRatingsTotalQueryVariables) => variables === undefined ? ['BrokerRatingsTotal'] : ['BrokerRatingsTotal', variables];

export const useInfiniteBrokerRatingsTotalQuery = <
      TData = InfiniteData<GQLBrokerRatingsTotalQuery>,
      TError = unknown
    >(
      variables: GQLBrokerRatingsTotalQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLBrokerRatingsTotalQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLBrokerRatingsTotalQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLBrokerRatingsTotalQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? variables === undefined ? ['BrokerRatingsTotal.infinite'] : ['BrokerRatingsTotal.infinite', variables],
      queryFn: (metaData) => fetchData<GQLBrokerRatingsTotalQuery, GQLBrokerRatingsTotalQueryVariables>(BrokerRatingsTotalDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteBrokerRatingsTotalQuery.getKey = (variables?: GQLBrokerRatingsTotalQueryVariables) => variables === undefined ? ['BrokerRatingsTotal.infinite'] : ['BrokerRatingsTotal.infinite', variables];


useBrokerRatingsTotalQuery.fetcher = (variables?: GQLBrokerRatingsTotalQueryVariables, options?: RequestInit['headers']) => fetchData<GQLBrokerRatingsTotalQuery, GQLBrokerRatingsTotalQueryVariables>(BrokerRatingsTotalDocument, variables, options);

export const KtiPageBrokerRatingsCurrentYearDocument = `
    query KtiPageBrokerRatingsCurrentYear {
  currentBroker {
    rating {
      weighted
    }
  }
  ratings(ytd: true) {
    average
    count
  }
}
    `;

export const useKtiPageBrokerRatingsCurrentYearQuery = <
      TData = GQLKtiPageBrokerRatingsCurrentYearQuery,
      TError = unknown
    >(
      variables?: GQLKtiPageBrokerRatingsCurrentYearQueryVariables,
      options?: Omit<UseQueryOptions<GQLKtiPageBrokerRatingsCurrentYearQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLKtiPageBrokerRatingsCurrentYearQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLKtiPageBrokerRatingsCurrentYearQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['KtiPageBrokerRatingsCurrentYear'] : ['KtiPageBrokerRatingsCurrentYear', variables],
    queryFn: fetchData<GQLKtiPageBrokerRatingsCurrentYearQuery, GQLKtiPageBrokerRatingsCurrentYearQueryVariables>(KtiPageBrokerRatingsCurrentYearDocument, variables),
    ...options
  }
    )};

useKtiPageBrokerRatingsCurrentYearQuery.getKey = (variables?: GQLKtiPageBrokerRatingsCurrentYearQueryVariables) => variables === undefined ? ['KtiPageBrokerRatingsCurrentYear'] : ['KtiPageBrokerRatingsCurrentYear', variables];

export const useInfiniteKtiPageBrokerRatingsCurrentYearQuery = <
      TData = InfiniteData<GQLKtiPageBrokerRatingsCurrentYearQuery>,
      TError = unknown
    >(
      variables: GQLKtiPageBrokerRatingsCurrentYearQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLKtiPageBrokerRatingsCurrentYearQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLKtiPageBrokerRatingsCurrentYearQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLKtiPageBrokerRatingsCurrentYearQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? variables === undefined ? ['KtiPageBrokerRatingsCurrentYear.infinite'] : ['KtiPageBrokerRatingsCurrentYear.infinite', variables],
      queryFn: (metaData) => fetchData<GQLKtiPageBrokerRatingsCurrentYearQuery, GQLKtiPageBrokerRatingsCurrentYearQueryVariables>(KtiPageBrokerRatingsCurrentYearDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteKtiPageBrokerRatingsCurrentYearQuery.getKey = (variables?: GQLKtiPageBrokerRatingsCurrentYearQueryVariables) => variables === undefined ? ['KtiPageBrokerRatingsCurrentYear.infinite'] : ['KtiPageBrokerRatingsCurrentYear.infinite', variables];


useKtiPageBrokerRatingsCurrentYearQuery.fetcher = (variables?: GQLKtiPageBrokerRatingsCurrentYearQueryVariables, options?: RequestInit['headers']) => fetchData<GQLKtiPageBrokerRatingsCurrentYearQuery, GQLKtiPageBrokerRatingsCurrentYearQueryVariables>(KtiPageBrokerRatingsCurrentYearDocument, variables, options);

export const KtiPageAllSectionsDocument = `
    query KtiPageAllSections {
  currentBrokerKti(includeDepartment: true, includeNordvik: true) {
    broker {
      name
      averageRating
      ratingCount
      reviewCount
    }
    brokerDepartment {
      name
      averageRating
      ratingCount
      reviewCount
    }
    nordvik {
      name
      averageRating
      ratingCount
      reviewCount
    }
  }
}
    `;

export const useKtiPageAllSectionsQuery = <
      TData = GQLKtiPageAllSectionsQuery,
      TError = unknown
    >(
      variables?: GQLKtiPageAllSectionsQueryVariables,
      options?: Omit<UseQueryOptions<GQLKtiPageAllSectionsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLKtiPageAllSectionsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLKtiPageAllSectionsQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['KtiPageAllSections'] : ['KtiPageAllSections', variables],
    queryFn: fetchData<GQLKtiPageAllSectionsQuery, GQLKtiPageAllSectionsQueryVariables>(KtiPageAllSectionsDocument, variables),
    ...options
  }
    )};

useKtiPageAllSectionsQuery.getKey = (variables?: GQLKtiPageAllSectionsQueryVariables) => variables === undefined ? ['KtiPageAllSections'] : ['KtiPageAllSections', variables];

export const useInfiniteKtiPageAllSectionsQuery = <
      TData = InfiniteData<GQLKtiPageAllSectionsQuery>,
      TError = unknown
    >(
      variables: GQLKtiPageAllSectionsQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLKtiPageAllSectionsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLKtiPageAllSectionsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLKtiPageAllSectionsQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? variables === undefined ? ['KtiPageAllSections.infinite'] : ['KtiPageAllSections.infinite', variables],
      queryFn: (metaData) => fetchData<GQLKtiPageAllSectionsQuery, GQLKtiPageAllSectionsQueryVariables>(KtiPageAllSectionsDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteKtiPageAllSectionsQuery.getKey = (variables?: GQLKtiPageAllSectionsQueryVariables) => variables === undefined ? ['KtiPageAllSections.infinite'] : ['KtiPageAllSections.infinite', variables];


useKtiPageAllSectionsQuery.fetcher = (variables?: GQLKtiPageAllSectionsQueryVariables, options?: RequestInit['headers']) => fetchData<GQLKtiPageAllSectionsQuery, GQLKtiPageAllSectionsQueryVariables>(KtiPageAllSectionsDocument, variables, options);

export const JobsListingDocument = `
    query JobsListing {
  cmsJobsListing {
    items {
      id
      slug
      title
      excerpt
      postDate
      image {
        small
        medium
        large
      }
      author {
        name
        email
        avatarUrl
      }
    }
    meta {
      total
      currentPage
      totalPages
    }
  }
}
    `;

export const useJobsListingQuery = <
      TData = GQLJobsListingQuery,
      TError = unknown
    >(
      variables?: GQLJobsListingQueryVariables,
      options?: Omit<UseQueryOptions<GQLJobsListingQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLJobsListingQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLJobsListingQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['JobsListing'] : ['JobsListing', variables],
    queryFn: fetchData<GQLJobsListingQuery, GQLJobsListingQueryVariables>(JobsListingDocument, variables),
    ...options
  }
    )};

useJobsListingQuery.getKey = (variables?: GQLJobsListingQueryVariables) => variables === undefined ? ['JobsListing'] : ['JobsListing', variables];

export const useInfiniteJobsListingQuery = <
      TData = InfiniteData<GQLJobsListingQuery>,
      TError = unknown
    >(
      variables: GQLJobsListingQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLJobsListingQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLJobsListingQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLJobsListingQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? variables === undefined ? ['JobsListing.infinite'] : ['JobsListing.infinite', variables],
      queryFn: (metaData) => fetchData<GQLJobsListingQuery, GQLJobsListingQueryVariables>(JobsListingDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteJobsListingQuery.getKey = (variables?: GQLJobsListingQueryVariables) => variables === undefined ? ['JobsListing.infinite'] : ['JobsListing.infinite', variables];


useJobsListingQuery.fetcher = (variables?: GQLJobsListingQueryVariables, options?: RequestInit['headers']) => fetchData<GQLJobsListingQuery, GQLJobsListingQueryVariables>(JobsListingDocument, variables, options);

export const JobListingBySlugDocument = `
    query JobListingBySlug($slug: String!) {
  cmsJobBySlug(slug: $slug) {
    id
    slug
    title
    excerpt
    postDate
    image {
      small
      medium
      large
    }
    author {
      name
      email
      avatarUrl
    }
    modules {
      type
      body
    }
  }
}
    `;

export const useJobListingBySlugQuery = <
      TData = GQLJobListingBySlugQuery,
      TError = unknown
    >(
      variables: GQLJobListingBySlugQueryVariables,
      options?: Omit<UseQueryOptions<GQLJobListingBySlugQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLJobListingBySlugQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLJobListingBySlugQuery, TError, TData>(
      {
    queryKey: ['JobListingBySlug', variables],
    queryFn: fetchData<GQLJobListingBySlugQuery, GQLJobListingBySlugQueryVariables>(JobListingBySlugDocument, variables),
    ...options
  }
    )};

useJobListingBySlugQuery.getKey = (variables: GQLJobListingBySlugQueryVariables) => ['JobListingBySlug', variables];

export const useInfiniteJobListingBySlugQuery = <
      TData = InfiniteData<GQLJobListingBySlugQuery>,
      TError = unknown
    >(
      variables: GQLJobListingBySlugQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLJobListingBySlugQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLJobListingBySlugQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLJobListingBySlugQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['JobListingBySlug.infinite', variables],
      queryFn: (metaData) => fetchData<GQLJobListingBySlugQuery, GQLJobListingBySlugQueryVariables>(JobListingBySlugDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteJobListingBySlugQuery.getKey = (variables: GQLJobListingBySlugQueryVariables) => ['JobListingBySlug.infinite', variables];


useJobListingBySlugQuery.fetcher = (variables: GQLJobListingBySlugQueryVariables, options?: RequestInit['headers']) => fetchData<GQLJobListingBySlugQuery, GQLJobListingBySlugQueryVariables>(JobListingBySlugDocument, variables, options);

export const CmsArticlesDocument = `
    query CmsArticles($categorySlug: String, $type: CmsArticleType, $includeViewerHasRead: Boolean!, $searchQuery: String, $limit: Int, $page: Int, $userId: String) {
  cmsArticles(
    categorySlug: $categorySlug
    type: $type
    searchQuery: $searchQuery
    limit: $limit
    page: $page
  ) {
    meta {
      ...ArticlesMeta
    }
    items {
      ...ArticlesList
      viewerHasRead(userId: $userId) @include(if: $includeViewerHasRead)
    }
  }
}
    ${ArticlesMetaFragmentDoc}
${ArticlesListFragmentDoc}`;

export const useCmsArticlesQuery = <
      TData = GQLCmsArticlesQuery,
      TError = unknown
    >(
      variables: GQLCmsArticlesQueryVariables,
      options?: Omit<UseQueryOptions<GQLCmsArticlesQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLCmsArticlesQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLCmsArticlesQuery, TError, TData>(
      {
    queryKey: ['CmsArticles', variables],
    queryFn: fetchData<GQLCmsArticlesQuery, GQLCmsArticlesQueryVariables>(CmsArticlesDocument, variables),
    ...options
  }
    )};

useCmsArticlesQuery.getKey = (variables: GQLCmsArticlesQueryVariables) => ['CmsArticles', variables];

export const useInfiniteCmsArticlesQuery = <
      TData = InfiniteData<GQLCmsArticlesQuery>,
      TError = unknown
    >(
      variables: GQLCmsArticlesQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLCmsArticlesQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLCmsArticlesQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLCmsArticlesQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['CmsArticles.infinite', variables],
      queryFn: (metaData) => fetchData<GQLCmsArticlesQuery, GQLCmsArticlesQueryVariables>(CmsArticlesDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteCmsArticlesQuery.getKey = (variables: GQLCmsArticlesQueryVariables) => ['CmsArticles.infinite', variables];


useCmsArticlesQuery.fetcher = (variables: GQLCmsArticlesQueryVariables, options?: RequestInit['headers']) => fetchData<GQLCmsArticlesQuery, GQLCmsArticlesQueryVariables>(CmsArticlesDocument, variables, options);

export const CmsArticleCategoriesDocument = `
    query CmsArticleCategories($type: CmsArticleType) {
  cmsArticleCategories(type: $type) {
    ...ArticleCategory
  }
}
    ${ArticleCategoryFragmentDoc}`;

export const useCmsArticleCategoriesQuery = <
      TData = GQLCmsArticleCategoriesQuery,
      TError = unknown
    >(
      variables?: GQLCmsArticleCategoriesQueryVariables,
      options?: Omit<UseQueryOptions<GQLCmsArticleCategoriesQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLCmsArticleCategoriesQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLCmsArticleCategoriesQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['CmsArticleCategories'] : ['CmsArticleCategories', variables],
    queryFn: fetchData<GQLCmsArticleCategoriesQuery, GQLCmsArticleCategoriesQueryVariables>(CmsArticleCategoriesDocument, variables),
    ...options
  }
    )};

useCmsArticleCategoriesQuery.getKey = (variables?: GQLCmsArticleCategoriesQueryVariables) => variables === undefined ? ['CmsArticleCategories'] : ['CmsArticleCategories', variables];

export const useInfiniteCmsArticleCategoriesQuery = <
      TData = InfiniteData<GQLCmsArticleCategoriesQuery>,
      TError = unknown
    >(
      variables: GQLCmsArticleCategoriesQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLCmsArticleCategoriesQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLCmsArticleCategoriesQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLCmsArticleCategoriesQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? variables === undefined ? ['CmsArticleCategories.infinite'] : ['CmsArticleCategories.infinite', variables],
      queryFn: (metaData) => fetchData<GQLCmsArticleCategoriesQuery, GQLCmsArticleCategoriesQueryVariables>(CmsArticleCategoriesDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteCmsArticleCategoriesQuery.getKey = (variables?: GQLCmsArticleCategoriesQueryVariables) => variables === undefined ? ['CmsArticleCategories.infinite'] : ['CmsArticleCategories.infinite', variables];


useCmsArticleCategoriesQuery.fetcher = (variables?: GQLCmsArticleCategoriesQueryVariables, options?: RequestInit['headers']) => fetchData<GQLCmsArticleCategoriesQuery, GQLCmsArticleCategoriesQueryVariables>(CmsArticleCategoriesDocument, variables, options);

export const ArticleItemDocument = `
    query ArticleItem($slug: String!) {
  cmsArticleBySlug(slug: $slug) {
    ...ArticlePreview
  }
}
    ${ArticlePreviewFragmentDoc}`;

export const useArticleItemQuery = <
      TData = GQLArticleItemQuery,
      TError = unknown
    >(
      variables: GQLArticleItemQueryVariables,
      options?: Omit<UseQueryOptions<GQLArticleItemQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLArticleItemQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLArticleItemQuery, TError, TData>(
      {
    queryKey: ['ArticleItem', variables],
    queryFn: fetchData<GQLArticleItemQuery, GQLArticleItemQueryVariables>(ArticleItemDocument, variables),
    ...options
  }
    )};

useArticleItemQuery.getKey = (variables: GQLArticleItemQueryVariables) => ['ArticleItem', variables];

export const useInfiniteArticleItemQuery = <
      TData = InfiniteData<GQLArticleItemQuery>,
      TError = unknown
    >(
      variables: GQLArticleItemQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLArticleItemQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLArticleItemQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLArticleItemQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['ArticleItem.infinite', variables],
      queryFn: (metaData) => fetchData<GQLArticleItemQuery, GQLArticleItemQueryVariables>(ArticleItemDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteArticleItemQuery.getKey = (variables: GQLArticleItemQueryVariables) => ['ArticleItem.infinite', variables];


useArticleItemQuery.fetcher = (variables: GQLArticleItemQueryVariables, options?: RequestInit['headers']) => fetchData<GQLArticleItemQuery, GQLArticleItemQueryVariables>(ArticleItemDocument, variables, options);

export const CmsChangelogsDocument = `
    query CmsChangelogs($categorySlug: String, $searchQuery: String, $limit: Int, $page: Int, $dateFrom: DateTime, $dateTo: DateTime) {
  cmsChangelogs(
    categorySlug: $categorySlug
    searchQuery: $searchQuery
    limit: $limit
    page: $page
    dateFrom: $dateFrom
    dateTo: $dateTo
  ) {
    meta {
      ...ArticlesMeta
    }
    items {
      ...ChangelogPreview
    }
  }
  readArticles {
    id
    readAt
  }
}
    ${ArticlesMetaFragmentDoc}
${ChangelogPreviewFragmentDoc}`;

export const useCmsChangelogsQuery = <
      TData = GQLCmsChangelogsQuery,
      TError = unknown
    >(
      variables?: GQLCmsChangelogsQueryVariables,
      options?: Omit<UseQueryOptions<GQLCmsChangelogsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLCmsChangelogsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLCmsChangelogsQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['CmsChangelogs'] : ['CmsChangelogs', variables],
    queryFn: fetchData<GQLCmsChangelogsQuery, GQLCmsChangelogsQueryVariables>(CmsChangelogsDocument, variables),
    ...options
  }
    )};

useCmsChangelogsQuery.getKey = (variables?: GQLCmsChangelogsQueryVariables) => variables === undefined ? ['CmsChangelogs'] : ['CmsChangelogs', variables];

export const useInfiniteCmsChangelogsQuery = <
      TData = InfiniteData<GQLCmsChangelogsQuery>,
      TError = unknown
    >(
      variables: GQLCmsChangelogsQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLCmsChangelogsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLCmsChangelogsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLCmsChangelogsQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? variables === undefined ? ['CmsChangelogs.infinite'] : ['CmsChangelogs.infinite', variables],
      queryFn: (metaData) => fetchData<GQLCmsChangelogsQuery, GQLCmsChangelogsQueryVariables>(CmsChangelogsDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteCmsChangelogsQuery.getKey = (variables?: GQLCmsChangelogsQueryVariables) => variables === undefined ? ['CmsChangelogs.infinite'] : ['CmsChangelogs.infinite', variables];


useCmsChangelogsQuery.fetcher = (variables?: GQLCmsChangelogsQueryVariables, options?: RequestInit['headers']) => fetchData<GQLCmsChangelogsQuery, GQLCmsChangelogsQueryVariables>(CmsChangelogsDocument, variables, options);

export const CmsChangelogsCategoriesDocument = `
    query CmsChangelogsCategories {
  cmsArticleCategories(type: changelog) {
    ...ArticleCategory
  }
}
    ${ArticleCategoryFragmentDoc}`;

export const useCmsChangelogsCategoriesQuery = <
      TData = GQLCmsChangelogsCategoriesQuery,
      TError = unknown
    >(
      variables?: GQLCmsChangelogsCategoriesQueryVariables,
      options?: Omit<UseQueryOptions<GQLCmsChangelogsCategoriesQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLCmsChangelogsCategoriesQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLCmsChangelogsCategoriesQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['CmsChangelogsCategories'] : ['CmsChangelogsCategories', variables],
    queryFn: fetchData<GQLCmsChangelogsCategoriesQuery, GQLCmsChangelogsCategoriesQueryVariables>(CmsChangelogsCategoriesDocument, variables),
    ...options
  }
    )};

useCmsChangelogsCategoriesQuery.getKey = (variables?: GQLCmsChangelogsCategoriesQueryVariables) => variables === undefined ? ['CmsChangelogsCategories'] : ['CmsChangelogsCategories', variables];

export const useInfiniteCmsChangelogsCategoriesQuery = <
      TData = InfiniteData<GQLCmsChangelogsCategoriesQuery>,
      TError = unknown
    >(
      variables: GQLCmsChangelogsCategoriesQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLCmsChangelogsCategoriesQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLCmsChangelogsCategoriesQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLCmsChangelogsCategoriesQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? variables === undefined ? ['CmsChangelogsCategories.infinite'] : ['CmsChangelogsCategories.infinite', variables],
      queryFn: (metaData) => fetchData<GQLCmsChangelogsCategoriesQuery, GQLCmsChangelogsCategoriesQueryVariables>(CmsChangelogsCategoriesDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteCmsChangelogsCategoriesQuery.getKey = (variables?: GQLCmsChangelogsCategoriesQueryVariables) => variables === undefined ? ['CmsChangelogsCategories.infinite'] : ['CmsChangelogsCategories.infinite', variables];


useCmsChangelogsCategoriesQuery.fetcher = (variables?: GQLCmsChangelogsCategoriesQueryVariables, options?: RequestInit['headers']) => fetchData<GQLCmsChangelogsCategoriesQuery, GQLCmsChangelogsCategoriesQueryVariables>(CmsChangelogsCategoriesDocument, variables, options);

export const ChangelogItemDocument = `
    query ChangelogItem($slug: String!) {
  cmsArticleBySlug(slug: $slug) {
    ...ArticlePreview
  }
}
    ${ArticlePreviewFragmentDoc}`;

export const useChangelogItemQuery = <
      TData = GQLChangelogItemQuery,
      TError = unknown
    >(
      variables: GQLChangelogItemQueryVariables,
      options?: Omit<UseQueryOptions<GQLChangelogItemQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLChangelogItemQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLChangelogItemQuery, TError, TData>(
      {
    queryKey: ['ChangelogItem', variables],
    queryFn: fetchData<GQLChangelogItemQuery, GQLChangelogItemQueryVariables>(ChangelogItemDocument, variables),
    ...options
  }
    )};

useChangelogItemQuery.getKey = (variables: GQLChangelogItemQueryVariables) => ['ChangelogItem', variables];

export const useInfiniteChangelogItemQuery = <
      TData = InfiniteData<GQLChangelogItemQuery>,
      TError = unknown
    >(
      variables: GQLChangelogItemQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLChangelogItemQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLChangelogItemQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLChangelogItemQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['ChangelogItem.infinite', variables],
      queryFn: (metaData) => fetchData<GQLChangelogItemQuery, GQLChangelogItemQueryVariables>(ChangelogItemDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteChangelogItemQuery.getKey = (variables: GQLChangelogItemQueryVariables) => ['ChangelogItem.infinite', variables];


useChangelogItemQuery.fetcher = (variables: GQLChangelogItemQueryVariables, options?: RequestInit['headers']) => fetchData<GQLChangelogItemQuery, GQLChangelogItemQueryVariables>(ChangelogItemDocument, variables, options);

export const EstateByIdDocument = `
    query estateById($id: String!) {
  estate(id: $id) {
    status
    id
    estateId
    address {
      streetAddress
      city
      zipCode
      municipality
    }
    latitude
    longitude
    assignmentNumber
    assignmentType
    isValuation
    isEtakstPublished
    ownAssignmentType
    noOfBedRooms
    noOfRooms
    soldDate
    commissionAcceptedDate
    linkToNext
    finn {
      finnExpireDate
      finnPublishDate
    }
    estatePrice {
      totalPrice
      priceSuggestion
      soldPrice
    }
    sumArea {
      braI
      pRom
    }
    areaSize {
      BRAItotal
    }
    mainImage {
      large
    }
    placeholderImage
    links {
      linkType
      url
      text
    }
    sellers {
      firstName
      lastName
      email
      mobilePhone
      mainContact
      contactId
    }
    showings {
      start
      end
      showingId
    }
    activities {
      start
      end
      type
      typeName
      name
      performedById
      done
      id
      value
    }
    upcomingEvents {
      start
      end
      type
      typeName
      name
      performedById
      done
      id
    }
    linkToNext
    hjemUrl
    inspectionFolder {
      id
      publishedAt
    }
    listingAgreement {
      id
      updatedAt
      createdAt
      signicatDocumentId
      feePercentage
      suggestedPrice
      deadline
      signedAt
      initiatedSigningAt
      sentToClientAt
      status
      signers {
        id
        externalSignerId
        url
        signedAt
        title
        email
        firstName
        lastName
      }
      deadlineHasBeenExceeded
      commission
    }
    brokers {
      employeeId
      name
      mobilePhone
      email
      slug
      role
      title
      employeeRoles {
        source
        typeId
        name
      }
      image {
        small
      }
    }
    brokersIdWithRoles {
      employeeId
      brokerRole
      employee {
        title
        name
        email
        mobilePhone
        image {
          small
        }
      }
    }
  }
}
    `;

export const useEstateByIdQuery = <
      TData = GQLEstateByIdQuery,
      TError = unknown
    >(
      variables: GQLEstateByIdQueryVariables,
      options?: Omit<UseQueryOptions<GQLEstateByIdQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLEstateByIdQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLEstateByIdQuery, TError, TData>(
      {
    queryKey: ['estateById', variables],
    queryFn: fetchData<GQLEstateByIdQuery, GQLEstateByIdQueryVariables>(EstateByIdDocument, variables),
    ...options
  }
    )};

useEstateByIdQuery.getKey = (variables: GQLEstateByIdQueryVariables) => ['estateById', variables];

export const useInfiniteEstateByIdQuery = <
      TData = InfiniteData<GQLEstateByIdQuery>,
      TError = unknown
    >(
      variables: GQLEstateByIdQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLEstateByIdQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLEstateByIdQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLEstateByIdQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['estateById.infinite', variables],
      queryFn: (metaData) => fetchData<GQLEstateByIdQuery, GQLEstateByIdQueryVariables>(EstateByIdDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteEstateByIdQuery.getKey = (variables: GQLEstateByIdQueryVariables) => ['estateById.infinite', variables];


useEstateByIdQuery.fetcher = (variables: GQLEstateByIdQueryVariables, options?: RequestInit['headers']) => fetchData<GQLEstateByIdQuery, GQLEstateByIdQueryVariables>(EstateByIdDocument, variables, options);

export const EstateCampaignsByIdDocument = `
    query estateCampaignsById($id: String!) {
  estate(id: $id) {
    campaigns {
      packageName
      marketingPackage
      dateOrdered
      orderStartDate
      orderEndDate
      externalId
    }
  }
}
    `;

export const useEstateCampaignsByIdQuery = <
      TData = GQLEstateCampaignsByIdQuery,
      TError = unknown
    >(
      variables: GQLEstateCampaignsByIdQueryVariables,
      options?: Omit<UseQueryOptions<GQLEstateCampaignsByIdQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLEstateCampaignsByIdQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLEstateCampaignsByIdQuery, TError, TData>(
      {
    queryKey: ['estateCampaignsById', variables],
    queryFn: fetchData<GQLEstateCampaignsByIdQuery, GQLEstateCampaignsByIdQueryVariables>(EstateCampaignsByIdDocument, variables),
    ...options
  }
    )};

useEstateCampaignsByIdQuery.getKey = (variables: GQLEstateCampaignsByIdQueryVariables) => ['estateCampaignsById', variables];

export const useInfiniteEstateCampaignsByIdQuery = <
      TData = InfiniteData<GQLEstateCampaignsByIdQuery>,
      TError = unknown
    >(
      variables: GQLEstateCampaignsByIdQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLEstateCampaignsByIdQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLEstateCampaignsByIdQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLEstateCampaignsByIdQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['estateCampaignsById.infinite', variables],
      queryFn: (metaData) => fetchData<GQLEstateCampaignsByIdQuery, GQLEstateCampaignsByIdQueryVariables>(EstateCampaignsByIdDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteEstateCampaignsByIdQuery.getKey = (variables: GQLEstateCampaignsByIdQueryVariables) => ['estateCampaignsById.infinite', variables];


useEstateCampaignsByIdQuery.fetcher = (variables: GQLEstateCampaignsByIdQueryVariables, options?: RequestInit['headers']) => fetchData<GQLEstateCampaignsByIdQuery, GQLEstateCampaignsByIdQueryVariables>(EstateCampaignsByIdDocument, variables, options);

export const EstateExtrasByIdDocument = `
    query estateExtrasById($id: String!, $estateProps: EstateProps) {
  forms: estateFormsByEstateId(estateId: $id, estateProps: $estateProps) {
    type
    name
    link
    status {
      signingFinished
      isNotificationSent
    }
    relevantForEstateWithProps {
      status
      projectRelation
    }
  }
  oa: listingAgreementByEstateId(estateId: $id) {
    id
    createdAt
    status
    signicatDocumentId
    signedAt
    sentToClientAt
    initiatedSigningAt
    deadline
    brokerSigners {
      id
      externalSignerId
      url
      signedAt
      title
      email
      phone
      firstName
      lastName
    }
    sellerSigners {
      id
      externalSignerId
      url
      signedAt
      title
      email
      phone
      firstName
      lastName
    }
    accessTokens {
      id
      createdAt
    }
  }
}
    `;

export const useEstateExtrasByIdQuery = <
      TData = GQLEstateExtrasByIdQuery,
      TError = unknown
    >(
      variables: GQLEstateExtrasByIdQueryVariables,
      options?: Omit<UseQueryOptions<GQLEstateExtrasByIdQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLEstateExtrasByIdQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLEstateExtrasByIdQuery, TError, TData>(
      {
    queryKey: ['estateExtrasById', variables],
    queryFn: fetchData<GQLEstateExtrasByIdQuery, GQLEstateExtrasByIdQueryVariables>(EstateExtrasByIdDocument, variables),
    ...options
  }
    )};

useEstateExtrasByIdQuery.getKey = (variables: GQLEstateExtrasByIdQueryVariables) => ['estateExtrasById', variables];

export const useInfiniteEstateExtrasByIdQuery = <
      TData = InfiniteData<GQLEstateExtrasByIdQuery>,
      TError = unknown
    >(
      variables: GQLEstateExtrasByIdQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLEstateExtrasByIdQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLEstateExtrasByIdQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLEstateExtrasByIdQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['estateExtrasById.infinite', variables],
      queryFn: (metaData) => fetchData<GQLEstateExtrasByIdQuery, GQLEstateExtrasByIdQueryVariables>(EstateExtrasByIdDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteEstateExtrasByIdQuery.getKey = (variables: GQLEstateExtrasByIdQueryVariables) => ['estateExtrasById.infinite', variables];


useEstateExtrasByIdQuery.fetcher = (variables: GQLEstateExtrasByIdQueryVariables, options?: RequestInit['headers']) => fetchData<GQLEstateExtrasByIdQuery, GQLEstateExtrasByIdQueryVariables>(EstateExtrasByIdDocument, variables, options);

export const EstateFormsByIdDocument = `
    query estateFormsById($id: String!) {
  estate(id: $id) {
    forms {
      type
      name
      link
      status {
        signingFinished
        isNotificationSent
      }
      relevantForEstateWithProps {
        status
        projectRelation
      }
    }
  }
}
    `;

export const useEstateFormsByIdQuery = <
      TData = GQLEstateFormsByIdQuery,
      TError = unknown
    >(
      variables: GQLEstateFormsByIdQueryVariables,
      options?: Omit<UseQueryOptions<GQLEstateFormsByIdQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLEstateFormsByIdQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLEstateFormsByIdQuery, TError, TData>(
      {
    queryKey: ['estateFormsById', variables],
    queryFn: fetchData<GQLEstateFormsByIdQuery, GQLEstateFormsByIdQueryVariables>(EstateFormsByIdDocument, variables),
    ...options
  }
    )};

useEstateFormsByIdQuery.getKey = (variables: GQLEstateFormsByIdQueryVariables) => ['estateFormsById', variables];

export const useInfiniteEstateFormsByIdQuery = <
      TData = InfiniteData<GQLEstateFormsByIdQuery>,
      TError = unknown
    >(
      variables: GQLEstateFormsByIdQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLEstateFormsByIdQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLEstateFormsByIdQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLEstateFormsByIdQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['estateFormsById.infinite', variables],
      queryFn: (metaData) => fetchData<GQLEstateFormsByIdQuery, GQLEstateFormsByIdQueryVariables>(EstateFormsByIdDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteEstateFormsByIdQuery.getKey = (variables: GQLEstateFormsByIdQueryVariables) => ['estateFormsById.infinite', variables];


useEstateFormsByIdQuery.fetcher = (variables: GQLEstateFormsByIdQueryVariables, options?: RequestInit['headers']) => fetchData<GQLEstateFormsByIdQuery, GQLEstateFormsByIdQueryVariables>(EstateFormsByIdDocument, variables, options);

export const EstateInspectionByIdDocument = `
    query estateInspectionById($id: String!) {
  estate(id: $id) {
    hasInspection
    inspection {
      success
      entries {
        id
        title
        postDate {
          date
          timezone_type
          timezone
        }
        url
      }
    }
  }
}
    `;

export const useEstateInspectionByIdQuery = <
      TData = GQLEstateInspectionByIdQuery,
      TError = unknown
    >(
      variables: GQLEstateInspectionByIdQueryVariables,
      options?: Omit<UseQueryOptions<GQLEstateInspectionByIdQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLEstateInspectionByIdQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLEstateInspectionByIdQuery, TError, TData>(
      {
    queryKey: ['estateInspectionById', variables],
    queryFn: fetchData<GQLEstateInspectionByIdQuery, GQLEstateInspectionByIdQueryVariables>(EstateInspectionByIdDocument, variables),
    ...options
  }
    )};

useEstateInspectionByIdQuery.getKey = (variables: GQLEstateInspectionByIdQueryVariables) => ['estateInspectionById', variables];

export const useInfiniteEstateInspectionByIdQuery = <
      TData = InfiniteData<GQLEstateInspectionByIdQuery>,
      TError = unknown
    >(
      variables: GQLEstateInspectionByIdQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLEstateInspectionByIdQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLEstateInspectionByIdQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLEstateInspectionByIdQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['estateInspectionById.infinite', variables],
      queryFn: (metaData) => fetchData<GQLEstateInspectionByIdQuery, GQLEstateInspectionByIdQueryVariables>(EstateInspectionByIdDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteEstateInspectionByIdQuery.getKey = (variables: GQLEstateInspectionByIdQueryVariables) => ['estateInspectionById.infinite', variables];


useEstateInspectionByIdQuery.fetcher = (variables: GQLEstateInspectionByIdQueryVariables, options?: RequestInit['headers']) => fetchData<GQLEstateInspectionByIdQuery, GQLEstateInspectionByIdQueryVariables>(EstateInspectionByIdDocument, variables, options);

export const SyncEstateWithVitecDocument = `
    query syncEstateWithVitec($estateId: String!) {
  syncEstateWithVitec(estateId: $estateId)
}
    `;

export const useSyncEstateWithVitecQuery = <
      TData = GQLSyncEstateWithVitecQuery,
      TError = unknown
    >(
      variables: GQLSyncEstateWithVitecQueryVariables,
      options?: Omit<UseQueryOptions<GQLSyncEstateWithVitecQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLSyncEstateWithVitecQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLSyncEstateWithVitecQuery, TError, TData>(
      {
    queryKey: ['syncEstateWithVitec', variables],
    queryFn: fetchData<GQLSyncEstateWithVitecQuery, GQLSyncEstateWithVitecQueryVariables>(SyncEstateWithVitecDocument, variables),
    ...options
  }
    )};

useSyncEstateWithVitecQuery.getKey = (variables: GQLSyncEstateWithVitecQueryVariables) => ['syncEstateWithVitec', variables];

export const useInfiniteSyncEstateWithVitecQuery = <
      TData = InfiniteData<GQLSyncEstateWithVitecQuery>,
      TError = unknown
    >(
      variables: GQLSyncEstateWithVitecQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLSyncEstateWithVitecQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLSyncEstateWithVitecQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLSyncEstateWithVitecQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['syncEstateWithVitec.infinite', variables],
      queryFn: (metaData) => fetchData<GQLSyncEstateWithVitecQuery, GQLSyncEstateWithVitecQueryVariables>(SyncEstateWithVitecDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteSyncEstateWithVitecQuery.getKey = (variables: GQLSyncEstateWithVitecQueryVariables) => ['syncEstateWithVitec.infinite', variables];


useSyncEstateWithVitecQuery.fetcher = (variables: GQLSyncEstateWithVitecQueryVariables, options?: RequestInit['headers']) => fetchData<GQLSyncEstateWithVitecQuery, GQLSyncEstateWithVitecQueryVariables>(SyncEstateWithVitecDocument, variables, options);

export const EstateResetFormDocument = `
    mutation estateResetForm($estateId: String!, $formType: String!) {
  resetForm(estateId: $estateId, formType: $formType)
}
    `;

export const useEstateResetFormMutation = <
      TError = unknown,
      TContext = unknown
    >(options?: UseMutationOptions<GQLEstateResetFormMutation, TError, GQLEstateResetFormMutationVariables, TContext>) => {
    
    return useMutation<GQLEstateResetFormMutation, TError, GQLEstateResetFormMutationVariables, TContext>(
      {
    mutationKey: ['estateResetForm'],
    mutationFn: (variables?: GQLEstateResetFormMutationVariables) => fetchData<GQLEstateResetFormMutation, GQLEstateResetFormMutationVariables>(EstateResetFormDocument, variables)(),
    ...options
  }
    )};

useEstateResetFormMutation.getKey = () => ['estateResetForm'];


useEstateResetFormMutation.fetcher = (variables: GQLEstateResetFormMutationVariables, options?: RequestInit['headers']) => fetchData<GQLEstateResetFormMutation, GQLEstateResetFormMutationVariables>(EstateResetFormDocument, variables, options);

export const AgreementAndInspectionDocument = `
    query agreementAndInspection($estateId: String!) {
  estate(id: $estateId) {
    matrikkel {
      gnr
      bnr
      snr
      ownPart
    }
    ownershipType
    estateId
    hasCompanySeller
    inspectionDate
    estateTypeExternal
    isValuation
    ownership
    isValuation
    landIdentificationMatrix {
      gnr
      bnr
      knr
      snr
      ownPart
    }
    partOwnership {
      partName
      partNumber
      partOrgNumber
      estateHousingCooperativeStockHousingUnitNumber
      estateHousingCooperativeStockNumber
    }
    estateType
    estateTypeId
    estatePrice {
      priceSuggestion
      collectiveDebt
    }
    estatePriceModel {
      collectiveDebt
      estimatedValue
      priceSuggestion
      totalPrice
    }
    department {
      departmentId
      departmentNumber
      name
      legalName
      organisationNumber
      phone
      streetAddress
      postalCode
      city
      email
      employees {
        employeeId
        name
        email
        mobilePhone
        title
        slug
      }
    }
    address {
      streetAddress
      city
      zipCode
      municipality
    }
    activities {
      start
      end
      type
      typeName
      name
      performedById
      done
      id
      value
    }
    status
    linkToNext
    inspectionFolder {
      id
      sentAt
      publishedAt
      listingAgreementSentAt
      listingAgreementActive
      audit {
        id
        sentAt
        listingAgreementActive
      }
    }
    listingAgreement {
      id
      updatedAt
      createdAt
      signicatDocumentId
      feePercentage
      suggestedPrice
      deadline
      signedAt
      initiatedSigningAt
      sentToClientAt
      status
      canStartSigning
      offerSellerLink
      signers {
        id
        externalSignerId
        url
        signedAt
        title
        email
        firstName
        lastName
      }
      deadlineHasBeenExceeded
      commission
      interactions {
        id
        eventType
        eventTimestamp
      }
      hasStorebrandLead
    }
    mainSeller {
      contactId
      firstName
      lastName
    }
    sellers {
      firstName
      lastName
      email
      mobilePhone
      mainContact
      contactId
      contactType
      proxyId
    }
    companyContacts {
      contactId
      departmentId
      contactType
      companyName
      organisationNumber
      firstName
      lastName
      mobilePhone
      privatePhone
      workPhone
      email
      address
      postalAddress
      postalCode
      city
      deletedAt
      relationName
      roleName
      relationType
    }
    extraContacts(source: Next) {
      contactId
      departmentId
      contactType
      companyName
      organisationNumber
      firstName
      lastName
      mobilePhone
      privatePhone
      workPhone
      email
      address
      postalAddress
      postalCode
      city
      deletedAt
      relationName
      relationType
    }
    broker {
      id
      employeeId
      name
      email
    }
    brokers {
      employeeId
      name
      mobilePhone
      email
      slug
      role
      title
      employeeRoles {
        source
        typeId
        name
      }
      image {
        small
      }
    }
    brokersIdWithRoles {
      employeeId
      brokerRole
      employee {
        title
        name
        email
        mobilePhone
        image {
          small
        }
      }
    }
  }
}
    `;

export const useAgreementAndInspectionQuery = <
      TData = GQLAgreementAndInspectionQuery,
      TError = unknown
    >(
      variables: GQLAgreementAndInspectionQueryVariables,
      options?: Omit<UseQueryOptions<GQLAgreementAndInspectionQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLAgreementAndInspectionQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLAgreementAndInspectionQuery, TError, TData>(
      {
    queryKey: ['agreementAndInspection', variables],
    queryFn: fetchData<GQLAgreementAndInspectionQuery, GQLAgreementAndInspectionQueryVariables>(AgreementAndInspectionDocument, variables),
    ...options
  }
    )};

useAgreementAndInspectionQuery.getKey = (variables: GQLAgreementAndInspectionQueryVariables) => ['agreementAndInspection', variables];

export const useInfiniteAgreementAndInspectionQuery = <
      TData = InfiniteData<GQLAgreementAndInspectionQuery>,
      TError = unknown
    >(
      variables: GQLAgreementAndInspectionQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLAgreementAndInspectionQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLAgreementAndInspectionQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLAgreementAndInspectionQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['agreementAndInspection.infinite', variables],
      queryFn: (metaData) => fetchData<GQLAgreementAndInspectionQuery, GQLAgreementAndInspectionQueryVariables>(AgreementAndInspectionDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteAgreementAndInspectionQuery.getKey = (variables: GQLAgreementAndInspectionQueryVariables) => ['agreementAndInspection.infinite', variables];


useAgreementAndInspectionQuery.fetcher = (variables: GQLAgreementAndInspectionQueryVariables, options?: RequestInit['headers']) => fetchData<GQLAgreementAndInspectionQuery, GQLAgreementAndInspectionQueryVariables>(AgreementAndInspectionDocument, variables, options);

export const CombinedInteractionsDocument = `
    query combinedInteractions($estateId: String!, $includeSubPages: Boolean) {
  estate(id: $estateId) {
    estateId
    hasCompanySeller
    isValuation
    sellers {
      firstName
      lastName
      email
      mobilePhone
      mainContact
      contactId
      proxyId
      proxy {
        contactId
        firstName
      }
    }
    companyContacts {
      contactId
      firstName
      lastName
      email
      mobilePhone
    }
    extraContacts(source: Nordvik) {
      contactId
      firstName
      lastName
      email
      mobilePhone
      deletedAt
    }
    inspectionFolder {
      id
      audit {
        id
        recipientContactIds
        listingAgreementActive
        sentAt
        channels
        extraData
        templateId
        modifiedTemplate
        recipients {
          contactId
          email
          mobilePhone
          lastName
          firstName
        }
        sentBy {
          name
        }
        emailAuditId
      }
    }
    listingAgreement {
      id
      deadline
      status
      sellerSigners {
        id
        externalSignerId
        url
        signedAt
        title
        email
        firstName
        lastName
      }
      brokerSigners {
        id
        externalSignerId
        signedAt
      }
      deadlineHasBeenExceeded
    }
  }
  listingAgreementInteractionsForEstate(estateId: $estateId) {
    id
    listingAgreementsId
    sellerId
    eventType
    eventTimestamp
    extraData
    name
    seller {
      firstName
      lastName
    }
  }
  emailInteractionsForEstate(estateId: $estateId) {
    id
    eventType
    eventTimestamp
    recipientEmail
    contactId
    messageId
    openCount
    emailAuditId
    bounceType
    rejectReason
  }
  pageVisits(estateId: $estateId, includeSubPages: $includeSubPages) {
    id
    contactId
    contactName
    source
    employeeId
    totalTimeSpent
    lastHeartbeat
    startTime
    endTime
    pageId
    browser
    userAgent {
      os {
        name
      }
      browser {
        name
      }
    }
  }
}
    `;

export const useCombinedInteractionsQuery = <
      TData = GQLCombinedInteractionsQuery,
      TError = unknown
    >(
      variables: GQLCombinedInteractionsQueryVariables,
      options?: Omit<UseQueryOptions<GQLCombinedInteractionsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLCombinedInteractionsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLCombinedInteractionsQuery, TError, TData>(
      {
    queryKey: ['combinedInteractions', variables],
    queryFn: fetchData<GQLCombinedInteractionsQuery, GQLCombinedInteractionsQueryVariables>(CombinedInteractionsDocument, variables),
    ...options
  }
    )};

useCombinedInteractionsQuery.getKey = (variables: GQLCombinedInteractionsQueryVariables) => ['combinedInteractions', variables];

export const useInfiniteCombinedInteractionsQuery = <
      TData = InfiniteData<GQLCombinedInteractionsQuery>,
      TError = unknown
    >(
      variables: GQLCombinedInteractionsQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLCombinedInteractionsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLCombinedInteractionsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLCombinedInteractionsQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['combinedInteractions.infinite', variables],
      queryFn: (metaData) => fetchData<GQLCombinedInteractionsQuery, GQLCombinedInteractionsQueryVariables>(CombinedInteractionsDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteCombinedInteractionsQuery.getKey = (variables: GQLCombinedInteractionsQueryVariables) => ['combinedInteractions.infinite', variables];


useCombinedInteractionsQuery.fetcher = (variables: GQLCombinedInteractionsQueryVariables, options?: RequestInit['headers']) => fetchData<GQLCombinedInteractionsQuery, GQLCombinedInteractionsQueryVariables>(CombinedInteractionsDocument, variables, options);

export const ListingAgreementInteractionsDocument = `
    query listingAgreementInteractions($estateId: String!) {
  estate(id: $estateId) {
    estateId
    hasCompanySeller
    sellers {
      firstName
      lastName
      email
      mobilePhone
      mainContact
      contactId
      proxyId
      proxy {
        contactId
        firstName
      }
    }
    companyContacts {
      contactId
      firstName
      lastName
      email
      mobilePhone
    }
    extraContacts(source: Nordvik) {
      firstName
      lastName
      email
      mobilePhone
      contactId
      deletedAt
      relationName
    }
    listingAgreement {
      id
      deadline
      status
      sellerSigners {
        id
        externalSignerId
        url
        signedAt
        title
        email
        firstName
        lastName
      }
      brokerSigners {
        id
        externalSignerId
        signedAt
      }
      deadlineHasBeenExceeded
    }
  }
  listingAgreementInteractionsForEstate(estateId: $estateId) {
    id
    listingAgreementsId
    sellerId
    eventType
    eventTimestamp
    extraData
    name
    seller {
      firstName
      lastName
    }
  }
  pageVisits(estateId: $estateId, includeSubPages: true) {
    id
    contactId
    employeeId
    totalTimeSpent
    lastHeartbeat
    startTime
    endTime
    pageId
    browser
    location
    contactName
  }
}
    `;

export const useListingAgreementInteractionsQuery = <
      TData = GQLListingAgreementInteractionsQuery,
      TError = unknown
    >(
      variables: GQLListingAgreementInteractionsQueryVariables,
      options?: Omit<UseQueryOptions<GQLListingAgreementInteractionsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLListingAgreementInteractionsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLListingAgreementInteractionsQuery, TError, TData>(
      {
    queryKey: ['listingAgreementInteractions', variables],
    queryFn: fetchData<GQLListingAgreementInteractionsQuery, GQLListingAgreementInteractionsQueryVariables>(ListingAgreementInteractionsDocument, variables),
    ...options
  }
    )};

useListingAgreementInteractionsQuery.getKey = (variables: GQLListingAgreementInteractionsQueryVariables) => ['listingAgreementInteractions', variables];

export const useInfiniteListingAgreementInteractionsQuery = <
      TData = InfiniteData<GQLListingAgreementInteractionsQuery>,
      TError = unknown
    >(
      variables: GQLListingAgreementInteractionsQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLListingAgreementInteractionsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLListingAgreementInteractionsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLListingAgreementInteractionsQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['listingAgreementInteractions.infinite', variables],
      queryFn: (metaData) => fetchData<GQLListingAgreementInteractionsQuery, GQLListingAgreementInteractionsQueryVariables>(ListingAgreementInteractionsDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteListingAgreementInteractionsQuery.getKey = (variables: GQLListingAgreementInteractionsQueryVariables) => ['listingAgreementInteractions.infinite', variables];


useListingAgreementInteractionsQuery.fetcher = (variables: GQLListingAgreementInteractionsQueryVariables, options?: RequestInit['headers']) => fetchData<GQLListingAgreementInteractionsQuery, GQLListingAgreementInteractionsQueryVariables>(ListingAgreementInteractionsDocument, variables, options);

export const EditInspectionFolderDocument = `
    query EditInspectionFolder($estateId: String!) {
  inspectionFolder(estateId: $estateId) {
    id
    listingAgreementActive
    excludedPartners {
      id
    }
    excludedEmployees
    relevantLinks
    sentAt
    listingAgreementSentAt
  }
  estate(id: $estateId) {
    assistantBroker {
      employeeId
      name
      title
      mobilePhone
      email
      image {
        small
      }
    }
    mainBroker {
      team {
        id
        name
        category
        createdAt
        profilePicture
      }
    }
  }
}
    `;

export const useEditInspectionFolderQuery = <
      TData = GQLEditInspectionFolderQuery,
      TError = unknown
    >(
      variables: GQLEditInspectionFolderQueryVariables,
      options?: Omit<UseQueryOptions<GQLEditInspectionFolderQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLEditInspectionFolderQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLEditInspectionFolderQuery, TError, TData>(
      {
    queryKey: ['EditInspectionFolder', variables],
    queryFn: fetchData<GQLEditInspectionFolderQuery, GQLEditInspectionFolderQueryVariables>(EditInspectionFolderDocument, variables),
    ...options
  }
    )};

useEditInspectionFolderQuery.getKey = (variables: GQLEditInspectionFolderQueryVariables) => ['EditInspectionFolder', variables];

export const useInfiniteEditInspectionFolderQuery = <
      TData = InfiniteData<GQLEditInspectionFolderQuery>,
      TError = unknown
    >(
      variables: GQLEditInspectionFolderQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLEditInspectionFolderQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLEditInspectionFolderQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLEditInspectionFolderQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['EditInspectionFolder.infinite', variables],
      queryFn: (metaData) => fetchData<GQLEditInspectionFolderQuery, GQLEditInspectionFolderQueryVariables>(EditInspectionFolderDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteEditInspectionFolderQuery.getKey = (variables: GQLEditInspectionFolderQueryVariables) => ['EditInspectionFolder.infinite', variables];


useEditInspectionFolderQuery.fetcher = (variables: GQLEditInspectionFolderQueryVariables, options?: RequestInit['headers']) => fetchData<GQLEditInspectionFolderQuery, GQLEditInspectionFolderQueryVariables>(EditInspectionFolderDocument, variables, options);

export const OaSignedAtDocument = `
    query OaSignedAt($estateId: String!) {
  listingAgreementByEstateId(estateId: $estateId) {
    signedAt
  }
}
    `;

export const useOaSignedAtQuery = <
      TData = GQLOaSignedAtQuery,
      TError = unknown
    >(
      variables: GQLOaSignedAtQueryVariables,
      options?: Omit<UseQueryOptions<GQLOaSignedAtQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLOaSignedAtQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLOaSignedAtQuery, TError, TData>(
      {
    queryKey: ['OaSignedAt', variables],
    queryFn: fetchData<GQLOaSignedAtQuery, GQLOaSignedAtQueryVariables>(OaSignedAtDocument, variables),
    ...options
  }
    )};

useOaSignedAtQuery.getKey = (variables: GQLOaSignedAtQueryVariables) => ['OaSignedAt', variables];

export const useInfiniteOaSignedAtQuery = <
      TData = InfiniteData<GQLOaSignedAtQuery>,
      TError = unknown
    >(
      variables: GQLOaSignedAtQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLOaSignedAtQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLOaSignedAtQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLOaSignedAtQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['OaSignedAt.infinite', variables],
      queryFn: (metaData) => fetchData<GQLOaSignedAtQuery, GQLOaSignedAtQueryVariables>(OaSignedAtDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteOaSignedAtQuery.getKey = (variables: GQLOaSignedAtQueryVariables) => ['OaSignedAt.infinite', variables];


useOaSignedAtQuery.fetcher = (variables: GQLOaSignedAtQueryVariables, options?: RequestInit['headers']) => fetchData<GQLOaSignedAtQuery, GQLOaSignedAtQueryVariables>(OaSignedAtDocument, variables, options);

export const PageVisitsDocument = `
    query PageVisits($estateId: String!, $includeSubPages: Boolean!) {
  pageVisits(estateId: $estateId, includeSubPages: $includeSubPages) {
    id
    contactId
    employeeId
    totalTimeSpent
    lastHeartbeat
    startTime
    endTime
    pageId
    browser
    location
    contactName
  }
}
    `;

export const usePageVisitsQuery = <
      TData = GQLPageVisitsQuery,
      TError = unknown
    >(
      variables: GQLPageVisitsQueryVariables,
      options?: Omit<UseQueryOptions<GQLPageVisitsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLPageVisitsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLPageVisitsQuery, TError, TData>(
      {
    queryKey: ['PageVisits', variables],
    queryFn: fetchData<GQLPageVisitsQuery, GQLPageVisitsQueryVariables>(PageVisitsDocument, variables),
    ...options
  }
    )};

usePageVisitsQuery.getKey = (variables: GQLPageVisitsQueryVariables) => ['PageVisits', variables];

export const useInfinitePageVisitsQuery = <
      TData = InfiniteData<GQLPageVisitsQuery>,
      TError = unknown
    >(
      variables: GQLPageVisitsQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLPageVisitsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLPageVisitsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLPageVisitsQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['PageVisits.infinite', variables],
      queryFn: (metaData) => fetchData<GQLPageVisitsQuery, GQLPageVisitsQueryVariables>(PageVisitsDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfinitePageVisitsQuery.getKey = (variables: GQLPageVisitsQueryVariables) => ['PageVisits.infinite', variables];


usePageVisitsQuery.fetcher = (variables: GQLPageVisitsQueryVariables, options?: RequestInit['headers']) => fetchData<GQLPageVisitsQuery, GQLPageVisitsQueryVariables>(PageVisitsDocument, variables, options);

export const EstateInteractionsAndPageVisitsDocument = `
    query EstateInteractionsAndPageVisits($estateId: String!, $includeSubPages: Boolean) {
  pageVisits(estateId: $estateId, includeSubPages: $includeSubPages) {
    id
    contactId
    employeeId
    totalTimeSpent
    lastHeartbeat
    startTime
    endTime
    pageId
    browser
    location
    contactName
  }
  listingAgreementInteractionsForEstate(estateId: $estateId) {
    id
    listingAgreementsId
    sellerId
    eventType
    eventTimestamp
    extraData
    name
    seller {
      firstName
      lastName
    }
  }
  listingAgreementByEstateId(estateId: $estateId) {
    signedAt
    sentToClientAt
    status
  }
}
    `;

export const useEstateInteractionsAndPageVisitsQuery = <
      TData = GQLEstateInteractionsAndPageVisitsQuery,
      TError = unknown
    >(
      variables: GQLEstateInteractionsAndPageVisitsQueryVariables,
      options?: Omit<UseQueryOptions<GQLEstateInteractionsAndPageVisitsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLEstateInteractionsAndPageVisitsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLEstateInteractionsAndPageVisitsQuery, TError, TData>(
      {
    queryKey: ['EstateInteractionsAndPageVisits', variables],
    queryFn: fetchData<GQLEstateInteractionsAndPageVisitsQuery, GQLEstateInteractionsAndPageVisitsQueryVariables>(EstateInteractionsAndPageVisitsDocument, variables),
    ...options
  }
    )};

useEstateInteractionsAndPageVisitsQuery.getKey = (variables: GQLEstateInteractionsAndPageVisitsQueryVariables) => ['EstateInteractionsAndPageVisits', variables];

export const useInfiniteEstateInteractionsAndPageVisitsQuery = <
      TData = InfiniteData<GQLEstateInteractionsAndPageVisitsQuery>,
      TError = unknown
    >(
      variables: GQLEstateInteractionsAndPageVisitsQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLEstateInteractionsAndPageVisitsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLEstateInteractionsAndPageVisitsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLEstateInteractionsAndPageVisitsQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['EstateInteractionsAndPageVisits.infinite', variables],
      queryFn: (metaData) => fetchData<GQLEstateInteractionsAndPageVisitsQuery, GQLEstateInteractionsAndPageVisitsQueryVariables>(EstateInteractionsAndPageVisitsDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteEstateInteractionsAndPageVisitsQuery.getKey = (variables: GQLEstateInteractionsAndPageVisitsQueryVariables) => ['EstateInteractionsAndPageVisits.infinite', variables];


useEstateInteractionsAndPageVisitsQuery.fetcher = (variables: GQLEstateInteractionsAndPageVisitsQueryVariables, options?: RequestInit['headers']) => fetchData<GQLEstateInteractionsAndPageVisitsQuery, GQLEstateInteractionsAndPageVisitsQueryVariables>(EstateInteractionsAndPageVisitsDocument, variables, options);

export const ActivitySummaryDocument = `
    query activitySummary($estateId: String!) {
  activitySummary(estateId: $estateId) {
    visitsCount
    visitorCount
    totalTimeSpent
    signedCount
    signersCount
    recipientsCount
  }
}
    `;

export const useActivitySummaryQuery = <
      TData = GQLActivitySummaryQuery,
      TError = unknown
    >(
      variables: GQLActivitySummaryQueryVariables,
      options?: Omit<UseQueryOptions<GQLActivitySummaryQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLActivitySummaryQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLActivitySummaryQuery, TError, TData>(
      {
    queryKey: ['activitySummary', variables],
    queryFn: fetchData<GQLActivitySummaryQuery, GQLActivitySummaryQueryVariables>(ActivitySummaryDocument, variables),
    ...options
  }
    )};

useActivitySummaryQuery.getKey = (variables: GQLActivitySummaryQueryVariables) => ['activitySummary', variables];

export const useInfiniteActivitySummaryQuery = <
      TData = InfiniteData<GQLActivitySummaryQuery>,
      TError = unknown
    >(
      variables: GQLActivitySummaryQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLActivitySummaryQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLActivitySummaryQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLActivitySummaryQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['activitySummary.infinite', variables],
      queryFn: (metaData) => fetchData<GQLActivitySummaryQuery, GQLActivitySummaryQueryVariables>(ActivitySummaryDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteActivitySummaryQuery.getKey = (variables: GQLActivitySummaryQueryVariables) => ['activitySummary.infinite', variables];


useActivitySummaryQuery.fetcher = (variables: GQLActivitySummaryQueryVariables, options?: RequestInit['headers']) => fetchData<GQLActivitySummaryQuery, GQLActivitySummaryQueryVariables>(ActivitySummaryDocument, variables, options);

export const DepartmentEmployeesDocument = `
    query DepartmentEmployees($departmentId: Int!) {
  department(departmentId: $departmentId) {
    departmentId
    employees {
      employeeId
      name
    }
  }
}
    `;

export const useDepartmentEmployeesQuery = <
      TData = GQLDepartmentEmployeesQuery,
      TError = unknown
    >(
      variables: GQLDepartmentEmployeesQueryVariables,
      options?: Omit<UseQueryOptions<GQLDepartmentEmployeesQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLDepartmentEmployeesQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLDepartmentEmployeesQuery, TError, TData>(
      {
    queryKey: ['DepartmentEmployees', variables],
    queryFn: fetchData<GQLDepartmentEmployeesQuery, GQLDepartmentEmployeesQueryVariables>(DepartmentEmployeesDocument, variables),
    ...options
  }
    )};

useDepartmentEmployeesQuery.getKey = (variables: GQLDepartmentEmployeesQueryVariables) => ['DepartmentEmployees', variables];

export const useInfiniteDepartmentEmployeesQuery = <
      TData = InfiniteData<GQLDepartmentEmployeesQuery>,
      TError = unknown
    >(
      variables: GQLDepartmentEmployeesQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLDepartmentEmployeesQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLDepartmentEmployeesQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLDepartmentEmployeesQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['DepartmentEmployees.infinite', variables],
      queryFn: (metaData) => fetchData<GQLDepartmentEmployeesQuery, GQLDepartmentEmployeesQueryVariables>(DepartmentEmployeesDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteDepartmentEmployeesQuery.getKey = (variables: GQLDepartmentEmployeesQueryVariables) => ['DepartmentEmployees.infinite', variables];


useDepartmentEmployeesQuery.fetcher = (variables: GQLDepartmentEmployeesQueryVariables, options?: RequestInit['headers']) => fetchData<GQLDepartmentEmployeesQuery, GQLDepartmentEmployeesQueryVariables>(DepartmentEmployeesDocument, variables, options);

export const EstateDrawerDetailsDocument = `
    query estateDrawerDetails($id: String!) {
  estate(id: $id) {
    status
    estateId
    address {
      streetAddress
      city
    }
    assignmentNumber
    noOfBedRooms
    soldDate
    commissionAcceptedDate
    linkToNext
    marketingStart {
      date
      source
    }
    estatePrice {
      totalPrice
      priceSuggestion
      soldPrice
    }
    sumArea {
      braI
      pRom
    }
    links {
      linkType
      url
      text
    }
    mainImage {
      large
    }
    placeholderImage
    sellers {
      firstName
      lastName
      email
      mobilePhone
      mainContact
      contactId
    }
    showings {
      start
      end
      showingId
    }
    checklist {
      firstTag
      value
    }
    linkToNext
    hjemUrl
    brokers {
      role
      image {
        small
      }
      name
      employeeId
      employeeRoles {
        typeId
      }
    }
    ads {
      source
      link
    }
  }
  events: inspectionEvents(estateId: $id) {
    id
    title
    start
    end
    type
    description
  }
}
    `;

export const useEstateDrawerDetailsQuery = <
      TData = GQLEstateDrawerDetailsQuery,
      TError = unknown
    >(
      variables: GQLEstateDrawerDetailsQueryVariables,
      options?: Omit<UseQueryOptions<GQLEstateDrawerDetailsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLEstateDrawerDetailsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLEstateDrawerDetailsQuery, TError, TData>(
      {
    queryKey: ['estateDrawerDetails', variables],
    queryFn: fetchData<GQLEstateDrawerDetailsQuery, GQLEstateDrawerDetailsQueryVariables>(EstateDrawerDetailsDocument, variables),
    ...options
  }
    )};

useEstateDrawerDetailsQuery.getKey = (variables: GQLEstateDrawerDetailsQueryVariables) => ['estateDrawerDetails', variables];

export const useInfiniteEstateDrawerDetailsQuery = <
      TData = InfiniteData<GQLEstateDrawerDetailsQuery>,
      TError = unknown
    >(
      variables: GQLEstateDrawerDetailsQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLEstateDrawerDetailsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLEstateDrawerDetailsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLEstateDrawerDetailsQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['estateDrawerDetails.infinite', variables],
      queryFn: (metaData) => fetchData<GQLEstateDrawerDetailsQuery, GQLEstateDrawerDetailsQueryVariables>(EstateDrawerDetailsDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteEstateDrawerDetailsQuery.getKey = (variables: GQLEstateDrawerDetailsQueryVariables) => ['estateDrawerDetails.infinite', variables];


useEstateDrawerDetailsQuery.fetcher = (variables: GQLEstateDrawerDetailsQueryVariables, options?: RequestInit['headers']) => fetchData<GQLEstateDrawerDetailsQuery, GQLEstateDrawerDetailsQueryVariables>(EstateDrawerDetailsDocument, variables, options);

export const UpsertEstatePublishDateDocument = `
    mutation upsertEstatePublishDate($estateId: String!, $publishDate: DateTime) {
  upsertEstatePublishDate(estateId: $estateId, publishDate: $publishDate)
}
    `;

export const useUpsertEstatePublishDateMutation = <
      TError = unknown,
      TContext = unknown
    >(options?: UseMutationOptions<GQLUpsertEstatePublishDateMutation, TError, GQLUpsertEstatePublishDateMutationVariables, TContext>) => {
    
    return useMutation<GQLUpsertEstatePublishDateMutation, TError, GQLUpsertEstatePublishDateMutationVariables, TContext>(
      {
    mutationKey: ['upsertEstatePublishDate'],
    mutationFn: (variables?: GQLUpsertEstatePublishDateMutationVariables) => fetchData<GQLUpsertEstatePublishDateMutation, GQLUpsertEstatePublishDateMutationVariables>(UpsertEstatePublishDateDocument, variables)(),
    ...options
  }
    )};

useUpsertEstatePublishDateMutation.getKey = () => ['upsertEstatePublishDate'];


useUpsertEstatePublishDateMutation.fetcher = (variables: GQLUpsertEstatePublishDateMutationVariables, options?: RequestInit['headers']) => fetchData<GQLUpsertEstatePublishDateMutation, GQLUpsertEstatePublishDateMutationVariables>(UpsertEstatePublishDateDocument, variables, options);

export const NewAssignmentsOverviewDocument = `
    query NewAssignmentsOverview($brokerId: String!, $departmentId: Int!, $limit: Int!, $offset: Int!, $tabs: [EstateTabFilter!]!, $archived: Boolean = false, $search: String, $officeView: Boolean = false, $brokerIds: [String!], $sortBy: SortEstateBy) {
  broker: estatesForBrokerById(
    brokerId: $brokerId
    limit: $limit
    offset: $offset
    tabs: $tabs
    assignmentTypeGroup: [0, 1, 2, 4, 5, 6, 7, 8, 9, 10]
    archived: $archived
    search: $search
    sortBy: $sortBy
  ) @skip(if: $officeView) {
    pagination {
      total
      count
      offset
      limit
    }
    items {
      ...EstatesOverviewItem
    }
  }
  office: estatesForDepartment(
    departmentId: $departmentId
    limit: $limit
    offset: $offset
    tabs: $tabs
    assignmentTypeGroup: [0, 1, 2, 4, 5, 6, 7, 8, 9, 10]
    archived: $archived
    search: $search
    brokerIds: $brokerIds
  ) @include(if: $officeView) {
    pagination {
      total
      count
      offset
      limit
    }
    items {
      ...EstatesOverviewItem
    }
  }
}
    ${EstatesOverviewItemFragmentDoc}`;

export const useNewAssignmentsOverviewQuery = <
      TData = GQLNewAssignmentsOverviewQuery,
      TError = unknown
    >(
      variables: GQLNewAssignmentsOverviewQueryVariables,
      options?: Omit<UseQueryOptions<GQLNewAssignmentsOverviewQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLNewAssignmentsOverviewQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLNewAssignmentsOverviewQuery, TError, TData>(
      {
    queryKey: ['NewAssignmentsOverview', variables],
    queryFn: fetchData<GQLNewAssignmentsOverviewQuery, GQLNewAssignmentsOverviewQueryVariables>(NewAssignmentsOverviewDocument, variables),
    ...options
  }
    )};

useNewAssignmentsOverviewQuery.getKey = (variables: GQLNewAssignmentsOverviewQueryVariables) => ['NewAssignmentsOverview', variables];

export const useInfiniteNewAssignmentsOverviewQuery = <
      TData = InfiniteData<GQLNewAssignmentsOverviewQuery>,
      TError = unknown
    >(
      variables: GQLNewAssignmentsOverviewQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLNewAssignmentsOverviewQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLNewAssignmentsOverviewQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLNewAssignmentsOverviewQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['NewAssignmentsOverview.infinite', variables],
      queryFn: (metaData) => fetchData<GQLNewAssignmentsOverviewQuery, GQLNewAssignmentsOverviewQueryVariables>(NewAssignmentsOverviewDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteNewAssignmentsOverviewQuery.getKey = (variables: GQLNewAssignmentsOverviewQueryVariables) => ['NewAssignmentsOverview.infinite', variables];


useNewAssignmentsOverviewQuery.fetcher = (variables: GQLNewAssignmentsOverviewQueryVariables, options?: RequestInit['headers']) => fetchData<GQLNewAssignmentsOverviewQuery, GQLNewAssignmentsOverviewQueryVariables>(NewAssignmentsOverviewDocument, variables, options);

export const EstatesForBrokerByIdDocument = `
    query estatesForBrokerById($brokerId: String, $email: String, $tabs: [EstateTabFilter!]!, $limit: Int, $offset: Int, $archived: Boolean, $search: String, $disableCache: Boolean) {
  estatesForBrokerById(
    brokerId: $brokerId
    tabs: $tabs
    limit: $limit
    offset: $offset
    archived: $archived
    email: $email
    search: $search
    disableCache: $disableCache
    assignmentTypeGroup: [0, 1, 2, 4, 5, 6, 7, 8, 9, 10]
  ) {
    pagination {
      offset
      limit
      count
      total
    }
    items {
      id
      estateId
      type
      assignmentNumber
      finn {
        finnCode
        finnExpireDate
        finnPublishDate
      }
      address {
        streetAddress
      }
      isWithdrawn
      status
      soldDate
      expireDate
      upcomingEvents {
        start
        end
        type
        typeName
        name
        done
        id
      }
      estatePrice {
        totalPrice
        priceSuggestion
        soldPrice
      }
      mainImage {
        medium
      }
      sellers {
        firstName
        lastName
        email
        mobilePhone
        mainContact
        contactId
      }
      mainSeller {
        contactId
        firstName
        lastName
        email
        mobilePhone
        mainContact
      }
      commissionAcceptedDate
      inspectionFolder {
        id
        sentAt
        createdAt
      }
      listingAgreement {
        id
        createdAt
        status
        signicatDocumentId
        signedAt
        sentToClientAt
        initiatedSigningAt
        deadline
        brokerSigners {
          id
          externalSignerId
          url
          signedAt
          title
          email
          phone
          firstName
          lastName
        }
        sellerSigners {
          id
          externalSignerId
          url
          signedAt
          title
          email
          phone
          firstName
          lastName
        }
        accessTokens {
          id
          createdAt
        }
      }
    }
  }
}
    `;

export const useEstatesForBrokerByIdQuery = <
      TData = GQLEstatesForBrokerByIdQuery,
      TError = unknown
    >(
      variables: GQLEstatesForBrokerByIdQueryVariables,
      options?: Omit<UseQueryOptions<GQLEstatesForBrokerByIdQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLEstatesForBrokerByIdQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLEstatesForBrokerByIdQuery, TError, TData>(
      {
    queryKey: ['estatesForBrokerById', variables],
    queryFn: fetchData<GQLEstatesForBrokerByIdQuery, GQLEstatesForBrokerByIdQueryVariables>(EstatesForBrokerByIdDocument, variables),
    ...options
  }
    )};

useEstatesForBrokerByIdQuery.getKey = (variables: GQLEstatesForBrokerByIdQueryVariables) => ['estatesForBrokerById', variables];

export const useInfiniteEstatesForBrokerByIdQuery = <
      TData = InfiniteData<GQLEstatesForBrokerByIdQuery>,
      TError = unknown
    >(
      variables: GQLEstatesForBrokerByIdQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLEstatesForBrokerByIdQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLEstatesForBrokerByIdQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLEstatesForBrokerByIdQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['estatesForBrokerById.infinite', variables],
      queryFn: (metaData) => fetchData<GQLEstatesForBrokerByIdQuery, GQLEstatesForBrokerByIdQueryVariables>(EstatesForBrokerByIdDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteEstatesForBrokerByIdQuery.getKey = (variables: GQLEstatesForBrokerByIdQueryVariables) => ['estatesForBrokerById.infinite', variables];


useEstatesForBrokerByIdQuery.fetcher = (variables: GQLEstatesForBrokerByIdQueryVariables, options?: RequestInit['headers']) => fetchData<GQLEstatesForBrokerByIdQuery, GQLEstatesForBrokerByIdQueryVariables>(EstatesForBrokerByIdDocument, variables, options);

export const EstatesOverviewForBrokerByIdDocument = `
    query estatesOverviewForBrokerById($brokerId: String, $email: String, $tabs: [EstateTabFilter!]!, $limit: Int, $offset: Int, $archived: Boolean, $search: String, $disableCache: Boolean, $includeEtakstPublished: Boolean = false) {
  estatesForBrokerById(
    brokerId: $brokerId
    tabs: $tabs
    limit: $limit
    offset: $offset
    archived: $archived
    email: $email
    search: $search
    disableCache: $disableCache
  ) {
    pagination {
      offset
      limit
      count
      total
    }
    items {
      id
      estateId
      type
      assignmentNumber
      isValuation
      finn {
        finnCode
        finnExpireDate
        finnPublishDate
      }
      address {
        streetAddress
      }
      isWithdrawn
      status
      soldDate
      expireDate
      inspectionFolder {
        notes
      }
      upcomingEvents {
        start
        end
        type
        typeName
        name
        done
        id
      }
      estatePrice {
        totalPrice
        priceSuggestion
        soldPrice
      }
      mainImage {
        medium
      }
      sellers {
        firstName
        lastName
        email
        mobilePhone
        mainContact
        contactId
      }
      mainSeller {
        contactId
        firstName
        lastName
        email
        mobilePhone
        mainContact
      }
      commissionAcceptedDate
      isEtakstPublished @include(if: $includeEtakstPublished)
    }
  }
}
    `;

export const useEstatesOverviewForBrokerByIdQuery = <
      TData = GQLEstatesOverviewForBrokerByIdQuery,
      TError = unknown
    >(
      variables: GQLEstatesOverviewForBrokerByIdQueryVariables,
      options?: Omit<UseQueryOptions<GQLEstatesOverviewForBrokerByIdQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLEstatesOverviewForBrokerByIdQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLEstatesOverviewForBrokerByIdQuery, TError, TData>(
      {
    queryKey: ['estatesOverviewForBrokerById', variables],
    queryFn: fetchData<GQLEstatesOverviewForBrokerByIdQuery, GQLEstatesOverviewForBrokerByIdQueryVariables>(EstatesOverviewForBrokerByIdDocument, variables),
    ...options
  }
    )};

useEstatesOverviewForBrokerByIdQuery.getKey = (variables: GQLEstatesOverviewForBrokerByIdQueryVariables) => ['estatesOverviewForBrokerById', variables];

export const useInfiniteEstatesOverviewForBrokerByIdQuery = <
      TData = InfiniteData<GQLEstatesOverviewForBrokerByIdQuery>,
      TError = unknown
    >(
      variables: GQLEstatesOverviewForBrokerByIdQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLEstatesOverviewForBrokerByIdQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLEstatesOverviewForBrokerByIdQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLEstatesOverviewForBrokerByIdQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['estatesOverviewForBrokerById.infinite', variables],
      queryFn: (metaData) => fetchData<GQLEstatesOverviewForBrokerByIdQuery, GQLEstatesOverviewForBrokerByIdQueryVariables>(EstatesOverviewForBrokerByIdDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteEstatesOverviewForBrokerByIdQuery.getKey = (variables: GQLEstatesOverviewForBrokerByIdQueryVariables) => ['estatesOverviewForBrokerById.infinite', variables];


useEstatesOverviewForBrokerByIdQuery.fetcher = (variables: GQLEstatesOverviewForBrokerByIdQueryVariables, options?: RequestInit['headers']) => fetchData<GQLEstatesOverviewForBrokerByIdQuery, GQLEstatesOverviewForBrokerByIdQueryVariables>(EstatesOverviewForBrokerByIdDocument, variables, options);

export const EstatesForBrokerIdCountDocument = `
    query estatesForBrokerIdCount($brokerId: String, $tabs: [EstateTabFilter!]!, $email: String) {
  estatesForBrokerIdCount(brokerId: $brokerId, tabs: $tabs, email: $email) {
    tab
    count
  }
}
    `;

export const useEstatesForBrokerIdCountQuery = <
      TData = GQLEstatesForBrokerIdCountQuery,
      TError = unknown
    >(
      variables: GQLEstatesForBrokerIdCountQueryVariables,
      options?: Omit<UseQueryOptions<GQLEstatesForBrokerIdCountQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLEstatesForBrokerIdCountQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLEstatesForBrokerIdCountQuery, TError, TData>(
      {
    queryKey: ['estatesForBrokerIdCount', variables],
    queryFn: fetchData<GQLEstatesForBrokerIdCountQuery, GQLEstatesForBrokerIdCountQueryVariables>(EstatesForBrokerIdCountDocument, variables),
    ...options
  }
    )};

useEstatesForBrokerIdCountQuery.getKey = (variables: GQLEstatesForBrokerIdCountQueryVariables) => ['estatesForBrokerIdCount', variables];

export const useInfiniteEstatesForBrokerIdCountQuery = <
      TData = InfiniteData<GQLEstatesForBrokerIdCountQuery>,
      TError = unknown
    >(
      variables: GQLEstatesForBrokerIdCountQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLEstatesForBrokerIdCountQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLEstatesForBrokerIdCountQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLEstatesForBrokerIdCountQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['estatesForBrokerIdCount.infinite', variables],
      queryFn: (metaData) => fetchData<GQLEstatesForBrokerIdCountQuery, GQLEstatesForBrokerIdCountQueryVariables>(EstatesForBrokerIdCountDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteEstatesForBrokerIdCountQuery.getKey = (variables: GQLEstatesForBrokerIdCountQueryVariables) => ['estatesForBrokerIdCount.infinite', variables];


useEstatesForBrokerIdCountQuery.fetcher = (variables: GQLEstatesForBrokerIdCountQueryVariables, options?: RequestInit['headers']) => fetchData<GQLEstatesForBrokerIdCountQuery, GQLEstatesForBrokerIdCountQueryVariables>(EstatesForBrokerIdCountDocument, variables, options);

export const EstateListingAgreementDocument = `
    query estateListingAgreement($estateId: String!) {
  listingAgreementByEstateId(estateId: $estateId) {
    id
    createdAt
    status
    signicatDocumentId
    signedAt
    sentToClientAt
    initiatedSigningAt
    deadline
    brokerSigners {
      id
      externalSignerId
      url
      signedAt
      title
      email
      phone
      firstName
      lastName
    }
    sellerSigners {
      id
      externalSignerId
      url
      signedAt
      title
      email
      phone
      firstName
      lastName
    }
    accessTokens {
      id
      createdAt
    }
  }
}
    `;

export const useEstateListingAgreementQuery = <
      TData = GQLEstateListingAgreementQuery,
      TError = unknown
    >(
      variables: GQLEstateListingAgreementQueryVariables,
      options?: Omit<UseQueryOptions<GQLEstateListingAgreementQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLEstateListingAgreementQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLEstateListingAgreementQuery, TError, TData>(
      {
    queryKey: ['estateListingAgreement', variables],
    queryFn: fetchData<GQLEstateListingAgreementQuery, GQLEstateListingAgreementQueryVariables>(EstateListingAgreementDocument, variables),
    ...options
  }
    )};

useEstateListingAgreementQuery.getKey = (variables: GQLEstateListingAgreementQueryVariables) => ['estateListingAgreement', variables];

export const useInfiniteEstateListingAgreementQuery = <
      TData = InfiniteData<GQLEstateListingAgreementQuery>,
      TError = unknown
    >(
      variables: GQLEstateListingAgreementQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLEstateListingAgreementQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLEstateListingAgreementQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLEstateListingAgreementQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['estateListingAgreement.infinite', variables],
      queryFn: (metaData) => fetchData<GQLEstateListingAgreementQuery, GQLEstateListingAgreementQueryVariables>(EstateListingAgreementDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteEstateListingAgreementQuery.getKey = (variables: GQLEstateListingAgreementQueryVariables) => ['estateListingAgreement.infinite', variables];


useEstateListingAgreementQuery.fetcher = (variables: GQLEstateListingAgreementQueryVariables, options?: RequestInit['headers']) => fetchData<GQLEstateListingAgreementQuery, GQLEstateListingAgreementQueryVariables>(EstateListingAgreementDocument, variables, options);

export const ProfilePreviewByEmployeeIdDocument = `
    query profilePreviewByEmployeeId($employeeId: String!) {
  brokerByEmployeeId(employeeId: $employeeId) {
    ...BrokerPresentation
  }
}
    ${BrokerPresentationFragmentDoc}`;

export const useProfilePreviewByEmployeeIdQuery = <
      TData = GQLProfilePreviewByEmployeeIdQuery,
      TError = unknown
    >(
      variables: GQLProfilePreviewByEmployeeIdQueryVariables,
      options?: Omit<UseQueryOptions<GQLProfilePreviewByEmployeeIdQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLProfilePreviewByEmployeeIdQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLProfilePreviewByEmployeeIdQuery, TError, TData>(
      {
    queryKey: ['profilePreviewByEmployeeId', variables],
    queryFn: fetchData<GQLProfilePreviewByEmployeeIdQuery, GQLProfilePreviewByEmployeeIdQueryVariables>(ProfilePreviewByEmployeeIdDocument, variables),
    ...options
  }
    )};

useProfilePreviewByEmployeeIdQuery.getKey = (variables: GQLProfilePreviewByEmployeeIdQueryVariables) => ['profilePreviewByEmployeeId', variables];

export const useInfiniteProfilePreviewByEmployeeIdQuery = <
      TData = InfiniteData<GQLProfilePreviewByEmployeeIdQuery>,
      TError = unknown
    >(
      variables: GQLProfilePreviewByEmployeeIdQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLProfilePreviewByEmployeeIdQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLProfilePreviewByEmployeeIdQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLProfilePreviewByEmployeeIdQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['profilePreviewByEmployeeId.infinite', variables],
      queryFn: (metaData) => fetchData<GQLProfilePreviewByEmployeeIdQuery, GQLProfilePreviewByEmployeeIdQueryVariables>(ProfilePreviewByEmployeeIdDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteProfilePreviewByEmployeeIdQuery.getKey = (variables: GQLProfilePreviewByEmployeeIdQueryVariables) => ['profilePreviewByEmployeeId.infinite', variables];


useProfilePreviewByEmployeeIdQuery.fetcher = (variables: GQLProfilePreviewByEmployeeIdQueryVariables, options?: RequestInit['headers']) => fetchData<GQLProfilePreviewByEmployeeIdQuery, GQLProfilePreviewByEmployeeIdQueryVariables>(ProfilePreviewByEmployeeIdDocument, variables, options);

export const ProfileDocument = `
    query profile {
  currentBroker {
    employeeId
    title
    name
    instagram
    usp {
      title
      description
    }
    mobilePhone
    email
    department {
      name
      displayKtiOnEmployee
    }
    rating {
      average
      count
    }
    featuredReviews: reviews(input: {limit: 12, featured: true}) {
      userName
      rating
      createdAt
      review {
        text
      }
    }
    fallbackReviews: reviews(input: {limit: 12, featured: false, rating: 5}) {
      userName
      rating
      createdAt
      review {
        text
      }
    }
    awards {
      id
      name
      origin
      year
    }
    nordvikAwards {
      awardId
      name
      origin
      year
      hidden
      private
    }
    aboutMe
    image {
      medium
    }
  }
}
    `;

export const useProfileQuery = <
      TData = GQLProfileQuery,
      TError = unknown
    >(
      variables?: GQLProfileQueryVariables,
      options?: Omit<UseQueryOptions<GQLProfileQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLProfileQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLProfileQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['profile'] : ['profile', variables],
    queryFn: fetchData<GQLProfileQuery, GQLProfileQueryVariables>(ProfileDocument, variables),
    ...options
  }
    )};

useProfileQuery.getKey = (variables?: GQLProfileQueryVariables) => variables === undefined ? ['profile'] : ['profile', variables];

export const useInfiniteProfileQuery = <
      TData = InfiniteData<GQLProfileQuery>,
      TError = unknown
    >(
      variables: GQLProfileQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLProfileQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLProfileQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLProfileQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? variables === undefined ? ['profile.infinite'] : ['profile.infinite', variables],
      queryFn: (metaData) => fetchData<GQLProfileQuery, GQLProfileQueryVariables>(ProfileDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteProfileQuery.getKey = (variables?: GQLProfileQueryVariables) => variables === undefined ? ['profile.infinite'] : ['profile.infinite', variables];


useProfileQuery.fetcher = (variables?: GQLProfileQueryVariables, options?: RequestInit['headers']) => fetchData<GQLProfileQuery, GQLProfileQueryVariables>(ProfileDocument, variables, options);

export const BrokerProfileLinksDocument = `
    query brokerProfileLinks {
  currentBrokerProfileLinks {
    adLinks
    mediaLinks
  }
}
    `;

export const useBrokerProfileLinksQuery = <
      TData = GQLBrokerProfileLinksQuery,
      TError = unknown
    >(
      variables?: GQLBrokerProfileLinksQueryVariables,
      options?: Omit<UseQueryOptions<GQLBrokerProfileLinksQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLBrokerProfileLinksQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLBrokerProfileLinksQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['brokerProfileLinks'] : ['brokerProfileLinks', variables],
    queryFn: fetchData<GQLBrokerProfileLinksQuery, GQLBrokerProfileLinksQueryVariables>(BrokerProfileLinksDocument, variables),
    ...options
  }
    )};

useBrokerProfileLinksQuery.getKey = (variables?: GQLBrokerProfileLinksQueryVariables) => variables === undefined ? ['brokerProfileLinks'] : ['brokerProfileLinks', variables];

export const useInfiniteBrokerProfileLinksQuery = <
      TData = InfiniteData<GQLBrokerProfileLinksQuery>,
      TError = unknown
    >(
      variables: GQLBrokerProfileLinksQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLBrokerProfileLinksQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLBrokerProfileLinksQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLBrokerProfileLinksQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? variables === undefined ? ['brokerProfileLinks.infinite'] : ['brokerProfileLinks.infinite', variables],
      queryFn: (metaData) => fetchData<GQLBrokerProfileLinksQuery, GQLBrokerProfileLinksQueryVariables>(BrokerProfileLinksDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteBrokerProfileLinksQuery.getKey = (variables?: GQLBrokerProfileLinksQueryVariables) => variables === undefined ? ['brokerProfileLinks.infinite'] : ['brokerProfileLinks.infinite', variables];


useBrokerProfileLinksQuery.fetcher = (variables?: GQLBrokerProfileLinksQueryVariables, options?: RequestInit['headers']) => fetchData<GQLBrokerProfileLinksQuery, GQLBrokerProfileLinksQueryVariables>(BrokerProfileLinksDocument, variables, options);

export const UpdateProfileDocument = `
    mutation updateProfile($input: updateBrokerPayload!) {
  updateBroker(input: $input)
}
    `;

export const useUpdateProfileMutation = <
      TError = unknown,
      TContext = unknown
    >(options?: UseMutationOptions<GQLUpdateProfileMutation, TError, GQLUpdateProfileMutationVariables, TContext>) => {
    
    return useMutation<GQLUpdateProfileMutation, TError, GQLUpdateProfileMutationVariables, TContext>(
      {
    mutationKey: ['updateProfile'],
    mutationFn: (variables?: GQLUpdateProfileMutationVariables) => fetchData<GQLUpdateProfileMutation, GQLUpdateProfileMutationVariables>(UpdateProfileDocument, variables)(),
    ...options
  }
    )};

useUpdateProfileMutation.getKey = () => ['updateProfile'];


useUpdateProfileMutation.fetcher = (variables: GQLUpdateProfileMutationVariables, options?: RequestInit['headers']) => fetchData<GQLUpdateProfileMutation, GQLUpdateProfileMutationVariables>(UpdateProfileDocument, variables, options);

export const AddAwardDocument = `
    mutation addAward($input: CreateAward!) {
  addAward(input: $input)
}
    `;

export const useAddAwardMutation = <
      TError = unknown,
      TContext = unknown
    >(options?: UseMutationOptions<GQLAddAwardMutation, TError, GQLAddAwardMutationVariables, TContext>) => {
    
    return useMutation<GQLAddAwardMutation, TError, GQLAddAwardMutationVariables, TContext>(
      {
    mutationKey: ['addAward'],
    mutationFn: (variables?: GQLAddAwardMutationVariables) => fetchData<GQLAddAwardMutation, GQLAddAwardMutationVariables>(AddAwardDocument, variables)(),
    ...options
  }
    )};

useAddAwardMutation.getKey = () => ['addAward'];


useAddAwardMutation.fetcher = (variables: GQLAddAwardMutationVariables, options?: RequestInit['headers']) => fetchData<GQLAddAwardMutation, GQLAddAwardMutationVariables>(AddAwardDocument, variables, options);

export const UpdateAwardDocument = `
    mutation updateAward($input: UpdateAward!) {
  updateAward(input: $input)
}
    `;

export const useUpdateAwardMutation = <
      TError = unknown,
      TContext = unknown
    >(options?: UseMutationOptions<GQLUpdateAwardMutation, TError, GQLUpdateAwardMutationVariables, TContext>) => {
    
    return useMutation<GQLUpdateAwardMutation, TError, GQLUpdateAwardMutationVariables, TContext>(
      {
    mutationKey: ['updateAward'],
    mutationFn: (variables?: GQLUpdateAwardMutationVariables) => fetchData<GQLUpdateAwardMutation, GQLUpdateAwardMutationVariables>(UpdateAwardDocument, variables)(),
    ...options
  }
    )};

useUpdateAwardMutation.getKey = () => ['updateAward'];


useUpdateAwardMutation.fetcher = (variables: GQLUpdateAwardMutationVariables, options?: RequestInit['headers']) => fetchData<GQLUpdateAwardMutation, GQLUpdateAwardMutationVariables>(UpdateAwardDocument, variables, options);

export const RemoveAwardDocument = `
    mutation removeAward($id: String!) {
  removeAward(id: $id)
}
    `;

export const useRemoveAwardMutation = <
      TError = unknown,
      TContext = unknown
    >(options?: UseMutationOptions<GQLRemoveAwardMutation, TError, GQLRemoveAwardMutationVariables, TContext>) => {
    
    return useMutation<GQLRemoveAwardMutation, TError, GQLRemoveAwardMutationVariables, TContext>(
      {
    mutationKey: ['removeAward'],
    mutationFn: (variables?: GQLRemoveAwardMutationVariables) => fetchData<GQLRemoveAwardMutation, GQLRemoveAwardMutationVariables>(RemoveAwardDocument, variables)(),
    ...options
  }
    )};

useRemoveAwardMutation.getKey = () => ['removeAward'];


useRemoveAwardMutation.fetcher = (variables: GQLRemoveAwardMutationVariables, options?: RequestInit['headers']) => fetchData<GQLRemoveAwardMutation, GQLRemoveAwardMutationVariables>(RemoveAwardDocument, variables, options);

export const HideAwardDocument = `
    mutation hideAward($id: String!, $hidden: Boolean!) {
  hideAward(id: $id, hidden: $hidden)
}
    `;

export const useHideAwardMutation = <
      TError = unknown,
      TContext = unknown
    >(options?: UseMutationOptions<GQLHideAwardMutation, TError, GQLHideAwardMutationVariables, TContext>) => {
    
    return useMutation<GQLHideAwardMutation, TError, GQLHideAwardMutationVariables, TContext>(
      {
    mutationKey: ['hideAward'],
    mutationFn: (variables?: GQLHideAwardMutationVariables) => fetchData<GQLHideAwardMutation, GQLHideAwardMutationVariables>(HideAwardDocument, variables)(),
    ...options
  }
    )};

useHideAwardMutation.getKey = () => ['hideAward'];


useHideAwardMutation.fetcher = (variables: GQLHideAwardMutationVariables, options?: RequestInit['headers']) => fetchData<GQLHideAwardMutation, GQLHideAwardMutationVariables>(HideAwardDocument, variables, options);

export const UpdateLinksDocument = `
    mutation updateLinks($input: BrokerProfileLinksPayload!) {
  updateBrokerProfileLinks(input: $input)
}
    `;

export const useUpdateLinksMutation = <
      TError = unknown,
      TContext = unknown
    >(options?: UseMutationOptions<GQLUpdateLinksMutation, TError, GQLUpdateLinksMutationVariables, TContext>) => {
    
    return useMutation<GQLUpdateLinksMutation, TError, GQLUpdateLinksMutationVariables, TContext>(
      {
    mutationKey: ['updateLinks'],
    mutationFn: (variables?: GQLUpdateLinksMutationVariables) => fetchData<GQLUpdateLinksMutation, GQLUpdateLinksMutationVariables>(UpdateLinksDocument, variables)(),
    ...options
  }
    )};

useUpdateLinksMutation.getKey = () => ['updateLinks'];


useUpdateLinksMutation.fetcher = (variables: GQLUpdateLinksMutationVariables, options?: RequestInit['headers']) => fetchData<GQLUpdateLinksMutation, GQLUpdateLinksMutationVariables>(UpdateLinksDocument, variables, options);

export const AddPartnerDocument = `
    mutation addPartner($input: BrokerPartnerCreateInput!) {
  createBrokerPartner(input: $input) {
    id
    name
    instagram
    website
    description
    category
    images
    createdAt
    updatedAt
    profilePicture
  }
}
    `;

export const useAddPartnerMutation = <
      TError = unknown,
      TContext = unknown
    >(options?: UseMutationOptions<GQLAddPartnerMutation, TError, GQLAddPartnerMutationVariables, TContext>) => {
    
    return useMutation<GQLAddPartnerMutation, TError, GQLAddPartnerMutationVariables, TContext>(
      {
    mutationKey: ['addPartner'],
    mutationFn: (variables?: GQLAddPartnerMutationVariables) => fetchData<GQLAddPartnerMutation, GQLAddPartnerMutationVariables>(AddPartnerDocument, variables)(),
    ...options
  }
    )};

useAddPartnerMutation.getKey = () => ['addPartner'];


useAddPartnerMutation.fetcher = (variables: GQLAddPartnerMutationVariables, options?: RequestInit['headers']) => fetchData<GQLAddPartnerMutation, GQLAddPartnerMutationVariables>(AddPartnerDocument, variables, options);

export const DeletePartnerDocument = `
    mutation deletePartner($id: String!) {
  deleteBrokerPartner(id: $id)
}
    `;

export const useDeletePartnerMutation = <
      TError = unknown,
      TContext = unknown
    >(options?: UseMutationOptions<GQLDeletePartnerMutation, TError, GQLDeletePartnerMutationVariables, TContext>) => {
    
    return useMutation<GQLDeletePartnerMutation, TError, GQLDeletePartnerMutationVariables, TContext>(
      {
    mutationKey: ['deletePartner'],
    mutationFn: (variables?: GQLDeletePartnerMutationVariables) => fetchData<GQLDeletePartnerMutation, GQLDeletePartnerMutationVariables>(DeletePartnerDocument, variables)(),
    ...options
  }
    )};

useDeletePartnerMutation.getKey = () => ['deletePartner'];


useDeletePartnerMutation.fetcher = (variables: GQLDeletePartnerMutationVariables, options?: RequestInit['headers']) => fetchData<GQLDeletePartnerMutation, GQLDeletePartnerMutationVariables>(DeletePartnerDocument, variables, options);

export const UpdateBrokerPartnerDocument = `
    mutation updateBrokerPartner($input: BrokerPartnerUpdateInput!) {
  updateBrokerPartner(input: $input) {
    id
    name
    instagram
    website
    description
    category
    images
    createdAt
    updatedAt
    profilePicture
  }
}
    `;

export const useUpdateBrokerPartnerMutation = <
      TError = unknown,
      TContext = unknown
    >(options?: UseMutationOptions<GQLUpdateBrokerPartnerMutation, TError, GQLUpdateBrokerPartnerMutationVariables, TContext>) => {
    
    return useMutation<GQLUpdateBrokerPartnerMutation, TError, GQLUpdateBrokerPartnerMutationVariables, TContext>(
      {
    mutationKey: ['updateBrokerPartner'],
    mutationFn: (variables?: GQLUpdateBrokerPartnerMutationVariables) => fetchData<GQLUpdateBrokerPartnerMutation, GQLUpdateBrokerPartnerMutationVariables>(UpdateBrokerPartnerDocument, variables)(),
    ...options
  }
    )};

useUpdateBrokerPartnerMutation.getKey = () => ['updateBrokerPartner'];


useUpdateBrokerPartnerMutation.fetcher = (variables: GQLUpdateBrokerPartnerMutationVariables, options?: RequestInit['headers']) => fetchData<GQLUpdateBrokerPartnerMutation, GQLUpdateBrokerPartnerMutationVariables>(UpdateBrokerPartnerDocument, variables, options);

export const HideBrokerPartnerDocument = `
    mutation hideBrokerPartner($id: String!, $hidden: Boolean!) {
  hideBrokerPartner(id: $id, hidden: $hidden) {
    id
    hidden
  }
}
    `;

export const useHideBrokerPartnerMutation = <
      TError = unknown,
      TContext = unknown
    >(options?: UseMutationOptions<GQLHideBrokerPartnerMutation, TError, GQLHideBrokerPartnerMutationVariables, TContext>) => {
    
    return useMutation<GQLHideBrokerPartnerMutation, TError, GQLHideBrokerPartnerMutationVariables, TContext>(
      {
    mutationKey: ['hideBrokerPartner'],
    mutationFn: (variables?: GQLHideBrokerPartnerMutationVariables) => fetchData<GQLHideBrokerPartnerMutation, GQLHideBrokerPartnerMutationVariables>(HideBrokerPartnerDocument, variables)(),
    ...options
  }
    )};

useHideBrokerPartnerMutation.getKey = () => ['hideBrokerPartner'];


useHideBrokerPartnerMutation.fetcher = (variables: GQLHideBrokerPartnerMutationVariables, options?: RequestInit['headers']) => fetchData<GQLHideBrokerPartnerMutation, GQLHideBrokerPartnerMutationVariables>(HideBrokerPartnerDocument, variables, options);

export const ReorderBrokerPartnersDocument = `
    mutation reorderBrokerPartners($ids: [String!]!) {
  reorderBrokerPartners(ids: $ids)
}
    `;

export const useReorderBrokerPartnersMutation = <
      TError = unknown,
      TContext = unknown
    >(options?: UseMutationOptions<GQLReorderBrokerPartnersMutation, TError, GQLReorderBrokerPartnersMutationVariables, TContext>) => {
    
    return useMutation<GQLReorderBrokerPartnersMutation, TError, GQLReorderBrokerPartnersMutationVariables, TContext>(
      {
    mutationKey: ['reorderBrokerPartners'],
    mutationFn: (variables?: GQLReorderBrokerPartnersMutationVariables) => fetchData<GQLReorderBrokerPartnersMutation, GQLReorderBrokerPartnersMutationVariables>(ReorderBrokerPartnersDocument, variables)(),
    ...options
  }
    )};

useReorderBrokerPartnersMutation.getKey = () => ['reorderBrokerPartners'];


useReorderBrokerPartnersMutation.fetcher = (variables: GQLReorderBrokerPartnersMutationVariables, options?: RequestInit['headers']) => fetchData<GQLReorderBrokerPartnersMutation, GQLReorderBrokerPartnersMutationVariables>(ReorderBrokerPartnersDocument, variables, options);

export const GetBrokerPartnersDocument = `
    query getBrokerPartners {
  currentBrokerPartners {
    id
    name
    instagram
    website
    description
    category
    images
    createdAt
    updatedAt
    profilePicture
    hidden
  }
}
    `;

export const useGetBrokerPartnersQuery = <
      TData = GQLGetBrokerPartnersQuery,
      TError = unknown
    >(
      variables?: GQLGetBrokerPartnersQueryVariables,
      options?: Omit<UseQueryOptions<GQLGetBrokerPartnersQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLGetBrokerPartnersQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLGetBrokerPartnersQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['getBrokerPartners'] : ['getBrokerPartners', variables],
    queryFn: fetchData<GQLGetBrokerPartnersQuery, GQLGetBrokerPartnersQueryVariables>(GetBrokerPartnersDocument, variables),
    ...options
  }
    )};

useGetBrokerPartnersQuery.getKey = (variables?: GQLGetBrokerPartnersQueryVariables) => variables === undefined ? ['getBrokerPartners'] : ['getBrokerPartners', variables];

export const useInfiniteGetBrokerPartnersQuery = <
      TData = InfiniteData<GQLGetBrokerPartnersQuery>,
      TError = unknown
    >(
      variables: GQLGetBrokerPartnersQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLGetBrokerPartnersQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLGetBrokerPartnersQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLGetBrokerPartnersQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? variables === undefined ? ['getBrokerPartners.infinite'] : ['getBrokerPartners.infinite', variables],
      queryFn: (metaData) => fetchData<GQLGetBrokerPartnersQuery, GQLGetBrokerPartnersQueryVariables>(GetBrokerPartnersDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteGetBrokerPartnersQuery.getKey = (variables?: GQLGetBrokerPartnersQueryVariables) => variables === undefined ? ['getBrokerPartners.infinite'] : ['getBrokerPartners.infinite', variables];


useGetBrokerPartnersQuery.fetcher = (variables?: GQLGetBrokerPartnersQueryVariables, options?: RequestInit['headers']) => fetchData<GQLGetBrokerPartnersQuery, GQLGetBrokerPartnersQueryVariables>(GetBrokerPartnersDocument, variables, options);

export const ProfilePreviewDocument = `
    query profilePreview {
  currentBroker {
    ...BrokerPresentation
  }
}
    ${BrokerPresentationFragmentDoc}`;

export const useProfilePreviewQuery = <
      TData = GQLProfilePreviewQuery,
      TError = unknown
    >(
      variables?: GQLProfilePreviewQueryVariables,
      options?: Omit<UseQueryOptions<GQLProfilePreviewQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLProfilePreviewQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLProfilePreviewQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['profilePreview'] : ['profilePreview', variables],
    queryFn: fetchData<GQLProfilePreviewQuery, GQLProfilePreviewQueryVariables>(ProfilePreviewDocument, variables),
    ...options
  }
    )};

useProfilePreviewQuery.getKey = (variables?: GQLProfilePreviewQueryVariables) => variables === undefined ? ['profilePreview'] : ['profilePreview', variables];

export const useInfiniteProfilePreviewQuery = <
      TData = InfiniteData<GQLProfilePreviewQuery>,
      TError = unknown
    >(
      variables: GQLProfilePreviewQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLProfilePreviewQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLProfilePreviewQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLProfilePreviewQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? variables === undefined ? ['profilePreview.infinite'] : ['profilePreview.infinite', variables],
      queryFn: (metaData) => fetchData<GQLProfilePreviewQuery, GQLProfilePreviewQueryVariables>(ProfilePreviewDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteProfilePreviewQuery.getKey = (variables?: GQLProfilePreviewQueryVariables) => variables === undefined ? ['profilePreview.infinite'] : ['profilePreview.infinite', variables];


useProfilePreviewQuery.fetcher = (variables?: GQLProfilePreviewQueryVariables, options?: RequestInit['headers']) => fetchData<GQLProfilePreviewQuery, GQLProfilePreviewQueryVariables>(ProfilePreviewDocument, variables, options);

export const AllDepartmentsDocument = `
    query AllDepartments {
  allDepartments {
    items {
      departmentId
      name
    }
  }
}
    `;

export const useAllDepartmentsQuery = <
      TData = GQLAllDepartmentsQuery,
      TError = unknown
    >(
      variables?: GQLAllDepartmentsQueryVariables,
      options?: Omit<UseQueryOptions<GQLAllDepartmentsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLAllDepartmentsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLAllDepartmentsQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['AllDepartments'] : ['AllDepartments', variables],
    queryFn: fetchData<GQLAllDepartmentsQuery, GQLAllDepartmentsQueryVariables>(AllDepartmentsDocument, variables),
    ...options
  }
    )};

useAllDepartmentsQuery.getKey = (variables?: GQLAllDepartmentsQueryVariables) => variables === undefined ? ['AllDepartments'] : ['AllDepartments', variables];

export const useInfiniteAllDepartmentsQuery = <
      TData = InfiniteData<GQLAllDepartmentsQuery>,
      TError = unknown
    >(
      variables: GQLAllDepartmentsQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLAllDepartmentsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLAllDepartmentsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLAllDepartmentsQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? variables === undefined ? ['AllDepartments.infinite'] : ['AllDepartments.infinite', variables],
      queryFn: (metaData) => fetchData<GQLAllDepartmentsQuery, GQLAllDepartmentsQueryVariables>(AllDepartmentsDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteAllDepartmentsQuery.getKey = (variables?: GQLAllDepartmentsQueryVariables) => variables === undefined ? ['AllDepartments.infinite'] : ['AllDepartments.infinite', variables];


useAllDepartmentsQuery.fetcher = (variables?: GQLAllDepartmentsQueryVariables, options?: RequestInit['headers']) => fetchData<GQLAllDepartmentsQuery, GQLAllDepartmentsQueryVariables>(AllDepartmentsDocument, variables, options);

export const ToplistDocument = `
    query Toplist($offset: Int, $limit: Int, $type: String, $estateType: String, $section: ToplistSection, $startDate: Date, $endDate: Date, $departmentId: Int, $marketingPackage: String, $roles: [ToplistRole!]) {
  toplist(
    offset: $offset
    limit: $limit
    type: $type
    estateType: $estateType
    section: $section
    startDate: $startDate
    endDate: $endDate
    departmentId: $departmentId
    marketingPackage: $marketingPackage
    roles: $roles
  ) {
    items {
      id
      name
      departmentId
      department
      value
      avatarUrl
      count
      reviews
    }
    totalCount
  }
}
    `;

export const useToplistQuery = <
      TData = GQLToplistQuery,
      TError = unknown
    >(
      variables?: GQLToplistQueryVariables,
      options?: Omit<UseQueryOptions<GQLToplistQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLToplistQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLToplistQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['Toplist'] : ['Toplist', variables],
    queryFn: fetchData<GQLToplistQuery, GQLToplistQueryVariables>(ToplistDocument, variables),
    ...options
  }
    )};

useToplistQuery.getKey = (variables?: GQLToplistQueryVariables) => variables === undefined ? ['Toplist'] : ['Toplist', variables];

export const useInfiniteToplistQuery = <
      TData = InfiniteData<GQLToplistQuery>,
      TError = unknown
    >(
      variables: GQLToplistQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLToplistQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLToplistQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLToplistQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? variables === undefined ? ['Toplist.infinite'] : ['Toplist.infinite', variables],
      queryFn: (metaData) => fetchData<GQLToplistQuery, GQLToplistQueryVariables>(ToplistDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteToplistQuery.getKey = (variables?: GQLToplistQueryVariables) => variables === undefined ? ['Toplist.infinite'] : ['Toplist.infinite', variables];


useToplistQuery.fetcher = (variables?: GQLToplistQueryVariables, options?: RequestInit['headers']) => fetchData<GQLToplistQuery, GQLToplistQueryVariables>(ToplistDocument, variables, options);

export const BrokerByEmailDocument = `
    query BrokerByEmail($email: String!) {
  brokerByEmail(email: $email) {
    id
    employeeId
    department {
      departmentId
    }
    name
  }
}
    `;

export const useBrokerByEmailQuery = <
      TData = GQLBrokerByEmailQuery,
      TError = unknown
    >(
      variables: GQLBrokerByEmailQueryVariables,
      options?: Omit<UseQueryOptions<GQLBrokerByEmailQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLBrokerByEmailQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLBrokerByEmailQuery, TError, TData>(
      {
    queryKey: ['BrokerByEmail', variables],
    queryFn: fetchData<GQLBrokerByEmailQuery, GQLBrokerByEmailQueryVariables>(BrokerByEmailDocument, variables),
    ...options
  }
    )};

useBrokerByEmailQuery.getKey = (variables: GQLBrokerByEmailQueryVariables) => ['BrokerByEmail', variables];

export const useInfiniteBrokerByEmailQuery = <
      TData = InfiniteData<GQLBrokerByEmailQuery>,
      TError = unknown
    >(
      variables: GQLBrokerByEmailQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLBrokerByEmailQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLBrokerByEmailQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLBrokerByEmailQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['BrokerByEmail.infinite', variables],
      queryFn: (metaData) => fetchData<GQLBrokerByEmailQuery, GQLBrokerByEmailQueryVariables>(BrokerByEmailDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteBrokerByEmailQuery.getKey = (variables: GQLBrokerByEmailQueryVariables) => ['BrokerByEmail.infinite', variables];


useBrokerByEmailQuery.fetcher = (variables: GQLBrokerByEmailQueryVariables, options?: RequestInit['headers']) => fetchData<GQLBrokerByEmailQuery, GQLBrokerByEmailQueryVariables>(BrokerByEmailDocument, variables, options);

export const HallOfFameDocument = `
    query HallOfFame {
  hallOfFame {
    year
    entries {
      awardId
      name
      employee {
        employeeId
        id
        name
        department {
          name
        }
        image {
          medium
        }
      }
    }
  }
}
    `;

export const useHallOfFameQuery = <
      TData = GQLHallOfFameQuery,
      TError = unknown
    >(
      variables?: GQLHallOfFameQueryVariables,
      options?: Omit<UseQueryOptions<GQLHallOfFameQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLHallOfFameQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLHallOfFameQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['HallOfFame'] : ['HallOfFame', variables],
    queryFn: fetchData<GQLHallOfFameQuery, GQLHallOfFameQueryVariables>(HallOfFameDocument, variables),
    ...options
  }
    )};

useHallOfFameQuery.getKey = (variables?: GQLHallOfFameQueryVariables) => variables === undefined ? ['HallOfFame'] : ['HallOfFame', variables];

export const useInfiniteHallOfFameQuery = <
      TData = InfiniteData<GQLHallOfFameQuery>,
      TError = unknown
    >(
      variables: GQLHallOfFameQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLHallOfFameQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLHallOfFameQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLHallOfFameQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? variables === undefined ? ['HallOfFame.infinite'] : ['HallOfFame.infinite', variables],
      queryFn: (metaData) => fetchData<GQLHallOfFameQuery, GQLHallOfFameQueryVariables>(HallOfFameDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteHallOfFameQuery.getKey = (variables?: GQLHallOfFameQueryVariables) => variables === undefined ? ['HallOfFame.infinite'] : ['HallOfFame.infinite', variables];


useHallOfFameQuery.fetcher = (variables?: GQLHallOfFameQueryVariables, options?: RequestInit['headers']) => fetchData<GQLHallOfFameQuery, GQLHallOfFameQueryVariables>(HallOfFameDocument, variables, options);

export const DashboardCacheDocument = `
    query DashboardCache {
  dashboardCache
}
    `;

export const useDashboardCacheQuery = <
      TData = GQLDashboardCacheQuery,
      TError = unknown
    >(
      variables?: GQLDashboardCacheQueryVariables,
      options?: Omit<UseQueryOptions<GQLDashboardCacheQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLDashboardCacheQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLDashboardCacheQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['DashboardCache'] : ['DashboardCache', variables],
    queryFn: fetchData<GQLDashboardCacheQuery, GQLDashboardCacheQueryVariables>(DashboardCacheDocument, variables),
    ...options
  }
    )};

useDashboardCacheQuery.getKey = (variables?: GQLDashboardCacheQueryVariables) => variables === undefined ? ['DashboardCache'] : ['DashboardCache', variables];

export const useInfiniteDashboardCacheQuery = <
      TData = InfiniteData<GQLDashboardCacheQuery>,
      TError = unknown
    >(
      variables: GQLDashboardCacheQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLDashboardCacheQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLDashboardCacheQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLDashboardCacheQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? variables === undefined ? ['DashboardCache.infinite'] : ['DashboardCache.infinite', variables],
      queryFn: (metaData) => fetchData<GQLDashboardCacheQuery, GQLDashboardCacheQueryVariables>(DashboardCacheDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteDashboardCacheQuery.getKey = (variables?: GQLDashboardCacheQueryVariables) => variables === undefined ? ['DashboardCache.infinite'] : ['DashboardCache.infinite', variables];


useDashboardCacheQuery.fetcher = (variables?: GQLDashboardCacheQueryVariables, options?: RequestInit['headers']) => fetchData<GQLDashboardCacheQuery, GQLDashboardCacheQueryVariables>(DashboardCacheDocument, variables, options);

export const DashboardCacheForEmployeeDocument = `
    query DashboardCacheForEmployee($employeeId: String!, $section: Section!) {
  dashboardCacheForEmployee(employeeId: $employeeId, section: $section)
}
    `;

export const useDashboardCacheForEmployeeQuery = <
      TData = GQLDashboardCacheForEmployeeQuery,
      TError = unknown
    >(
      variables: GQLDashboardCacheForEmployeeQueryVariables,
      options?: Omit<UseQueryOptions<GQLDashboardCacheForEmployeeQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLDashboardCacheForEmployeeQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLDashboardCacheForEmployeeQuery, TError, TData>(
      {
    queryKey: ['DashboardCacheForEmployee', variables],
    queryFn: fetchData<GQLDashboardCacheForEmployeeQuery, GQLDashboardCacheForEmployeeQueryVariables>(DashboardCacheForEmployeeDocument, variables),
    ...options
  }
    )};

useDashboardCacheForEmployeeQuery.getKey = (variables: GQLDashboardCacheForEmployeeQueryVariables) => ['DashboardCacheForEmployee', variables];

export const useInfiniteDashboardCacheForEmployeeQuery = <
      TData = InfiniteData<GQLDashboardCacheForEmployeeQuery>,
      TError = unknown
    >(
      variables: GQLDashboardCacheForEmployeeQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLDashboardCacheForEmployeeQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLDashboardCacheForEmployeeQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLDashboardCacheForEmployeeQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['DashboardCacheForEmployee.infinite', variables],
      queryFn: (metaData) => fetchData<GQLDashboardCacheForEmployeeQuery, GQLDashboardCacheForEmployeeQueryVariables>(DashboardCacheForEmployeeDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteDashboardCacheForEmployeeQuery.getKey = (variables: GQLDashboardCacheForEmployeeQueryVariables) => ['DashboardCacheForEmployee.infinite', variables];


useDashboardCacheForEmployeeQuery.fetcher = (variables: GQLDashboardCacheForEmployeeQueryVariables, options?: RequestInit['headers']) => fetchData<GQLDashboardCacheForEmployeeQuery, GQLDashboardCacheForEmployeeQueryVariables>(DashboardCacheForEmployeeDocument, variables, options);

export const CurrentBrokerDocument = `
    query currentBroker {
  currentBroker {
    id
    name
    email
    image {
      medium
    }
    createdDate
    employeeId
    department {
      id
      name
      departmentId
    }
    mobilePhone
  }
}
    `;

export const useCurrentBrokerQuery = <
      TData = GQLCurrentBrokerQuery,
      TError = unknown
    >(
      variables?: GQLCurrentBrokerQueryVariables,
      options?: Omit<UseQueryOptions<GQLCurrentBrokerQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLCurrentBrokerQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLCurrentBrokerQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['currentBroker'] : ['currentBroker', variables],
    queryFn: fetchData<GQLCurrentBrokerQuery, GQLCurrentBrokerQueryVariables>(CurrentBrokerDocument, variables),
    ...options
  }
    )};

useCurrentBrokerQuery.getKey = (variables?: GQLCurrentBrokerQueryVariables) => variables === undefined ? ['currentBroker'] : ['currentBroker', variables];

export const useInfiniteCurrentBrokerQuery = <
      TData = InfiniteData<GQLCurrentBrokerQuery>,
      TError = unknown
    >(
      variables: GQLCurrentBrokerQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLCurrentBrokerQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLCurrentBrokerQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLCurrentBrokerQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? variables === undefined ? ['currentBroker.infinite'] : ['currentBroker.infinite', variables],
      queryFn: (metaData) => fetchData<GQLCurrentBrokerQuery, GQLCurrentBrokerQueryVariables>(CurrentBrokerDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteCurrentBrokerQuery.getKey = (variables?: GQLCurrentBrokerQueryVariables) => variables === undefined ? ['currentBroker.infinite'] : ['currentBroker.infinite', variables];


useCurrentBrokerQuery.fetcher = (variables?: GQLCurrentBrokerQueryVariables, options?: RequestInit['headers']) => fetchData<GQLCurrentBrokerQuery, GQLCurrentBrokerQueryVariables>(CurrentBrokerDocument, variables, options);

export const TrackVisitDocument = `
    query trackVisit($pageId: String!, $estateId: String!) {
  pageVisitHeartbeat(pageId: $pageId, estateId: $estateId) {
    id
    lastHeartbeat
  }
}
    `;

export const useTrackVisitQuery = <
      TData = GQLTrackVisitQuery,
      TError = unknown
    >(
      variables: GQLTrackVisitQueryVariables,
      options?: Omit<UseQueryOptions<GQLTrackVisitQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLTrackVisitQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLTrackVisitQuery, TError, TData>(
      {
    queryKey: ['trackVisit', variables],
    queryFn: fetchData<GQLTrackVisitQuery, GQLTrackVisitQueryVariables>(TrackVisitDocument, variables),
    ...options
  }
    )};

useTrackVisitQuery.getKey = (variables: GQLTrackVisitQueryVariables) => ['trackVisit', variables];

export const useInfiniteTrackVisitQuery = <
      TData = InfiniteData<GQLTrackVisitQuery>,
      TError = unknown
    >(
      variables: GQLTrackVisitQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLTrackVisitQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLTrackVisitQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLTrackVisitQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['trackVisit.infinite', variables],
      queryFn: (metaData) => fetchData<GQLTrackVisitQuery, GQLTrackVisitQueryVariables>(TrackVisitDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteTrackVisitQuery.getKey = (variables: GQLTrackVisitQueryVariables) => ['trackVisit.infinite', variables];


useTrackVisitQuery.fetcher = (variables: GQLTrackVisitQueryVariables, options?: RequestInit['headers']) => fetchData<GQLTrackVisitQuery, GQLTrackVisitQueryVariables>(TrackVisitDocument, variables, options);

export const EndVisitDocument = `
    mutation endVisit($pageId: String!) {
  endPageVisit(pageId: $pageId)
}
    `;

export const useEndVisitMutation = <
      TError = unknown,
      TContext = unknown
    >(options?: UseMutationOptions<GQLEndVisitMutation, TError, GQLEndVisitMutationVariables, TContext>) => {
    
    return useMutation<GQLEndVisitMutation, TError, GQLEndVisitMutationVariables, TContext>(
      {
    mutationKey: ['endVisit'],
    mutationFn: (variables?: GQLEndVisitMutationVariables) => fetchData<GQLEndVisitMutation, GQLEndVisitMutationVariables>(EndVisitDocument, variables)(),
    ...options
  }
    )};

useEndVisitMutation.getKey = () => ['endVisit'];


useEndVisitMutation.fetcher = (variables: GQLEndVisitMutationVariables, options?: RequestInit['headers']) => fetchData<GQLEndVisitMutation, GQLEndVisitMutationVariables>(EndVisitDocument, variables, options);

export const BefaringVisitsDocument = `
    query befaringVisits($estateId: String!) {
  pageVisits(estateId: $estateId) {
    id
    estateId
    contactId
    employeeId
    pageId
    startTime
    lastHeartbeat
    endTime
    source
    browser
    location
  }
}
    `;

export const useBefaringVisitsQuery = <
      TData = GQLBefaringVisitsQuery,
      TError = unknown
    >(
      variables: GQLBefaringVisitsQueryVariables,
      options?: Omit<UseQueryOptions<GQLBefaringVisitsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLBefaringVisitsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLBefaringVisitsQuery, TError, TData>(
      {
    queryKey: ['befaringVisits', variables],
    queryFn: fetchData<GQLBefaringVisitsQuery, GQLBefaringVisitsQueryVariables>(BefaringVisitsDocument, variables),
    ...options
  }
    )};

useBefaringVisitsQuery.getKey = (variables: GQLBefaringVisitsQueryVariables) => ['befaringVisits', variables];

export const useInfiniteBefaringVisitsQuery = <
      TData = InfiniteData<GQLBefaringVisitsQuery>,
      TError = unknown
    >(
      variables: GQLBefaringVisitsQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLBefaringVisitsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLBefaringVisitsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLBefaringVisitsQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['befaringVisits.infinite', variables],
      queryFn: (metaData) => fetchData<GQLBefaringVisitsQuery, GQLBefaringVisitsQueryVariables>(BefaringVisitsDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteBefaringVisitsQuery.getKey = (variables: GQLBefaringVisitsQueryVariables) => ['befaringVisits.infinite', variables];


useBefaringVisitsQuery.fetcher = (variables: GQLBefaringVisitsQueryVariables, options?: RequestInit['headers']) => fetchData<GQLBefaringVisitsQuery, GQLBefaringVisitsQueryVariables>(BefaringVisitsDocument, variables, options);

export const UserHasFlagDocument = `
    query userHasFlag($flag: String!) {
  userHasFlag(flag: $flag)
}
    `;

export const useUserHasFlagQuery = <
      TData = GQLUserHasFlagQuery,
      TError = unknown
    >(
      variables: GQLUserHasFlagQueryVariables,
      options?: Omit<UseQueryOptions<GQLUserHasFlagQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLUserHasFlagQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLUserHasFlagQuery, TError, TData>(
      {
    queryKey: ['userHasFlag', variables],
    queryFn: fetchData<GQLUserHasFlagQuery, GQLUserHasFlagQueryVariables>(UserHasFlagDocument, variables),
    ...options
  }
    )};

useUserHasFlagQuery.getKey = (variables: GQLUserHasFlagQueryVariables) => ['userHasFlag', variables];

export const useInfiniteUserHasFlagQuery = <
      TData = InfiniteData<GQLUserHasFlagQuery>,
      TError = unknown
    >(
      variables: GQLUserHasFlagQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLUserHasFlagQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLUserHasFlagQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLUserHasFlagQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['userHasFlag.infinite', variables],
      queryFn: (metaData) => fetchData<GQLUserHasFlagQuery, GQLUserHasFlagQueryVariables>(UserHasFlagDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteUserHasFlagQuery.getKey = (variables: GQLUserHasFlagQueryVariables) => ['userHasFlag.infinite', variables];


useUserHasFlagQuery.fetcher = (variables: GQLUserHasFlagQueryVariables, options?: RequestInit['headers']) => fetchData<GQLUserHasFlagQuery, GQLUserHasFlagQueryVariables>(UserHasFlagDocument, variables, options);

export const UserSetFlagDocument = `
    mutation userSetFlag($flag: String!, $value: Boolean!) {
  userSetFlag(flag: $flag, value: $value)
}
    `;

export const useUserSetFlagMutation = <
      TError = unknown,
      TContext = unknown
    >(options?: UseMutationOptions<GQLUserSetFlagMutation, TError, GQLUserSetFlagMutationVariables, TContext>) => {
    
    return useMutation<GQLUserSetFlagMutation, TError, GQLUserSetFlagMutationVariables, TContext>(
      {
    mutationKey: ['userSetFlag'],
    mutationFn: (variables?: GQLUserSetFlagMutationVariables) => fetchData<GQLUserSetFlagMutation, GQLUserSetFlagMutationVariables>(UserSetFlagDocument, variables)(),
    ...options
  }
    )};

useUserSetFlagMutation.getKey = () => ['userSetFlag'];


useUserSetFlagMutation.fetcher = (variables: GQLUserSetFlagMutationVariables, options?: RequestInit['headers']) => fetchData<GQLUserSetFlagMutation, GQLUserSetFlagMutationVariables>(UserSetFlagDocument, variables, options);

export const UserResetAllFlagsDocument = `
    mutation userResetAllFlags {
  userResetAllFlags
}
    `;

export const useUserResetAllFlagsMutation = <
      TError = unknown,
      TContext = unknown
    >(options?: UseMutationOptions<GQLUserResetAllFlagsMutation, TError, GQLUserResetAllFlagsMutationVariables, TContext>) => {
    
    return useMutation<GQLUserResetAllFlagsMutation, TError, GQLUserResetAllFlagsMutationVariables, TContext>(
      {
    mutationKey: ['userResetAllFlags'],
    mutationFn: (variables?: GQLUserResetAllFlagsMutationVariables) => fetchData<GQLUserResetAllFlagsMutation, GQLUserResetAllFlagsMutationVariables>(UserResetAllFlagsDocument, variables)(),
    ...options
  }
    )};

useUserResetAllFlagsMutation.getKey = () => ['userResetAllFlags'];


useUserResetAllFlagsMutation.fetcher = (variables?: GQLUserResetAllFlagsMutationVariables, options?: RequestInit['headers']) => fetchData<GQLUserResetAllFlagsMutation, GQLUserResetAllFlagsMutationVariables>(UserResetAllFlagsDocument, variables, options);

export const LeadsDocument = `
    query leads($estateId: String!, $leadType: InspectionLeadType) {
  inspectionLeadsForEstate(estateId: $estateId, leadType: $leadType) {
    id
    contactId
    successful
    comment
  }
}
    `;

export const useLeadsQuery = <
      TData = GQLLeadsQuery,
      TError = unknown
    >(
      variables: GQLLeadsQueryVariables,
      options?: Omit<UseQueryOptions<GQLLeadsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GQLLeadsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GQLLeadsQuery, TError, TData>(
      {
    queryKey: ['leads', variables],
    queryFn: fetchData<GQLLeadsQuery, GQLLeadsQueryVariables>(LeadsDocument, variables),
    ...options
  }
    )};

useLeadsQuery.getKey = (variables: GQLLeadsQueryVariables) => ['leads', variables];

export const useInfiniteLeadsQuery = <
      TData = InfiniteData<GQLLeadsQuery>,
      TError = unknown
    >(
      variables: GQLLeadsQueryVariables,
      options: Omit<UseInfiniteQueryOptions<GQLLeadsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseInfiniteQueryOptions<GQLLeadsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useInfiniteQuery<GQLLeadsQuery, TError, TData>(
      (() => {
    const { queryKey: optionsQueryKey, ...restOptions } = options;
    return {
      queryKey: optionsQueryKey ?? ['leads.infinite', variables],
      queryFn: (metaData) => fetchData<GQLLeadsQuery, GQLLeadsQueryVariables>(LeadsDocument, {...variables, ...(metaData.pageParam ?? {})})(),
      ...restOptions
    }
  })()
    )};

useInfiniteLeadsQuery.getKey = (variables: GQLLeadsQueryVariables) => ['leads.infinite', variables];


useLeadsQuery.fetcher = (variables: GQLLeadsQueryVariables, options?: RequestInit['headers']) => fetchData<GQLLeadsQuery, GQLLeadsQueryVariables>(LeadsDocument, variables, options);

export const SendLeadDocument = `
    mutation sendLead($estateId: String!, $contactId: String!, $leadType: InspectionLeadType!, $source: String) {
  sendInspectionLead(
    estateId: $estateId
    contactId: $contactId
    leadType: $leadType
    source: $source
  ) {
    id
    successful
  }
}
    `;

export const useSendLeadMutation = <
      TError = unknown,
      TContext = unknown
    >(options?: UseMutationOptions<GQLSendLeadMutation, TError, GQLSendLeadMutationVariables, TContext>) => {
    
    return useMutation<GQLSendLeadMutation, TError, GQLSendLeadMutationVariables, TContext>(
      {
    mutationKey: ['sendLead'],
    mutationFn: (variables?: GQLSendLeadMutationVariables) => fetchData<GQLSendLeadMutation, GQLSendLeadMutationVariables>(SendLeadDocument, variables)(),
    ...options
  }
    )};

useSendLeadMutation.getKey = () => ['sendLead'];


useSendLeadMutation.fetcher = (variables: GQLSendLeadMutationVariables, options?: RequestInit['headers']) => fetchData<GQLSendLeadMutation, GQLSendLeadMutationVariables>(SendLeadDocument, variables, options);
