import React from 'react'

import EmailStoryPreview from '../../stories/email-story-preview'
import { getVitecMismatchTemplate } from '../get-vitec-mismatch-template'

export default {
  title: 'Email / Dynamic',
  parameters: {
    theme: 'light',
  },
  decorators: [
    (Story) => (
      <div className="@container/section">
        <Story />
      </div>
    ),
  ],
}

export const VitecMissmatch = () => {
  return (
    <EmailStoryPreview
      html={getVitecMismatchTemplate({
        validation: {
          type: 1,
          suggestedPriceVitec: 1,
          suggestedPriceDatabase: 10,
          feePercentageVitec: 110,
          feePercentageDatabase: 100,
          commissionVitec: 120,
          commissionDatabase: 1200,
          isSynced: false,
        },
        address: 'Testveien 1, 0000 Oslo',
        linkToNext: 'http://google.com',
        marketingPackage: 'Nordvik Ekstra',
      })}
    />
  )
}
