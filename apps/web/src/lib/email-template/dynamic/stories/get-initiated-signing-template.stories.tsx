import React from 'react'

import EmailStoryPreview from '../../stories/email-story-preview'
import { getInitiatedSigningTemplate } from '../get-initiated-signing-template'

export default {
  title: 'Email / Dynamic',
  parameters: {
    theme: 'light',
  },
  decorators: [
    (Story) => (
      <div className="@container/section">
        <Story />
      </div>
    ),
  ],
}

export const InitiatedSigning = () => {
  return (
    <EmailStoryPreview
      html={getInitiatedSigningTemplate({
        initiatedBy: '<PERSON> Unfoldsen',
        estateId: '0A8D9ADB-72BD-4E61-8737-FFDFD51D522F',
      })}
    />
  )
}
