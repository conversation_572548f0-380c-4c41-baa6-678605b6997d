import React from 'react'

import EmailStoryPreview from '../../stories/email-story-preview'
import { getSignedByAllSellersTemplate } from '../get-signed-by-all-sellers-temaplate'

export default {
  title: 'Email / Dynamic',
  parameters: {
    theme: 'light',
  },
  decorators: [
    (Story) => (
      <div className="@container/section">
        <Story />
      </div>
    ),
  ],
}

export const SignedByAllSellers = () => {
  return (
    <EmailStoryPreview
      html={getSignedByAllSellersTemplate({
        sellers: [
          {
            first_name: '<PERSON>',
            last_name: '<PERSON><PERSON><PERSON>',
            signed_at: new Date('2025.12.31'),
          },
          {
            first_name: '<PERSON>',
            last_name: '<PERSON><PERSON><PERSON>',
            signed_at: new Date(),
          },
        ],
        brokers: [
          {
            first_name: '<PERSON>',
            last_name: '<PERSON><PERSON><PERSON>',
            signed_at: new Date(),
          },
          {
            first_name: '<PERSON>',
            last_name: '<PERSON><PERSON><PERSON>',
            signed_at: new Date(),
          },
        ],
        address: 'Testveien 1',
        estateId: '0A8D9ADB-72BD-4E61-8737-FFDFD51D522F',
      })}
    />
  )
}
