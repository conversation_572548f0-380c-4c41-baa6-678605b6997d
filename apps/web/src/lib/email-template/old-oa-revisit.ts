import { getHtmlWithStyleReset } from './get-html'

interface OldOARevisitTemplateArgs {
  brokerFirstName?: string
  contactName?: string
  address: string
  daysSinceLastVisit: number
  title?: string
  estateId?: string
}

const html = String.raw

export function oldOARevisitTemplate({
  brokerFirstName,
  contactName,
  address,
  daysSinceLastVisit,
  title = 'Oppdragsavtale åpnet igjen',
  estateId,
}: OldOARevisitTemplateArgs) {
  const inactivityText =
    daysSinceLastVisit >= 14 ? 'to uker' : `${daysSinceLastVisit} dager`

  const baseUrl =
    process.env.NEXT_PUBLIC_URL || process.env.NEXT_PUBLIC_VERCEL_BRANCH_URL
      ? process.env.NEXT_PUBLIC_URL ||
        `https://${process.env.NEXT_PUBLIC_VERCEL_BRANCH_URL}`
      : undefined
  const detailUrl =
    estateId && baseUrl ? `${baseUrl}/oppdrag/detaljer/${estateId}` : undefined

  const content = html` <div class="container">
    <div class="content">
      <h1>${title}</h1>
      <p>Hei ${brokerFirstName ?? ''},</p>
      <p>
        For en time siden var ${contactName || 'selgeren'} på ${address} inne og
        åpnet oppdragsavtalen på nytt. Det er nå ${inactivityText} siden han
        sist var inne.
      </p>
      <p>
        Det kan være et godt tidspunkt å ta kontakt. Kanskje han er klar for å
        selge?
      </p>
      ${detailUrl
        ? html`<p style="margin-top:24px;">
            <a
              href="${detailUrl}"
              target="_blank"
              style="display:inline-block;background:#002d32;color:#ffffff;text-decoration:none;padding:10px 18px;border-radius:6px;font-size:14px;"
              >Åpne oppdragssiden</a
            >
          </p>`
        : ''}
      <p style="color:#888; font-size:12px;">
        (Automatisk varsling ved 14 dagers inaktivitet på oppdragsavtalen.)
      </p>
    </div>
  </div>`

  return getHtmlWithStyleReset(content, title)
}
