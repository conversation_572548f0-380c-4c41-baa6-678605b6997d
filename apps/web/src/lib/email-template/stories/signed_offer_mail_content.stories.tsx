import React from 'react'

import { signedOfferMailContent } from '../signed_offer_mail_content'

import EmailStoryPreview from './email-story-preview'

export default {
  title: 'Email',
  parameters: {
    theme: 'light',
  },
  decorators: [
    (Story) => (
      <div className="@container/section">
        <Story />
      </div>
    ),
  ],
}

export const SignedOffer = () => {
  const html = signedOfferMailContent({
    fornavn: 'Nora',
    isCompany: false,
  })

  return <EmailStoryPreview html={html} />
}
