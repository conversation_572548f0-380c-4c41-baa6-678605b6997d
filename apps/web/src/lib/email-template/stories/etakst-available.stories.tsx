import React from 'react'

import { etakstAvailableTemplate } from '../etakst-available'

import EmailStoryPreview from './email-story-preview'

export default {
  title: 'Email',
  parameters: {
    theme: 'light',
  },
  decorators: [
    (Story) => (
      <div className="@container/section">
        <Story />
      </div>
    ),
  ],
}

export const EtakstAvilable = () => {
  const html = etakstAvailableTemplate({
    withStorebrand: true,
    customerName: '<PERSON> Unfoldsen',
    estateName: 'Testveien 1',
    authenticatedLink: 'https://google.com',
    title: 'Title',
  })

  return <EmailStoryPreview html={html} />
}
