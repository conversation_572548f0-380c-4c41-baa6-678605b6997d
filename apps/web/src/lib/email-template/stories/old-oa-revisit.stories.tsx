import React from 'react'

import { oldOARevisitTemplate } from '../old-oa-revisit'

import EmailStoryPreview from './email-story-preview'

export default {
  title: 'Email',
  parameters: {
    theme: 'light',
  },
  decorators: [
    (Story) => (
      <div className="@container/section">
        <Story />
      </div>
    ),
  ],
}

export const OldOaRevisit = () => {
  const html = oldOARevisitTemplate({
    brokerFirstName: 'Nora',
    contactName: 'Unfoldsen',
    address: 'Testveien 1',
    daysSinceLastVisit: 2,
    title: 'Title',
    estateId: '0A8D9ADB-72BD-4E61-8737-FFDFD51D522F',
  })

  return <EmailStoryPreview html={html} />
}
