import type { Session } from 'next-auth'

import { createEmployeeLoader } from '@/app/api/graphql/loaders/employee-loader'
import { createEstateLoader } from '@/app/api/graphql/loaders/estate-loader'
import { createEtakstCheckLoader } from '@/app/api/graphql/loaders/etakst-check-leader'
import { createInspectionEventLoader } from '@/app/api/graphql/loaders/inspection-event-loader'
import { createInspectionFolderLoader } from '@/app/api/graphql/loaders/inspection-folder-loader'
import { createListingAgreementLoader } from '@/app/api/graphql/loaders/listing-agreement-loader'

export class Context {
  public user?: Session['user']
  public loaders: {
    listingAgreementLoader: ReturnType<typeof createListingAgreementLoader>
    inspectionFolderLoader: ReturnType<typeof createInspectionFolderLoader>
    etakstCheckLoader: ReturnType<typeof createEtakstCheckLoader>
    estateLoader: ReturnType<typeof createEstateLoader>
    employeeLoader: ReturnType<typeof createEmployeeLoader>
    inspectionEventLoader: ReturnType<typeof createInspectionEventLoader>
  }
}
