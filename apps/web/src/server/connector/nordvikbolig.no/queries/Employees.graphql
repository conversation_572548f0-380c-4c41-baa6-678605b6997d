query employeeWithRatingAndAwards($email: String, $employeeId: String) {
  employee(email: $email, employeeId: $employeeId, all: true) {
    id
    slug
    employeeId
    title
    name
    email
    departmentId
    mobilePhone
    employeeActive
    roles {
      source
      typeId
      name
    }

    createdDate
    department {
      id
      name
      departmentId
      kti
      rating {
        count
      }
      displayKtiOnEmployee
    }

    image {
      small
      medium
    }

    kti

    aboutMe

    instagram
    usp {
      title
      description
    }

    awards {
      id
      name
      origin
      year
    }

    nordvikAwards {
      awardId
      name
      origin
      year
      private
      hidden
    }

    rating {
      average
      count
      reviewsCount
      weighted
    }
  }
}

query employeeSessionInfo($employeeId: String) {
  employee(employeeId: $employeeId, all: true) {
    id
    employeeId
    slug
    title
    name
    email
    departmentId
    mobilePhone
    employeeActive
    roles {
      source
      typeId
      name
    }
    createdDate
    department {
      id
      name
      departmentId
      kti
      displayKtiOnEmployee
    }
    image {
      small
    }
  }
}

query employeeSessionInfoByEmail($email: String) {
  employee(email: $email, all: true) {
    id
    employeeId
    title
    name
    email
    departmentId
    mobilePhone
    employeeActive
    createdDate
    department {
      id
      name
      departmentId
      kti
      displayKtiOnEmployee
    }
    image {
      small
    }
  }
}

query activeEmployeeByEmail($email: String) {
  employee(email: $email) {
    id
    employeeId
    title
    name
    email
    departmentId
    createdDate
    roles {
      source
      typeId
      name
    }
    image {
      small
      medium
    }
  }
}

query employeeForAdplenty($employeeId: String!) {
  employee(employeeId: $employeeId, all: true) {
    employeeId
    employeeActive
    webPublish
    slug
    email
    name
    title
    mobilePhone
    image {
      medium
    }
    department {
      id
      departmentId
      name
    }
    description
    changedDate
  }
}

query employees($limit: Int, $offset: Int, $ids: [String!]) {
  employees(limit: $limit, offset: $offset, ids: $ids) {
    count
    employeesEntries {
      employeeId
      id
      name
      email
      employeeActive
      image {
        small
        large
      }
      roles {
        source
        typeId
        name
      }
      department {
        id
        name
        departmentId
      }
    }
  }
}

query employeesEstatesEntriesByIdCount(
  $employeeId: String
  $statuses: [Int]
  $expireDateAfter: Date
  $takeOverDateAfter: Date
  $soldDateBefore: Date
  $inspectionDateAfter: Date
  $archived: Boolean
  $allMarketReady: Boolean
  $assignmentTypeGroup: [Int!]
  $createdDateAfter: Date
) {
  employee(employeeId: $employeeId, all: true) {
    estateEntries(
      statuses: $statuses
      expireDateAfter: $expireDateAfter
      takeOverDateAfter: $takeOverDateAfter
      soldDateBefore: $soldDateBefore
      inspectionDateAfter: $inspectionDateAfter
      archived: $archived
      allMarketReady: $allMarketReady
      assignmentTypeGroup: $assignmentTypeGroup
      createdDateAfter: $createdDateAfter
    ) {
      pagination {
        total
      }
    }
  }
}

query employeesEstatesEntriesById(
  $employeeId: String
  $statuses: [Int]
  $status: Int
  $expireDateAfter: Date
  $takeOverDateAfter: Date
  $assignmentTypeGroup: [Int!]
  $soldDateBefore: Date
  $inspectionDateAfter: Date
  $archived: Boolean
  $allMarketReady: Boolean
  $offset: Int
  $limit: Int
  $search: String
  $sortBy: String
  $createdDateAfter: Date
) {
  employee(employeeId: $employeeId, all: true) {
    estateEntries(
      statuses: $statuses
      status: $status
      expireDateAfter: $expireDateAfter
      takeOverDateAfter: $takeOverDateAfter
      soldDateBefore: $soldDateBefore
      inspectionDateAfter: $inspectionDateAfter
      archived: $archived
      allMarketReady: $allMarketReady
      pagination: { offset: $offset, limit: $limit }
      search: $search
      sortBy: $sortBy
      assignmentTypeGroup: $assignmentTypeGroup
      createdDateAfter: $createdDateAfter
    ) {
      pagination {
        offset
        limit
        count
        total
      }
      data {
        ...EstateListEntryFields
      }
    }
  }
}

query employeeDepartments($email: String, $employeeId: String) {
  employee(email: $email, employeeId: $employeeId, all: true) {
    department {
      departmentId
    }
  }
}

query employeesDepartmentId($ids: [String!]) {
  employees(ids: $ids) {
    employeesEntries {
      employeeId
      department {
        departmentId
      }
    }
  }
}

query activeEmployeeByPhoneNumber($phoneNumber: String!) {
  employee(phone: $phoneNumber) {
    id
    employeeId
    title
    name
    email
    departmentId
    createdDate
    roles {
      source
      typeId
      name
    }
    image {
      small
      medium
    }
  }
}

query employeeByPhoneNumber($phoneNumber: String!) {
  employee(phone: $phoneNumber, all: true) {
    id
    employeeId
    title
    name
    email
    departmentId
    createdDate
    roles {
      source
      typeId
      name
    }
    image {
      small
      medium
    }
  }
}

query employeeBudget($employeeId: String!) {
  employee(employeeId: $employeeId, all: true) {
    salesBudget {
      year
      months {
        month
        sum {
          income
          numberOfSales
        }
      }
    }
  }
}
