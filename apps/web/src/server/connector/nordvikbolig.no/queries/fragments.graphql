fragment EstateListEntryFields on Estate {
  images {
    imageCategoryName
    imageDescription
    imageSequence
    imageId
    url
  }
  noOfBedRooms
  assignmentNum
  estateId
  createdDate
  noOfRooms
  soldDate
  estatePrice
  expireDate
  activities
  finnCode
  finnExpireDate
  finnPublishDate
  commissionAcceptedDate
  assignmentTypeGroup
  publishStart
  matrikkel {
    knr
    gnr
    bnr
    fnr
    snr
  }
  showings
  checklist {
    changedBy
    changedDate
    firstTag
    tags
    value
  }
  brokersIdWithRoles {
    brokerRole
    employee
    employeeId
  }
  id
  address {
    streetAdress
  }
  status
  takeOverDate
  sellers
  sellersEntries {
    contactId
    mainContact
    proxyId
    contact {
      firstName
      lastName
      email
      mobilePhone
      address
      postalCode
      city
      contactType
    }
  }
  ads {
    id
    finnAdType
    ownAdvertisementType
    link
    channel
  }
  hasAd
  changedDate
}
