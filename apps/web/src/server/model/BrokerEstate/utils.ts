import { endOfDay, subDays } from 'date-fns'
import { subMonths } from 'date-fns/subMonths'

import type { GQLQueryEstatesForBrokerByIdArgs } from '@/api/generated-client'
import { CacheKeyUtils } from '@/lib/cache-keys'
import { formatDate } from '@/lib/dates'
import { GQLEstateTabFilter } from '@/server/generated-schema'
import type {
  NordvikNoGQLEmployeesEstatesEntriesByIdQueryVariables,
  NordvikNoGQLEstate,
} from '@/server/nordvik-client'
import { generateUniqId } from '@/utils/generate-uniq-id'

import Estate from './model'
import { ActivityType, NordvikNoActivity } from './types'

export const getKvKey = (
  brokerId: string,
  tabs: GQLEstateTabFilter[],
  search?: string | null,
  args = {},
) => {
  return CacheKeyUtils.generateEstateKey(brokerId, tabs, search, args)
}

export const getActivityTypeName = (
  type: (typeof ActivityType)[keyof typeof ActivityType],
) => {
  switch (type) {
    case ActivityType.Inspection:
      return 'Befaring'
    case ActivityType.Contract:
      return 'Kontraktsmøte'
    case ActivityType.PreInspection:
      return 'Forhåndsbefaring'
    case ActivityType.YearInspection:
      return '1-års befaring'
    case ActivityType.Takeover:
      return 'Overtakelse'
    case ActivityType.TakeoverLog:
      return 'Overtakelsesprotokoll'
    case ActivityType.ProjectMeeting:
      return 'Prosjektmøte'
    case ActivityType.Viewing:
      return 'Visning'
    case ActivityType.PrivateViewing:
      return 'Privatvisning'
    case ActivityType.FollowUp:
      return 'Oppfølging'
    case ActivityType.Photography:
      return 'Fotografering'
    case ActivityType.Appraisal:
      return 'Taksering'
    case ActivityType.MoneyLaunderingControl:
      return 'Hvitvaskingskontroll'
    case ActivityType.InternalControl:
      return 'Internkontroll'
    default:
      return ''
  }
}

const ASSIGNMENT_TYPE_GROUP_DEFAULT = [0, 1, 2, 4, 5, 6, 7, 8, 9, 10]
const ASSIGNMENT_TYPE_GROUP_VALUATION = [3]

export const getParamsByStatus = ({
  tabs,
  ...args
}: GQLQueryEstatesForBrokerByIdArgs): NordvikNoGQLEmployeesEstatesEntriesByIdQueryVariables => {
  const variables = {
    ...args,
    limit: args.limit,
    offset: args.offset,
  } as NordvikNoGQLEmployeesEstatesEntriesByIdQueryVariables

  if (args.brokerId) {
    variables.employeeId = args.brokerId
  }

  if (args.search && args.search.trim().length > 0) {
    variables.search = args.search
    variables.statuses = [0, 1, 2, 3, 4]
    variables.assignmentTypeGroup = [
      ...ASSIGNMENT_TYPE_GROUP_DEFAULT,
      ...ASSIGNMENT_TYPE_GROUP_VALUATION,
    ]
    return variables
  }

  // Innsalg
  if (tabs.includes(GQLEstateTabFilter.Requested)) {
    variables.inspectionDateAfter = formatDate(
      subMonths(new Date(), 6),
      'yyyy-MM-dd',
    )
    variables.sortBy = 'changedDate'
    variables.status = 0
    variables.statuses = [0]
    variables.assignmentTypeGroup = ASSIGNMENT_TYPE_GROUP_DEFAULT
  }

  // Verdivurdering
  if (tabs.includes(GQLEstateTabFilter.Valuation)) {
    variables.createdDateAfter = formatDate(
      subMonths(new Date(), 6),
      'yyyy-MM-dd',
    )

    variables.statuses = [0, 1]
    variables.assignmentTypeGroup = ASSIGNMENT_TYPE_GROUP_VALUATION
  }

  // Klagjøring
  if (tabs.includes(GQLEstateTabFilter.InPreparation)) {
    variables.expireDateAfter = formatDate(new Date(), 'yyyy-MM-dd')
    variables.status = 1
    variables.statuses = [1]
    variables.assignmentTypeGroup = ASSIGNMENT_TYPE_GROUP_DEFAULT
  }

  // Til salgs
  if (tabs.includes(GQLEstateTabFilter.ForSale)) {
    variables.allMarketReady = true
    variables.status = 2
    variables.statuses = [2]
    variables.assignmentTypeGroup = ASSIGNMENT_TYPE_GROUP_DEFAULT
  }

  // Solgt
  if (tabs.includes(GQLEstateTabFilter.Sold)) {
    variables.takeOverDateAfter = formatDate(
      subDays(new Date(), 7),
      'yyyy-MM-dd',
    )
    variables.status = 3
    variables.statuses = [3]
    variables.assignmentTypeGroup = ASSIGNMENT_TYPE_GROUP_DEFAULT
  }

  // Arkivert
  if (tabs.includes(GQLEstateTabFilter.Archived)) {
    variables.archived = true
    variables.status = 4
    variables.statuses = [4]
    variables.assignmentTypeGroup = ASSIGNMENT_TYPE_GROUP_DEFAULT
  }

  return variables
}
export const knownChecklistTags = {
  TILBAKETRUKKET: 'TILBAKETRUKKET',
}

export const appendActivities = async (data: NordvikNoGQLEstate) => {
  const additionalActivities: (NordvikNoActivity & {
    value?: string | number | null
  })[] = []

  if (data.soldDate) {
    additionalActivities.push({
      // Vitec sets the sold date to start of the day. It fails to show activity in correct order.
      start: data.soldDate
        ? endOfDay(new Date(data.soldDate)).toISOString()
        : undefined,
      name: 'Solgt',
      id: generateUniqId(),
      type: ActivityType.Sold,
      value: data.estatePrice?.soldPrice,
    })
  }

  if (data.createdDate) {
    additionalActivities.push({
      start: data.createdDate,
      name: 'Oppdrag opprettet',
      id: generateUniqId(),
      type: ActivityType.Created,
    })
  }
  const estate = new Estate(data)
  const [listingAgreement, inspectionFolder] = await Promise.all([
    estate.listingAgreement(),
    estate.inspectionFolder(),
  ])

  const isSignedThroughNext =
    estate.commissionAcceptedDate && !listingAgreement?.signedAt
  if (isSignedThroughNext) {
    additionalActivities.push({
      start: estate.commissionAcceptedDate,
      name: 'Oppdragsavtale signert via Next',
      id: generateUniqId(),
      type: ActivityType.ListingAgreementSigned,
    })
  }

  if (listingAgreement) {
    if (listingAgreement.initiatedSigningAt) {
      additionalActivities.push({
        start: new Date(listingAgreement.initiatedSigningAt).toISOString(),
        name: 'Oppdragsavtale sendt til signering',
        id: generateUniqId(),
        type: ActivityType.ListingAgreementSentToSigning,
      })
    }

    if (listingAgreement.signedAt) {
      additionalActivities.push({
        start: new Date(listingAgreement.signedAt).toISOString(),
        name: 'Oppdragsavtale signert',
        id: generateUniqId(),
        type: ActivityType.ListingAgreementSigned,
      })
    }

    const sendToClientAt = await listingAgreement.sentToClientAt()

    if (sendToClientAt && !inspectionFolder) {
      additionalActivities.push({
        start: new Date(sendToClientAt).toISOString(),
        name: 'Oppdragsavtale sendt',
        id: generateUniqId(),
        type: ActivityType.ListingAgreementSentToClient,
      })
    }
  }

  const inspectionAudit = await inspectionFolder?.audit()
  if (inspectionAudit?.length) {
    for (const audit of inspectionAudit) {
      additionalActivities.push({
        start: new Date(audit.sentAt).toISOString(),
        name: audit.listingAgreementActive
          ? `Presentasjon og oppdragsavtale sendt`
          : `Presentasjon sendt`,
        id: generateUniqId(),
        type: ActivityType.InspectionSentToClient,
      })
    }
  }

  return additionalActivities
}
