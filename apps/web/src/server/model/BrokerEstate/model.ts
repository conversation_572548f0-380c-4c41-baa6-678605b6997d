import { BrokerRole } from '@befaring/lib/broker-constants'
import { isWithinInterval } from 'date-fns/isWithinInterval'
import isNumber from 'lodash/isNumber'
import orderBy from 'lodash/orderBy'
import remove from 'lodash/remove'
import uniqBy from 'lodash/uniqBy'

import { befaringsForEstate, estateHasBefaring } from '@/actions/befarings'
import { getNextOverviewUrl } from '@/actions/get-next-overview-url'
import { getCompanyContacts } from '@/actions/next/company-contacts'
import { getEstateContactRole } from '@/actions/next/estate-contact-role'
import {
  getEstateDocuments,
  getLatestValuation,
} from '@/actions/next/estate-documents'
import { riskCompletionCheck } from '@/actions/risk-check-completed'
import prisma from '@/db/prisma'
import { HOUR, MINUTE } from '@/db/util'
import retry from '@/lib/retry'
import { Context } from '@/server/context'
import {
  GQLBrokerEstate,
  GQLBrokerEstateCampaign,
  GQLBrokerEstateLink,
  GQLBrokerEstateShowing,
  GQLEstateActivity,
  GQLInspection,
} from '@/server/generated-schema'
import type {
  NordvikNoGQLEstate,
  NordvikNoGQLEstateContact,
} from '@/server/nordvik-client'
import nordvikApi from '@/server/nordvik-client-adaptor'
import { isNotNull } from '@/server/utils'
import { getPlaceholderImage } from '@/utils/getImageBase64'
import { withCache } from '@/utils/with-cache'

import Broker from '../Broker/model'
import BrokerEstateSeller from '../BrokerEstateSeller/model'
import { getContactById, getRawContactById } from '../Contact/factory'
import Contact from '../Contact/model'
import Department from '../Department/model'
import { getFormsByEstateId } from '../Forms/factory'
import InspectionFolder from '../Inspection/model'
import { agreementLoader } from '../ListingAgreement/factory'

import { BrokerEstateAuthorizer } from './authorizer'
import { ActivityType, NordvikBrokerIdWithRoleOnEstate } from './types'
import {
  appendActivities,
  getActivityTypeName,
  knownChecklistTags,
} from './utils'

const ownershipTypes = {
  0: 'Eiet',
  1: 'Andel',
  2: 'Aksje',
  3: 'Obligasjon',
  4: 'Eierseksjon',
} as const

export default class Estate implements GQLBrokerEstate {
  public authorizer = new BrokerEstateAuthorizer(this)

  constructor(
    protected data: NordvikNoGQLEstate,
    protected context?: Context,
  ) {}

  async activities() {
    const baseActivities = this.activitiesWithFallbacks(
      this.data.activities ?? [],
    )
    const additionalActivities = await appendActivities(this.data)
    const activities = [...baseActivities, ...additionalActivities]

    remove(activities, (activity) => {
      switch (activity.type) {
        case ActivityType.FollowUp:
          return true
        default:
          return false
      }
    })

    return this.sanitizeActivities(activities as GQLEstateActivity[])
  }

  get id() {
    return this.data.estateId
  }

  get estateId() {
    return this.data.estateId!
  }

  get createdAt() {
    return this.data.createdDate
  }

  get ownership() {
    if (!isNumber(this.data.ownership)) {
      return undefined
    }

    return this.data.ownership
  }

  get ownershipType() {
    const { ownership } = this.data

    if (
      isNumber(ownership) &&
      Object.keys(ownershipTypes).includes(ownership.toString())
    ) {
      return ownershipTypes[ownership as keyof typeof ownershipTypes]
    }
    return undefined
  }

  get landIdentificationMatrix() {
    const matrikkel = this.data.matrikkel?.at(0) ?? {}
    return {
      snr: matrikkel.snr,
      gnr: matrikkel.gnr,
      bnr: matrikkel.bnr,
      fnr: matrikkel.fnr,
      knr: matrikkel.knr,
      ownPart: matrikkel.ownPart,
    }
  }

  get matrikkel() {
    return (this.data.matrikkel ?? []).map((m) => ({
      gnr: m?.gnr,
      bnr: m?.bnr,
      snr: m?.snr,
      fnr: m?.fnr,
      knr: m?.knr,
      ownPart: m?.ownPart,
    }))
  }

  get estatePriceModel() {
    if (!this.data.estatePriceModel) return undefined
    return {
      ...this.data.estatePriceModel,
      __typename: undefined,
    }
  }

  get estateSizeModel() {
    if (!this.data.estateSizeModel) return undefined
    delete this.data.estateSizeModel.__typename
    return {
      ...this.data.estateSizeModel,
      __typename: undefined,
    }
  }

  async upcomingEvents() {
    const upcoming = this.getUpcomingActivities(await this.activities())
    return this.addTypeNamesToActivities(upcoming)
  }

  get numberOfBedrooms() {
    return this.data.noOfBedRooms ?? 0
  }

  async hasCompanySeller() {
    for (const seller of this.sellers) {
      if (seller.contactType === 1) {
        return true
      }

      if (seller.contactType === null || seller.contactType === undefined) {
        try {
          const contact = await getContactById(seller.contactId)
          if (contact?.contactType === 1) {
            return true
          }
        } catch (error) {
          console.error(error)
        }
      }
    }

    return false
  }

  get status() {
    return this.data.status ?? 0
  }

  get projectRelation() {
    return this.data.projectRelation ?? undefined
  }

  get assignmentNumber() {
    return this.data.assignmentNum ?? undefined
  }

  get assignmentType() {
    return this.data.assignmentType ?? undefined
  }

  get assignmentTypeGroup() {
    return this.data.assignmentTypeGroup ?? undefined
  }

  get isValuation() {
    return this.data.assignmentTypeGroup === 3
  }

  get ownAssignmentType() {
    return this.data.ownAssignmentType
  }

  get heading() {
    return this.data.heading
  }

  get estateType() {
    return this.data.estateType
  }

  get estateTypeId() {
    return this.data.estateTypeId
  }

  get estateTypeExternal() {
    return this.data.estateTypeExternal ?? undefined
  }

  async inspectionDate() {
    if (this.data.inspectionDate) {
      return this.data.inspectionDate
    }

    const inspectionFromActivities = (await this.activities()).find(
      (activity) => activity.type === ActivityType.Inspection,
    )
    return inspectionFromActivities?.start ?? undefined
  }

  get address() {
    const address = this.data.address ?? {}
    const municipality = this.data.municipality ?? undefined

    return {
      apartmentNumber: address.apartmentNumber ?? undefined,
      city: address.city ?? undefined,
      streetAddress: address.streetAdress ?? undefined, // spelling error in API
      zipCode: address.zipCode ?? undefined,
      municipality,
      __typename: 'BrokerAddress',
    } as const
  }

  get checklist() {
    return (
      this.data.checklist?.filter(isNotNull).map((item) => ({
        changedBy: item.changedBy,
        changedDate: item.changedDate ? new Date(item.changedDate) : undefined,
        firstTag: item.firstTag,
        // Not sure how to fix that type. It's a list of strings.
        tags: item.tags ? (item.tags.filter(Boolean) as string[]) : [],
        value: item.value,
      })) ?? []
    )
  }

  get isWithdrawn() {
    const hasBeenWtihdrawn =
      this.data.checklist?.find(
        (item) =>
          item?.tags?.includes(knownChecklistTags.TILBAKETRUKKET) ?? false,
      ) ?? false
    const isOnSaleStatus = this.data.status === 2
    return hasBeenWtihdrawn && isOnSaleStatus
  }

  get noOfBedRooms() {
    return this.data.noOfBedRooms ?? undefined
  }

  get brokerId() {
    return this.data.employeeId
  }

  async brokers() {
    const employees = await Promise.all(
      this.data.brokersIdWithRoles?.map((broker) =>
        withCache(
          `employeeSessionInfo:${broker?.employeeId}`,
          () =>
            nordvikApi.employeeSessionInfo({ employeeId: broker?.employeeId }),
          HOUR,
        ),
      ) ?? [],
    )

    return this.data.brokersIdWithRoles?.map((broker) => {
      const employee = broker?.employee
      const employeeData = employees.find(
        (e) => e.employee?.employeeId === broker?.employeeId,
      )
      return {
        ...employee,
        __typename: 'EstateBroker',
        role: broker?.brokerRole ?? undefined,
        employeeRoles: employeeData?.employee?.roles,
        employeeId: broker?.employeeId ?? employee.employeeId,
      } as const
    })
  }

  get noOfRooms() {
    return this.data.noOfRooms
  }

  get areaSize() {
    return {
      BRAItotal: this.data.areaSize?.BRAItotal ?? undefined,
    }
  }

  get sumArea() {
    return this.data.sumArea
  }

  get estatePrice() {
    return {
      totalPrice: this.data.estatePrice?.totalPrice ?? undefined,
      soldPrice: this.data.estatePrice?.soldPrice ?? undefined,
      priceSuggestion: this.data.estatePrice?.priceSuggestion ?? undefined,
      collectiveDebt: this.data.estatePrice?.collectiveDebt ?? undefined,
    }
  }

  get commissionAcceptedDate() {
    return this.data.commissionAcceptedDate ?? undefined
  }

  get takeOverDate() {
    return this.data.takeOverDate ?? undefined
  }

  get expireDate() {
    return this.data.expireDate ?? undefined
  }

  get soldDate() {
    return this.data.soldDate ?? undefined
  }

  get department() {
    return this.data.department
      ? new Department(this.data.department)
      : undefined
  }

  get departmentId() {
    return this.data.departmentId
  }

  get brokersIdWithRoles() {
    const estateBrokersIdWithRoles = this.data
      .brokersIdWithRoles as NordvikBrokerIdWithRoleOnEstate[]

    if (!estateBrokersIdWithRoles.length) {
      return []
    }

    return estateBrokersIdWithRoles
  }

  async campaigns() {
    const { campaigns } = await nordvikApi.campaigns({
      estateId: this.estateId,
    })

    const filtered = campaigns?.entries?.filter(isNotNull) ?? []
    return filtered as GQLBrokerEstateCampaign[]
  }

  async mainSeller() {
    if (await this.hasCompanySeller()) {
      const contactRoles = await getEstateContactRole(
        this.estateId,
        this.sellers[0].contactId,
      )
      const companyContacts = await this.companyContacts()
      const mainContact = contactRoles
        ? companyContacts.find(
            (contact) => contact.contactId === contactRoles[0]?.childContactId,
          )
        : undefined

      if (!mainContact) {
        return undefined
      }

      return new BrokerEstateSeller(mainContact)
    }

    const mainSeller = this.sellers.find((seller) => seller.mainContact)

    if (!mainSeller) {
      return undefined
    }

    return new BrokerEstateSeller(mainSeller)
  }

  async mainBroker() {
    const brokers = this.brokersIdWithRoles
    const mainBroker = brokers.find(
      (broker) => broker.brokerRole === BrokerRole.Main,
    )
    if (!mainBroker) return undefined

    const employee = this.context?.loaders.employeeLoader
      ? await this.context.loaders.employeeLoader.load(mainBroker.employeeId)
      : await nordvikApi
          .employeeWithRatingAndAwards(
            {
              employeeId: mainBroker.employeeId,
            },
            { cache: 'force-cache' },
          )
          .then((res) => res.employee)

    if (!employee) return undefined
    return new Broker(employee)
  }

  async assistantBroker() {
    const brokers = this.brokersIdWithRoles
    const assistantBroker = brokers.find(
      (broker) => broker.brokerRole === BrokerRole.Assistant,
    )
    if (!assistantBroker) return undefined

    if (this.context?.loaders.employeeLoader) {
      const employee = await this.context.loaders.employeeLoader.load(
        assistantBroker.employeeId,
      )
      if (!employee) return undefined
      return new Broker(employee)
    }

    const data = await nordvikApi.employeeSessionInfo(
      {
        employeeId: assistantBroker.employeeId,
      },
      { cache: 'force-cache' },
    )
    if (!data?.employee) return undefined
    return new Broker(data.employee)
  }

  get sellers(): BrokerEstateSeller[] {
    const sellersEntries = this.data.sellersEntries

    if (!sellersEntries) return []

    // Merge each entry in sellersEntries with its corresponding seller in sellers
    return sellersEntries.map(this.createBrokerEstateSeller.bind(this))
  }

  async companyContacts() {
    if (!this.sellers?.length) return []
    const companyContacts = await getCompanyContacts(this.sellers[0].contactId)

    return (
      companyContacts?.map((contact) => new Contact(contact, this.estateId)) ??
      []
    )
  }

  async extraContacts() {
    const prismaExtraContacts = await prisma.estate_extra_contact.findMany({
      where: { estate_id: this.estateId },
    })

    const promises = prismaExtraContacts.map(async (extraContact) => {
      const contact = await getRawContactById(extraContact.contact_id)
      if (!contact) return undefined
      return new Contact(contact, this.estateId)
    })

    return (await Promise.all(promises)).filter(isNotNull)
  }

  async inspectionFolder() {
    if (this.context?.loaders.inspectionFolderLoader) {
      return this.context?.loaders.inspectionFolderLoader.load(this.estateId)
    }

    const data = await prisma.inspection_folders.findUnique({
      where: { estate_id: this.estateId },
    })
    if (!data) return undefined
    return new InspectionFolder(data, this.context)
  }

  async listingAgreement() {
    return this.context?.loaders.listingAgreementLoader
      ? this.context?.loaders.listingAgreementLoader.load(this.estateId)
      : agreementLoader.load(this.estateId)
  }

  async documents() {
    try {
      return await retry(() => getEstateDocuments(this.estateId))
    } catch (error) {
      console.error(error)
      return []
    }
  }

  get showings() {
    const ordered = orderBy<GQLBrokerEstateShowing>(
      (this.data.showings ?? []) as GQLBrokerEstateShowing[],
      'start',
      'asc',
    )

    return ordered
  }

  get links() {
    const mappedLinks: GQLBrokerEstateLink[] = []

    mappedLinks.push(
      ...(this.data.links
        ?.map((link) => ({
          text: link?.text ?? undefined,
          url: link?.url ?? undefined,
          linkType: link?.linkType ?? undefined,
        }))
        .filter((link) => link.text && link.url) ?? []),
    )

    if (this.estateId && this.status > 0) {
      mappedLinks.push({
        text: 'Se på Nordvik Ekstra',
        url: `/api/adplenty/login?estateId=${this.estateId}`,
      })
    }

    if (this.data.finnCode) {
      mappedLinks.push({
        text: 'Se på Finn',
        url: `https://www.finn.no/realestate/homes/ad.html?finnkode=${this.data.finnCode}`,
      })
    }

    if (this.data.url && this.status === 2) {
      if (this.isPublished || this.projectRelation === 2) {
        mappedLinks.push({
          text: 'Se på nordvikbolig.no',
          url: `https://nordvikbolig.no${this.data.url}`,
        })
      }
    }

    return mappedLinks
  }

  get isPublished() {
    return (
      !!this.data.publishStart &&
      !!this.data.publishEnd &&
      isWithinInterval(new Date(), {
        start: new Date(this.data.publishStart),
        end: new Date(this.data.publishEnd),
      })
    )
  }

  async marketingStart() {
    const inspection = this.context
      ? await this.context?.loaders.inspectionEventLoader.load(this.estateId)
      : await prisma.inspection_event.findFirst({
          where: {
            estate_id: this.estateId,
            event_type: 'marketing',
          },
          orderBy: { start: 'asc' },
        })

    const start = inspection?.start ?? this.data.publishStart

    return start ? new Date(start) : undefined
  }

  get changedDate() {
    return this.data.changedDate
  }

  get finn() {
    return {
      finnCode: this.data.finnCode,
      finnExpireDate: this.data.finnExpireDate,
      finnPublishDate: this.data.finnPublishDate,
    }
  }

  async linkToNext() {
    const link = await getNextOverviewUrl(this.estateId)
    return link ?? ''
  }

  get hjemUrl() {
    return this.data.hjemUrl
  }

  get mainImage() {
    const mainImage = this.data.images?.length ? this.data.images[0] : null

    if (!mainImage) {
      return undefined
    }

    const image = {
      category: mainImage.imageCategoryName ?? '',
      small: mainImage.url.small ?? '',
      medium: mainImage.url.medium ?? '',
      large: mainImage.url.large ?? '',
      description: mainImage.imageDescription ?? '',
      id: mainImage.imageId ?? '',
      sequence: mainImage.imageSequence ?? -1,
    }

    return image
  }

  async placeholderImage() {
    const mainImage = this.data.images?.length ? this.data.images[0] : null

    if (!mainImage) {
      return undefined
    }

    return withCache(
      `estate:${this.estateId}:placeholderImage`,
      () =>
        getPlaceholderImage(
          (mainImage.url.small as string | undefined) ??
            (mainImage.url.medium as string | undefined),
        ),
      MINUTE * 5,
    )
  }

  get broker() {
    if (!this.data.employee) return undefined
    return new Broker(this.data.employee)
  }

  async images() {
    const images = Promise.all(
      this.data.images?.map(async (image, index) => {
        return {
          placeholder:
            index === 0
              ? await getPlaceholderImage(
                  (image?.url.small as string | undefined) ??
                    (image?.url.medium as string | undefined),
                )
              : undefined,
          category: image?.imageCategoryName ?? '',
          small: image?.url.small ?? '',
          medium: image?.url.medium ?? '',
          large: image?.url.large ?? '',
          description: image?.imageDescription ?? '',
          id: image?.imageId ?? '',
          sequence: image?.imageSequence ?? -1,
        }
      }) ?? [],
    )

    return images
  }

  async hasInspection() {
    return estateHasBefaring(this.data.estateId!)
  }

  async inspection() {
    try {
      const inspection = await befaringsForEstate(this.data.estateId!)

      return inspection as GQLInspection
    } catch (error) {
      console.error(error)
      return undefined
    }
  }

  get partOwnership() {
    return this.data.partOwnership
  }

  get businessManagerContact() {
    if (!this.data.businessManagerContact) {
      return undefined
    }

    return {
      ...this.data.businessManagerContact,
      __typename: undefined,
    }
  }

  async forms() {
    if (!this.data.estateId) {
      return []
    }

    return getFormsByEstateId(this.data.estateId, this.data)
  }

  get location() {
    return this.data.location
  }

  get longitude() {
    return this.data.location.coordinates[0]
  }

  get latitude() {
    return this.data.location.coordinates[1]
  }

  get stats() {
    if (!this.data.stats) return undefined
    return {
      interested: this.data.stats.interested ?? 0,
      showings: this.data.stats.showings ?? 0,
      showingsTotal: this.data.stats.showingsTotal ?? 0,
      privateShowingsCount: this.data.stats.privateShowingsCount ?? 0,
      showingRegistrations: this.data.stats.showingRegistrations ?? 0,
      showingRegistrationsTotal: this.data.stats.showingRegistrationsTotal ?? 0,
      showingParticipants: this.data.stats.showingParticipants ?? 0,
      bids: this.data.stats.bids ?? 0,
      bidders: this.data.stats.bidders ?? 0,
      followUp: this.data.stats['Interessent - Budoppfølging'] ?? 0,
    }
  }

  async etakst() {
    const doc = await getLatestValuation(this.estateId)
    if (!doc) return undefined
    return doc
  }

  async riskCheckmark() {
    return riskCompletionCheck(this.estateId).then(
      ({ isComplete }) => isComplete,
    )
  }

  async isEtakstPublished() {
    let hasBeenChecked
    if (this.context?.loaders.etakstCheckLoader) {
      hasBeenChecked = await this.context?.loaders.etakstCheckLoader.load(
        this.estateId,
      )
    } else {
      hasBeenChecked = await prisma.etakst_check_queue.findFirst({
        where: {
          estate_id: this.estateId,
        },
      })
    }

    if (hasBeenChecked) {
      return hasBeenChecked.is_complete
    }

    try {
      // if any of these checks fail, we return false early
      await Promise.all([
        this.listingAgreement().then((oa) => {
          if (!oa?.signedAt) throw new Error('No signed agreement')
        }),
        this.riskCheckmark().then((result) => {
          if (!result) throw new Error('Risk check not completed')
        }),
        this.etakst().then((result) => {
          if (!result) throw new Error('No etakst document')
        }),
      ])

      return true
    } catch {
      return false
    }
  }

  private sanitizeActivities(activities: GQLEstateActivity[]) {
    const filtered = uniqBy(activities, (e) => {
      const type = e.type ?? null
      const name = e.name ?? null
      const start = e.start
        ? new Date(e.start as string).toLocaleString('no-NO', {
            day: 'numeric',
            month: 'numeric',
            year: 'numeric',
            hour: 'numeric',
            minute: 'numeric',
          })
        : null

      if (!type || !name || !start) {
        return e.id
      }

      return `${type}-${name}-${start}`
    })

    return orderBy<GQLEstateActivity>(filtered, 'start', 'desc')
  }

  private getUpcomingActivities(activities: GQLEstateActivity[]) {
    const now = new Date()

    const upcoming = activities.filter((activity) => {
      const start = activity.start ? new Date(activity.start as string) : null

      if (!start || !activity.type) {
        return false
      }

      if (
        start > now &&
        activity.type &&
        [
          ActivityType.Inspection,
          ActivityType.Contract,
          ActivityType.Viewing,
          ActivityType.PrivateViewing,
        ].includes(activity.type as number)
      ) {
        return true
      }
      return false
    })

    return orderBy<GQLEstateActivity>(upcoming, 'start', 'asc')
  }

  private addTypeNamesToActivities(activities: GQLEstateActivity[]) {
    return activities.map((activity) => {
      return {
        ...activity,
        typeName: getActivityTypeName?.(activity.type as number),
      }
    })
  }

  private createBrokerEstateSeller(entry: NordvikNoGQLEstateContact) {
    const { contact = {}, ...rest } = entry
    const seller =
      this.data.sellers.find(({ contactId }) => contactId === rest.contactId) ??
      {}

    return new BrokerEstateSeller({
      ...seller, // Merge properties from corresponding seller if exists
      ...contact, // Include contact details first as it may contain null values
      ...rest, // Overwrite with item from sellersEntries assuming it's newer
      id: rest.contactId!,
      contactId: rest.contactId!,
    })
  }

  private activitiesWithFallbacks(activities: GQLEstateActivity[]) {
    return activities.map((activity) => {
      if (activity.type === ActivityType.Inspection && !activity.name) {
        return {
          ...activity,
          name: 'Befaring',
        }
      }

      return activity
    })
  }
}
