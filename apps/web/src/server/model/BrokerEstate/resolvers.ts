import { syncEstate } from '@/actions/sync/sync-estate'
import { MINUTE } from '@/db/util'
import { CACHE_KEYS } from '@/lib/cache-keys'
import type {
  GQLMutationResolvers,
  GQLQueryResolvers,
} from '@/server/generated-schema'
import nordvikApi from '@/server/nordvik-client-adaptor'
import { withCache } from '@/utils/with-cache'

// import { getCurrentUserOrThrow } from "@/lib/session";
import {
  estatesForBrokerById,
  estatesForBrokerIdCount,
  estatesForDepartment,
  findEstates,
  findEstatesForBroker,
  getEstateById,
  resetForm,
} from './factory'
import Estate from './model'

export function estatesForBrokerIdCountCacheKey(brokerId?: string): string {
  return CACHE_KEYS.ESTATE.FOR_BROKER_COUNT(brokerId)
}

export const Query: GQLQueryResolvers = {
  estates: () => {
    return []
  },
  estate: async (_, args, context) => {
    // const user = await getCurrentUserOrThrow();

    const estate = await getEstateById(args, context)
    // This needs to account for our open pages for customers which are using this query
    // if (!estate?.authorizer.viewerHasAccess({ employeeId: user.employeeId })) {
    //   return undefined;
    // }

    return estate
  },
  estatePriceHistories: () => {
    return []
  },
  estatesForBrokerIdCount: async (_, args) => {
    if (!args.brokerId) {
      return []
    }

    return withCache(
      estatesForBrokerIdCountCacheKey(args.brokerId),
      () => estatesForBrokerIdCount(args),
      MINUTE * 10,
    )
  },
  estatesForBrokerById: (_, args, context) => {
    return estatesForBrokerById(args, context)
  },
  estatesForDepartment: (_, args, ctx) => {
    return estatesForDepartment(args, ctx)
  },
  syncEstateWithVitec: async (_, args) => {
    try {
      const { estate: before } = await nordvikApi.getEstateById({
        estateId: args.estateId,
        statuses: [-1, 0, 1, 2, 3, 4],
      })

      if (!before) {
        return false
      }

      await syncEstate(args.estateId)
      const { estate: after } = await nordvikApi.getEstateById({
        estateId: args.estateId,
        statuses: [-1, 0, 1, 2, 3, 4],
      })
      if (!after) {
        return false
      }
      return before.changedDate !== after.changedDate
    } catch (error) {
      console.error('Error syncing estate with Vitec', error)
      return false
    }
  },
  findEstatesForBroker: async (_, args, context) => {
    const estates = await findEstatesForBroker(args)
    return estates.map((estateData) => new Estate(estateData, context))
  },
  findEstates: async (_, args) => {
    return findEstates(args)
  },
}

export const Mutation: GQLMutationResolvers = {
  resetForm: async (root, args) => {
    return resetForm(args)
  },
}
