import { getEstateDocuments } from '@/actions/next/estate-documents'
import { estateImages } from '@/actions/next/estate-images'
import { DocumentTypeEnum } from '@/actions/next/types-next'
import prisma from '@/db/prisma'
import { EstateFormType, getFormStatus } from '@/utils/forms'

import { ProviderType, RawProviderStatus } from '../types'

import { simulateProvider } from './simulation'

export interface ProviderStrategy {
  fetch(estateId: string): Promise<RawProviderStatus>
}

export class TakeoverProtocolStrategy implements ProviderStrategy {
  async fetch(estateId: string): Promise<RawProviderStatus> {
    const status = await getFormStatus(estateId, EstateFormType.Otp)
    return {
      type: 'TAKEOVER_PROTOCOL',
      state: status?.signingFinished
        ? 'COMPLETE'
        : status?.isNotificationSent
          ? 'STARTED'
          : 'NONE',
      updatedAt: undefined,
    }
  }
}

export class SettlementFormBuyerStrategy implements ProviderStrategy {
  async fetch(estateId: string): Promise<RawProviderStatus> {
    const status = await getFormStatus(estateId, EstateFormType.SettlementBuyer)
    return {
      type: 'SETTLEMENT_FORM_BUYER',
      state: status?.signingFinished
        ? 'COMPLETE'
        : status?.isNotificationSent
          ? 'STARTED'
          : 'NONE',
      updatedAt: undefined,
    }
  }
}

export class SettlementFormSellerStrategy implements ProviderStrategy {
  async fetch(estateId: string): Promise<RawProviderStatus> {
    const status = await getFormStatus(
      estateId,
      EstateFormType.SettlementSeller,
    )
    return {
      type: 'SETTLEMENT_FORM_SELLER',
      state: status?.signingFinished
        ? 'COMPLETE'
        : status?.isNotificationSent
          ? 'STARTED'
          : 'NONE',
      updatedAt: undefined,
    }
  }
}

export class SettlementFormBuyerProjectStrategy implements ProviderStrategy {
  async fetch(estateId: string): Promise<RawProviderStatus> {
    const status = await getFormStatus(
      estateId,
      EstateFormType.SettlementBuyerProject,
    )
    return {
      type: 'SETTLEMENT_FORM_BUYER_PROJECT',
      state: status?.signingFinished
        ? 'COMPLETE'
        : status?.isNotificationSent
          ? 'STARTED'
          : 'NONE',
      updatedAt: undefined,
    }
  }
}

export class SellerInterviewStrategy implements ProviderStrategy {
  async fetch(estateId: string): Promise<RawProviderStatus> {
    return simulateProvider(estateId, 'SELLER_INTERVIEW', 'STARTED')
  }
}

export class SelfDeclarationStrategy implements ProviderStrategy {
  async fetch(estateId: string): Promise<RawProviderStatus> {
    const documents = await getEstateDocuments(estateId, [
      DocumentTypeEnum.SelfDeclaration,
    ])
    return {
      type: 'SELF_DECLARATION',
      state: documents.length > 0 ? 'COMPLETE' : 'NONE',
      updatedAt: documents[0]?.lastChanged ?? undefined,
    }
  }
}

export class EnergyCertificateStrategy implements ProviderStrategy {
  async fetch(estateId: string): Promise<RawProviderStatus> {
    const documents = await getEstateDocuments(estateId, [
      DocumentTypeEnum.EnergyCertificate,
    ])
    return {
      type: 'ENERGY_CERTIFICATE',
      state: documents.length > 0 ? 'COMPLETE' : 'NONE',
      updatedAt: documents[0]?.lastChanged ?? undefined,
    }
  }
}

export class SecurityObligationStrategy implements ProviderStrategy {
  async fetch(estateId: string): Promise<RawProviderStatus> {
    return simulateProvider(estateId, 'SECURITY_OBLIGATION', 'COMPLETE')
  }
}

export class SurveyReportStrategy implements ProviderStrategy {
  async fetch(estateId: string): Promise<RawProviderStatus> {
    const documents = await getEstateDocuments(estateId, [
      DocumentTypeEnum.PropertyValuation,
    ])
    return {
      type: 'SURVEY_REPORT',
      state: documents.length > 0 ? 'COMPLETE' : 'NONE',
      updatedAt: documents[0]?.lastChanged ?? undefined,
    }
  }
}

export class PhotosStrategy implements ProviderStrategy {
  async fetch(estateId: string): Promise<RawProviderStatus> {
    const photos = await estateImages(estateId)
    const lastChangedDate = photos
      ?.map((p) => new Date(p.lastChanged))
      .sort((a, b) => b.getTime() - a.getTime())[0]
      ?.toISOString()

    const photoCount = photos?.length ?? 0

    return {
      type: 'PHOTOS',
      state:
        photoCount > 1 ? 'COMPLETE' : photoCount === 1 ? 'STARTED' : 'NONE',
      updatedAt: lastChangedDate,
    }
  }
}

export class ListingAgreementStrategy implements ProviderStrategy {
  async fetch(estateId: string): Promise<RawProviderStatus> {
    const inspection = await prisma.inspection_folders.findUnique({
      where: { estate_id: estateId },
      select: {
        sent_at: true,
        updated_at: true,
        listing_agreement: {
          select: { signing_finished_at: true, updated_at: true },
        },
      },
    })

    return {
      type: 'LISTING_AGREEMENT',
      state: inspection?.listing_agreement?.signing_finished_at
        ? 'COMPLETE'
        : inspection?.sent_at
          ? 'STARTED'
          : 'NONE',
      updatedAt: (
        inspection?.listing_agreement?.updated_at ?? inspection?.updated_at
      )?.toISOString(),
    }
  }
}

export const providerStrategies: Record<ProviderType, ProviderStrategy> = {
  TAKEOVER_PROTOCOL: new TakeoverProtocolStrategy(),
  SETTLEMENT_FORM_BUYER: new SettlementFormBuyerStrategy(),
  SETTLEMENT_FORM_SELLER: new SettlementFormSellerStrategy(),
  SETTLEMENT_FORM_BUYER_PROJECT: new SettlementFormBuyerProjectStrategy(),
  SELLER_INTERVIEW: new SellerInterviewStrategy(),
  SELF_DECLARATION: new SelfDeclarationStrategy(),
  ENERGY_CERTIFICATE: new EnergyCertificateStrategy(),
  SECURITY_OBLIGATION: new SecurityObligationStrategy(),
  SURVEY_REPORT: new SurveyReportStrategy(),
  PHOTOS: new PhotosStrategy(),
  LISTING_AGREEMENT: new ListingAgreementStrategy(),
}

export function getStrategyOrThrow(type: ProviderType): ProviderStrategy {
  const strategy = providerStrategies[type]
  if (!strategy) throw new Error(`No strategy registered for type ${type}`)
  return strategy
}
