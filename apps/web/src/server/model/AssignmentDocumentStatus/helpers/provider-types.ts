import { GQLEstateTabFilter } from '@/api/generated-client'

import { providers } from './providers'

export const providerTypesForStatus = (key: GQLEstateTabFilter) => {
  return providers
    .filter((p) => !p.enabledFor || p.enabledFor.includes(key))
    .map((p) => ({ type: p.type, name: p.name }))
}

export function providersForStatus(key?: GQLEstateTabFilter) {
  const selected =
    key != null
      ? providers.filter((p) => !p.enabledFor || p.enabledFor.includes(key))
      : providers

  return selected.length > 0 ? selected : providers
}
