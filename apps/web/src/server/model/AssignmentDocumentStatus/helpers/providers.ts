import { GQLEstateTabFilter } from '@/api/generated-client'

import { ProviderDefinition } from '../types'

export const providers: readonly ProviderDefinition[] = [
  // Sold
  {
    type: 'TAKEOVER_PROTOCOL',
    name: 'Overtakelsesprotokoll',
    enabledFor: [GQLEstateTabFilter.Sold],
  },
  {
    type: 'SETTLEMENT_FORM_BUYER',
    name: 'Oppgjø<PERSON>skjema kjøper',
    enabledFor: [GQLEstateTabFilter.Sold],
  },
  {
    type: 'SETTLEMENT_FORM_SELLER',
    name: 'Oppgj<PERSON><PERSON><PERSON><PERSON><PERSON> selger',
    enabledFor: [GQLEstateTabFilter.Sold],
  },
  {
    type: 'SETTLEMENT_FORM_BUYER_PROJECT',
    name: 'Oppgj<PERSON><PERSON><PERSON><PERSON><PERSON> kjøper (prosjekt)',
    enabledFor: [GQLEstateTabFilter.Sold],
  },

  // In preparation
  {
    type: 'SELLER_INTERVIEW',
    name: '<PERSON><PERSON><PERSON> intervju',
    enabledFor: [GQLEstateTabFilter.InPreparation],
  },
  {
    type: 'SELF_DECLARATION',
    name: 'Egenerklæring',
    enabledFor: [GQLEstateTabFilter.InPreparation],
  },
  {
    type: 'ENERGY_CERTIFICATE',
    name: 'Energiattest',
    enabledFor: [GQLEstateTabFilter.InPreparation],
  },
  {
    type: 'SECURITY_OBLIGATION',
    name: 'Sikringsobligasjon',
    enabledFor: [GQLEstateTabFilter.InPreparation],
  },
  {
    type: 'SURVEY_REPORT',
    enabledFor: [GQLEstateTabFilter.InPreparation],
    name: 'Takstrapport',
  },
  {
    type: 'PHOTOS',
    name: 'Foto',
    enabledFor: [GQLEstateTabFilter.InPreparation],
  },

  // Request
  {
    type: 'LISTING_AGREEMENT',
    name: 'Oppdragsavtale',
    enabledFor: [GQLEstateTabFilter.Requested],
  },
]
