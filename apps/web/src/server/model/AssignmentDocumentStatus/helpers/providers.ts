import { GQLEstateTabFilter } from '@/api/generated-client'

import { ProviderDefinition } from '../types'

// Export provider definitions (each provider = one document)
export const providers: readonly ProviderDefinition[] = [
  // Sold
  {
    type: 'TAKEOVER_PROTOCOL',
    name: 'Overtakelsesprotokoll',
    enabledFor: [GQLEstateTabFilter.Sold],
  },
  {
    type: 'SETTLEMENT_FORM_BUYER',
    name: 'Oppg<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kjøper',
    enabledFor: [GQLEstateTabFilter.Sold],
  },
  {
    type: 'SETTLEMENT_FORM_SELLER',
    name: 'Oppg<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> selger',
    enabledFor: [GQLEstateTabFilter.Sold],
  },
  {
    type: 'SETTLEMENT_FORM_BUYER_PROJECT',
    name: 'Oppgjø<PERSON>skje<PERSON> kjøper (prosjekt)',
    enabledFor: [GQLEstateTabFilter.Sold],
  },

  // In preparation
  {
    type: 'SELLER_INTERVIEW',
    name: 'Se<PERSON><PERSON> intervju',
    enabledFor: [GQLEstateTabFilter.InPreparation],
  },
  {
    type: 'SELF_DECLARATION',
    name: 'Egenerklæring',
    enabledFor: [GQLEstateTabFilter.InPreparation],
  },
  {
    type: 'ENERGY_CERTIFICATE',
    name: 'Energiattest',
    enabledFor: [GQLEstateTabFilter.InPreparation],
  },
  {
    type: 'SECURITY_OBLIGATION',
    name: 'Sikringsobligasjon',
    enabledFor: [GQLEstateTabFilter.InPreparation],
  },
  {
    type: 'SURVEY_REPORT',
    enabledFor: [GQLEstateTabFilter.InPreparation],
    name: 'Takstrapport',
  },
  {
    type: 'PHOTOS',
    name: 'Foto',
    enabledFor: [GQLEstateTabFilter.InPreparation],
  },

  // Request
  {
    type: 'LISTING_AGREEMENT',
    name: 'Oppdragsavtale',
    enabledFor: [GQLEstateTabFilter.Requested],
  },
]
