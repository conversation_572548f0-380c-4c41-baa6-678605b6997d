import { getEstateDocuments } from '@/actions/next/estate-documents'
import { DocumentTypeEnum } from '@/actions/next/types-next'
import nordvikApi from '@/server/nordvik-client-adaptor'

import type { DocumentPhase, RawProviderStatus } from '../types'

import type { ProviderStrategy, StrategyFetchContext } from './base'

export class EnergyCertificateStrategy implements ProviderStrategy {
  async fetch(
    estateId: string,
    ctx?: StrategyFetchContext,
  ): Promise<RawProviderStatus> {
    const getState = async () => {
      const documents = ctx?.documentsLoader
        ? await ctx.documentsLoader.load({
            estateId,
            docType: DocumentTypeEnum.EnergyCertificate,
          })
        : await getEstateDocuments(estateId, [
            DocumentTypeEnum.EnergyCertificate,
          ])

      if (documents.length > 0) {
        return {
          state: 'COMPLETE',
          updatedAt: documents[0]?.lastChanged ?? undefined,
        }
      }

      const checklist = ctx?.checklistLoader
        ? await ctx.checklistLoader.load(estateId)
        : (await nordvikApi.estateChecklist({ estateId })).estate?.checklist

      const checklistItem = checklist?.find(
        (c) =>
          (c?.firstTag?.startsWith('S_EES_') ?? false) && Boolean(c?.value),
      )

      if (checklistItem) {
        return {
          state: 'STARTED',
          updatedAt: checklistItem.changedDate ?? undefined,
        }
      }

      return {
        state: 'NONE',
        updatedAt: undefined,
      }
    }

    const state = await getState()

    return {
      type: 'ENERGY_CERTIFICATE',
      state: state?.state as DocumentPhase,
      updatedAt: state?.updatedAt ?? undefined,
    }
  }
}
