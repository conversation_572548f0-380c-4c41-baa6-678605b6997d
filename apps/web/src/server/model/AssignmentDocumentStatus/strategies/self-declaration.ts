import { getEstateDocuments } from '@/actions/next/estate-documents'
import { DocumentTypeEnum } from '@/actions/next/types-next'
import nordvikApi from '@/server/nordvik-client-adaptor'

import type { RawProviderStatus } from '../types'

import type { ProviderStrategy, StrategyFetchContext } from './base'

export class SelfDeclarationStrategy implements ProviderStrategy {
  async fetch(
    estateId: string,
    ctx?: StrategyFetchContext,
  ): Promise<RawProviderStatus> {
    const documents = ctx?.documentsLoader
      ? await ctx.documentsLoader.load({
          estateId,
          docType: DocumentTypeEnum.SelfDeclaration,
        })
      : await getEstateDocuments(estateId, [DocumentTypeEnum.SelfDeclaration])

    if (documents.length > 0) {
      return {
        type: 'SELF_DECLARATION',
        state: 'COMPLETE',
        updatedAt: documents[0]?.lastChanged ?? undefined,
      }
    }

    const checklist = ctx?.checklistLoader
      ? await ctx.checklistLoader.load(estateId)
      : (await nordvikApi.estateChecklist({ estateId })).estate?.checklist

    const checklistItem = checklist?.find(
      (c) =>
        (c?.firstTag?.startsWith('S_KUNDESJEKK') ?? false) && Boolean(c?.value),
    )

    if (checklistItem) {
      return {
        type: 'SELF_DECLARATION',
        state: 'STARTED',
        updatedAt: checklistItem.changedDate ?? undefined,
      }
    }

    return {
      type: 'SELF_DECLARATION',
      state: 'NONE',
      updatedAt: undefined,
    }
  }
}
