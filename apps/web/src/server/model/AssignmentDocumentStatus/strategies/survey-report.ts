import { getEstateDocuments } from '@/actions/next/estate-documents'
import { DocumentTypeEnum } from '@/actions/next/types-next'
import nordvikApi from '@/server/nordvik-client-adaptor'

import type { RawProviderStatus } from '../types'

import type { ProviderStrategy, StrategyFetchContext } from './base'

export const SURVEY_REPORT_TAGS = [
  'S_TILSTANDSR_MANUELL',
  'S_SUPERTAKST_TILSTANDSR',
  'S_IVIT_TILSTANDSR',
]

export class SurveyReportStrategy implements ProviderStrategy {
  async fetch(
    estateId: string,
    ctx?: StrategyFetchContext,
  ): Promise<RawProviderStatus> {
    const documents = ctx?.documentsLoader
      ? await ctx.documentsLoader.load({
          estateId,
          docType: Number(DocumentTypeEnum.PropertyValuation),
        })
      : await getEstateDocuments(estateId, [DocumentTypeEnum.PropertyValuation])

    if (documents.length > 0) {
      return {
        type: 'SURVEY_REPORT',
        state: 'COMPLETE',
        updatedAt: documents[0]?.lastChanged ?? undefined,
      }
    }

    const checklist = ctx?.checklistLoader
      ? await ctx.checklistLoader.load(estateId)
      : (await nordvikApi.estateChecklist({ estateId })).estate?.checklist

    const checklistItem = checklist?.find(
      (c) =>
        Boolean(c?.value) &&
        (c?.firstTag ? SURVEY_REPORT_TAGS.includes(c.firstTag) : false),
    )

    if (checklistItem) {
      return {
        type: 'SURVEY_REPORT',
        state: 'STARTED',
        updatedAt: checklistItem.changedDate ?? undefined,
      }
    }

    return {
      type: 'SURVEY_REPORT',
      state: 'NONE',
      updatedAt: undefined,
    }
  }
}
