import { GQLEstateTabFilter } from '@/api/generated-client'

export type EstateStatusKey = 'inpreparation' | 'sold' | 'request' | 'archived'

// Three phases for a single-document provider
export type DocumentPhase = 'COMPLETE' | 'STARTED' | 'NONE' | 'ERROR'

// Narrow type for provider identifiers used internally
export type ProviderType =
  | 'TAKEOVER_PROTOCOL'
  | 'SETTLEMENT_FORM_BUYER'
  | 'SETTLEMENT_FORM_SELLER'
  | 'SETTLEMENT_FORM_BUYER_PROJECT'
  | 'SELLER_INTERVIEW'
  | 'SELF_DECLARATION'
  | 'ENERGY_CERTIFICATE'
  | 'SECURITY_OBLIGATION'
  | 'SURVEY_REPORT'
  | 'PHOTOS'
  | 'LISTING_AGREEMENT'

export type RawProviderStatus = {
  type: ProviderType
  state: DocumentPhase
  updatedAt?: string
  message?: string
}

export type ProviderDefinition = {
  type: ProviderType
  name: string
  timeoutMs?: number
  // Which estate statuses this provider applies to.
  enabledFor?: GQLEstateTabFilter[]
}

// Simple timeout helper so one slow provider does not block the whole set.
export async function withTimeout<T>(p: Promise<T>, ms: number): Promise<T> {
  let to: NodeJS.Timeout
  try {
    return await Promise.race<Promise<T>>([
      p,
      new Promise<T>((_, reject) => {
        to = setTimeout(() => reject(new Error('timeout')), ms)
      }),
    ])
  } finally {
    clearTimeout(to!)
  }
}
