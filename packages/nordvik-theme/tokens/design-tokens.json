{"font": {"display": {"2xl": {"type": "custom-fontStyle", "value": {"fontSize": 56, "textDecoration": "none", "fontFamily": "Basel Classic", "fontWeight": 400, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 64, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}}, "xl": {"type": "custom-fontStyle", "value": {"fontSize": 48, "textDecoration": "none", "fontFamily": "Basel Classic", "fontWeight": 400, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 58, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}}, "lg": {"type": "custom-fontStyle", "value": {"fontSize": 40, "textDecoration": "none", "fontFamily": "Basel Classic", "fontWeight": 400, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 48, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}}, "md": {"type": "custom-fontStyle", "value": {"fontSize": 32, "textDecoration": "none", "fontFamily": "Basel Classic", "fontWeight": 400, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 40, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}}, "sm": {"type": "custom-fontStyle", "value": {"fontSize": 24, "textDecoration": "none", "fontFamily": "Basel Classic", "fontWeight": 400, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0.24, "lineHeight": 28, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}}, "xs": {"type": "custom-fontStyle", "value": {"fontSize": 20, "textDecoration": "none", "fontFamily": "Basel Classic", "fontWeight": 400, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0.4, "lineHeight": 24, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}}}, "title": {"lg": {"type": "custom-fontStyle", "value": {"fontSize": 32, "textDecoration": "none", "fontFamily": "Basel Grotesk", "fontWeight": 500, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 38, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}}, "md": {"type": "custom-fontStyle", "value": {"fontSize": 24, "textDecoration": "none", "fontFamily": "Basel Grotesk", "fontWeight": 500, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 28, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}}, "sm": {"type": "custom-fontStyle", "value": {"fontSize": 18, "textDecoration": "none", "fontFamily": "Basel Grotesk", "fontWeight": 500, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 24, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}}, "xs": {"type": "custom-fontStyle", "value": {"fontSize": 16, "textDecoration": "none", "fontFamily": "Basel Grotesk", "fontWeight": 500, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 20, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}}}, "body": {"xl": {"regular": {"description": "Used for ingress, larger paragraphs or sentences.", "type": "custom-fontStyle", "value": {"fontSize": 22, "textDecoration": "none", "fontFamily": "Basel Grotesk", "fontWeight": 400, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 34, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}}}, "lg": {"regular": {"type": "custom-fontStyle", "value": {"fontSize": 18, "textDecoration": "none", "fontFamily": "Basel Grotesk", "fontWeight": 400, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 30, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}}, "bold": {"type": "custom-fontStyle", "value": {"fontSize": 18, "textDecoration": "none", "fontFamily": "Basel Grotesk", "fontWeight": 500, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 30, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}}}, "md": {"regular": {"type": "custom-fontStyle", "value": {"fontSize": 16, "textDecoration": "none", "fontFamily": "Basel Grotesk", "fontWeight": 400, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 24, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}}, "bold": {"type": "custom-fontStyle", "value": {"fontSize": 16, "textDecoration": "none", "fontFamily": "Basel Grotesk", "fontWeight": 500, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 24, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}}}, "sm": {"regular": {"type": "custom-fontStyle", "value": {"fontSize": 14, "textDecoration": "none", "fontFamily": "Basel Grotesk", "fontWeight": 400, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 22, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}}, "bold": {"type": "custom-fontStyle", "value": {"fontSize": 14, "textDecoration": "none", "fontFamily": "Basel Grotesk", "fontWeight": 500, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 22, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}}}, "xs": {"regular": {"type": "custom-fontStyle", "value": {"fontSize": 12, "textDecoration": "none", "fontFamily": "Basel Grotesk", "fontWeight": 400, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 18, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}}}}, "label": {"lg": {"type": "custom-fontStyle", "value": {"fontSize": 16, "textDecoration": "none", "fontFamily": "Basel Grotesk", "fontWeight": 500, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 24, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}}, "md": {"type": "custom-fontStyle", "value": {"fontSize": 14, "textDecoration": "none", "fontFamily": "Basel Grotesk", "fontWeight": 500, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 20, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}}, "sm": {"type": "custom-fontStyle", "value": {"fontSize": 12, "textDecoration": "none", "fontFamily": "Basel Grotesk", "fontWeight": 500, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 16, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}}}, "detail": {"md": {"description": "Used for tags and labels", "type": "custom-fontStyle", "value": {"fontSize": 12, "textDecoration": "none", "fontFamily": "Basel Grotesk", "fontWeight": 500, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0.2, "lineHeight": 12, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "uppercase"}}, "sm": {"description": "Used for tags and labels", "type": "custom-fontStyle", "value": {"fontSize": 10, "textDecoration": "none", "fontFamily": "Basel Grotesk", "fontWeight": 500, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0.2, "lineHeight": 10, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "uppercase"}}}}, "primitives": {"teal": {"100": {"type": "color", "value": "#eef1f2ff", "blendMode": "normal"}, "200": {"description": "Brand color", "type": "color", "value": "#dee5e7ff", "blendMode": "normal"}, "300": {"type": "color", "value": "#ccd7d8ff", "blendMode": "normal"}, "400": {"type": "color", "value": "#acc9cdff", "blendMode": "normal"}, "500": {"type": "color", "value": "#5a8c91ff", "blendMode": "normal"}, "600": {"type": "color", "value": "#336669ff", "blendMode": "normal"}, "700": {"type": "color", "value": "#155356ff", "blendMode": "normal"}, "800": {"type": "color", "value": "#193d41ff", "blendMode": "normal"}, "900": {"type": "color", "value": "#002d32ff", "blendMode": "normal"}, "950": {"type": "color", "value": "#00262aff", "blendMode": "normal"}}, "gold": {"100": {"type": "color", "value": "#f8f2eaff", "blendMode": "normal"}, "200": {"type": "color", "value": "#f3e9dbff", "blendMode": "normal"}, "300": {"type": "color", "value": "#e6d8c3ff", "blendMode": "normal"}, "400": {"type": "color", "value": "#d6ba94ff", "blendMode": "normal"}, "500": {"type": "color", "value": "#d7b180ff", "blendMode": "normal"}, "600": {"type": "color", "value": "#a58862ff", "blendMode": "normal"}, "700": {"type": "color", "value": "#7e6748ff", "blendMode": "normal"}, "800": {"type": "color", "value": "#6a533eff", "blendMode": "normal"}, "900": {"type": "color", "value": "#3c2f27ff", "blendMode": "normal"}}, "red": {"100": {"type": "color", "value": "#fff6f6ff", "blendMode": "normal"}, "200": {"type": "color", "value": "#f9eae9ff", "blendMode": "normal"}, "300": {"type": "color", "value": "#f6cfcaff", "blendMode": "normal"}, "400": {"type": "color", "value": "#fd817aff", "blendMode": "normal"}, "500": {"type": "color", "value": "#ef4d2fff", "blendMode": "normal"}, "600": {"type": "color", "value": "#cc0e15ff", "blendMode": "normal"}, "700": {"type": "color", "value": "#a41015ff", "blendMode": "normal"}, "800": {"type": "color", "value": "#881418ff", "blendMode": "normal"}, "900": {"type": "color", "value": "#4a0508ff", "blendMode": "normal"}}, "gray": {"100": {"type": "color", "value": "#f4f5f6ff", "blendMode": "normal"}, "200": {"type": "color", "value": "#e6e7eaff", "blendMode": "normal"}, "300": {"type": "color", "value": "#caccceff", "blendMode": "normal"}, "400": {"type": "color", "value": "#b0b3baff", "blendMode": "normal"}, "500": {"type": "color", "value": "#9298a0ff", "blendMode": "normal"}, "600": {"type": "color", "value": "#68707cff", "blendMode": "normal"}, "700": {"type": "color", "value": "#4b5563ff", "blendMode": "normal"}, "800": {"type": "color", "value": "#2f363fff", "blendMode": "normal"}, "900": {"type": "color", "value": "#141e29ff", "blendMode": "normal"}, "white": {"type": "color", "value": "#ffffffff", "blendMode": "normal"}}, "green": {"100": {"type": "color", "value": "#edfcf3ff", "blendMode": "normal"}, "200": {"type": "color", "value": "#d3f8e0ff", "blendMode": "normal"}, "300": {"type": "color", "value": "#abefc7ff", "blendMode": "normal"}, "400": {"type": "color", "value": "#7ee2<PERSON>ff", "blendMode": "normal"}, "500": {"type": "color", "value": "#37ca81ff", "blendMode": "normal"}, "600": {"type": "color", "value": "#18b16aff", "blendMode": "normal"}, "700": {"type": "color", "value": "#0c8f55ff", "blendMode": "normal"}, "800": {"type": "color", "value": "#1d7c4dff", "blendMode": "normal"}, "900": {"type": "color", "value": "#0a5b39ff", "blendMode": "normal"}}, "blue": {"100": {"type": "color", "value": "#f1f6fdff", "blendMode": "normal"}, "200": {"type": "color", "value": "#d7e8ffff", "blendMode": "normal"}, "300": {"type": "color", "value": "#a1c4efff", "blendMode": "normal"}, "400": {"type": "color", "value": "#74a5e6ff", "blendMode": "normal"}, "500": {"type": "color", "value": "#5485ddff", "blendMode": "normal"}, "600": {"type": "color", "value": "#3f6ad1ff", "blendMode": "normal"}, "700": {"type": "color", "value": "#31489cff", "blendMode": "normal"}, "800": {"type": "color", "value": "#2c3f7cff", "blendMode": "normal"}, "900": {"type": "color", "value": "#1f284cff", "blendMode": "normal"}}, "petrol": {"50": {"type": "color", "value": "#f6f8f9ff", "blendMode": "normal"}, "100": {"type": "color", "value": "#eef1f2ff", "blendMode": "normal"}, "200": {"type": "color", "value": "#e1e6e6ff", "blendMode": "normal"}, "300": {"type": "color", "value": "#ccd5d6ff", "blendMode": "normal"}, "400": {"type": "color", "value": "#99abadff", "blendMode": "normal"}, "500": {"type": "color", "value": "#668184ff", "blendMode": "normal"}, "600": {"type": "color", "value": "#47686bff", "blendMode": "normal"}, "700": {"type": "color", "value": "#33575bff", "blendMode": "normal"}, "800": {"type": "color", "value": "#1f464bff", "blendMode": "normal"}, "900": {"type": "color", "value": "#0f3a3eff", "blendMode": "normal"}, "950": {"type": "color", "value": "#0d3539ff", "blendMode": "normal"}}, "yellow": {"100": {"type": "color", "value": "#fefbeaff", "blendMode": "normal"}, "200": {"type": "color", "value": "#fbeea0ff", "blendMode": "normal"}, "300": {"type": "color", "value": "#f9e382ff", "blendMode": "normal"}, "400": {"type": "color", "value": "#f2c747ff", "blendMode": "normal"}, "500": {"type": "color", "value": "#e0ac3cff", "blendMode": "normal"}, "600": {"type": "color", "value": "#c0882fff", "blendMode": "normal"}, "700": {"type": "color", "value": "#985f22ff", "blendMode": "normal"}, "800": {"type": "color", "value": "#6a3e1cff", "blendMode": "normal"}, "900": {"type": "color", "value": "#4f2e14ff", "blendMode": "normal"}}}, "color system": {"light": {"ink": {"neutral": {"default": {"description": "", "type": "color", "value": "{primitives.gray.900}"}, "subtle": {"description": "", "type": "color", "value": "{primitives.gray.700}"}, "disabled": {"description": "", "type": "color", "value": "{primitives.gray.500}"}, "muted": {"description": "", "type": "color", "value": "{primitives.gray.600}"}}, "brand": {"default": {"description": "", "type": "color", "value": "{primitives.teal.900}"}, "muted": {"description": "", "type": "color", "value": "{primitives.teal.700}"}}, "gold": {"default": {"description": "", "type": "color", "value": "{primitives.gold.500}"}, "muted": {"description": "", "type": "color", "value": "{primitives.gold.600}"}}, "danger": {"default": {"description": "", "type": "color", "value": "{primitives.red.700}"}, "muted": {"description": "", "type": "color", "value": "{primitives.red.500}"}}, "success": {"default": {"description": "", "type": "color", "value": "{primitives.green.800}"}}, "on": {"gold": {"default": {"description": "", "type": "color", "value": "{primitives.gold.900}"}, "emphasis": {"description": "", "type": "color", "value": "{primitives.gold.700}"}, "muted": {"description": "", "type": "color", "value": "{primitives.gray.600}"}}, "success": {"default": {"description": "", "type": "color", "value": "{primitives.teal.800}"}, "strong": {"description": "", "type": "color", "value": "{primitives.green.800}"}, "emphasis": {"description": "", "type": "color", "value": "{primitives.green.900}"}, "muted": {"description": "", "type": "color", "value": "{primitives.gray.600}"}}, "danger": {"bold": {"default": {"description": "", "type": "color", "value": "{primitives.gray.white}"}}, "subtle": {"emphasis": {"description": "", "type": "color", "value": "{primitives.red.700}"}, "default": {"description": "", "type": "color", "value": "{primitives.red.900}"}, "strong": {"description": "", "type": "color", "value": "{primitives.red.600}"}, "muted": {"description": "", "type": "color", "value": "{primitives.gray.600}"}}}, "info": {"default": {"description": "", "type": "color", "value": "{primitives.blue.900}"}, "emphasis": {"description": "", "type": "color", "value": "{primitives.blue.700}"}, "muted": {"description": "", "type": "color", "value": "{primitives.gray.600}"}}, "gray": {"subtle": {"description": "", "type": "color", "value": "{primitives.gray.700}"}, "muted": {"description": "", "type": "color", "value": "{primitives.gray.300}"}, "default": {"description": "", "type": "color", "value": "{primitives.gray.900}"}}, "brand": {"default": {"description": "", "type": "color", "value": "{primitives.gray.white}"}}, "light green": {"default": {"description": "", "type": "color", "value": "{primitives.teal.800}"}, "subtle": {"description": "", "type": "color", "value": "{primitives.teal.700}"}}, "yellow": {"default": {"description": "", "type": "color", "value": "{primitives.yellow.900}"}, "subtle": {"description": "", "type": "color", "value": "{primitives.yellow.700}"}}}}, "background": {"root": {"default": {"description": "", "type": "color", "value": "{primitives.gray.white}"}, "muted": {"description": "", "type": "color", "value": "{primitives.teal.100}"}}, "overlay": {"description": "Used as backgound on madals", "type": "color", "value": "#07232840", "blendMode": "normal"}}, "fill": {"light green": {"subtle": {"description": "", "type": "color", "value": "{primitives.teal.200}"}, "muted": {"description": "", "type": "color", "value": "{primitives.teal.100}"}}, "gold": {"subtle": {"description": "", "type": "color", "value": "{primitives.gold.300}"}, "emphasis": {"description": "", "type": "color", "value": "{primitives.gold.500}"}, "muted": {"description": "", "type": "color", "value": "{primitives.gold.100}"}}, "danger": {"subtle": {"description": "", "type": "color", "value": "{primitives.red.200}"}, "bold": {"description": "", "type": "color", "value": "{primitives.red.600}"}}, "interactive": {"muted": {"description": "", "type": "color", "value": "{primitives.petrol.100}"}, "subtle": {"description": "", "type": "color", "value": "{primitives.petrol.200}"}, "emphasis": {"description": "", "type": "color", "value": "{primitives.petrol.300}"}, "top": {"description": "", "type": "color", "value": "{primitives.gray.white}"}, "list hover": {"description": "", "type": "color", "value": "{primitives.petrol.50}"}}, "gray": {"subtle": {"description": "", "type": "color", "value": "{primitives.gray.200}"}, "muted": {"description": "", "type": "color", "value": "{primitives.gray.100}"}}, "brand": {"emphasis": {"description": "", "type": "color", "value": "{primitives.teal.600}"}, "subtle": {"description": "", "type": "color", "value": "{primitives.teal.700}"}, "muted": {"description": "", "type": "color", "value": "{primitives.teal.100}"}}, "info": {"subtle": {"description": "", "type": "color", "value": "{primitives.blue.200}"}, "muted": {"description": "", "type": "color", "value": "{primitives.blue.100}"}}, "success": {"subtle": {"description": "", "type": "color", "value": "{primitives.green.200}"}, "muted": {"description": "", "type": "color", "value": "{primitives.green.100}"}, "bold": {"description": "", "type": "color", "value": "{primitives.green.700}"}}, "float": {"default": {"description": "", "type": "color", "value": "{primitives.gray.white}"}}, "yellow": {"subtle": {"description": "", "type": "color", "value": "{primitives.yellow.200}"}, "muted": {"description": "", "type": "color", "value": "{primitives.yellow.100}"}}}, "stroke": {"neutral": {"muted": {"description": "<PERSON>ne stoken skal bare brukes når bla bla", "type": "color", "value": "{primitives.petrol.200}"}, "subtle": {"description": "", "type": "color", "value": "{primitives.petrol.300}"}, "disabled": {"description": "", "type": "color", "value": "{primitives.gray.300}"}, "active": {"description": "", "type": "color", "value": "{primitives.teal.700}"}, "emphasis": {"description": "", "type": "color", "value": "{primitives.petrol.400}"}, "strong": {"description": "", "type": "color", "value": "{primitives.petrol.500}"}}, "danger": {"emphasis": {"description": "", "type": "color", "value": "{primitives.red.700}"}, "subtle": {"description": "", "type": "color", "value": "{primitives.red.500}"}, "muted": {"description": "", "type": "color", "value": "{primitives.red.300}"}}, "gold": {"emphasis": {"description": "", "type": "color", "value": "{primitives.gold.500}"}, "muted": {"description": "", "type": "color", "value": "{primitives.gold.300}"}}, "success": {"emphasis": {"description": "", "type": "color", "value": "{primitives.green.800}"}, "muted": {"description": "", "type": "color", "value": "{primitives.green.300}"}}, "focus": {"description": "", "type": "color", "value": "{primitives.teal.900}"}, "yellow": {"subtle": {"description": "", "type": "color", "value": "{primitives.yellow.500}"}, "muted": {"description": "", "type": "color", "value": "{primitives.yellow.300}"}}}, "button": {"primary": {"fill": {"default": {"description": "", "type": "color", "value": "{primitives.teal.700}"}, "hover": {"description": "", "type": "color", "value": "{primitives.teal.600}"}, "disabled": {"description": "", "type": "color", "value": "{primitives.teal.200}"}}, "ink": {"default": {"description": "", "type": "color", "value": "{primitives.gray.white}"}, "disabled": {"description": "", "type": "color", "value": "{primitives.gray.500}"}}}, "secondary": {"border": {"default": {"description": "", "type": "color", "value": "{primitives.petrol.400}"}, "hover": {"description": "", "type": "color", "value": "{primitives.petrol.500}"}, "disabled": {"description": "", "type": "color", "value": "{primitives.gray.300}"}}, "ink": {"default": {"description": "", "type": "color", "value": "{primitives.teal.900}"}, "disabled": {"description": "", "type": "color", "value": "{primitives.gray.500}"}}}, "tertiary": {"fill": {"default": {"description": "", "type": "color", "value": "{primitives.petrol.200}"}, "hover": {"description": "", "type": "color", "value": "{primitives.petrol.100}"}, "disabled": {"description": "", "type": "color", "value": "{primitives.teal.200}"}}, "ink": {"default": {"description": "", "type": "color", "value": "{primitives.teal.900}"}, "disabled": {"description": "", "type": "color", "value": "{primitives.gray.500}"}}}, "ghost": {"fill": {"hover": {"description": "", "type": "color", "value": "{primitives.petrol.100}"}}, "ink": {"default": {"description": "", "type": "color", "value": "{primitives.teal.900}"}, "disabled": {"description": "", "type": "color", "value": "{primitives.gray.500}"}, "subtle": {"description": "", "type": "color", "value": "{primitives.petrol.600}"}}}, "ink-disabled 2": {"description": "", "type": "color", "value": "{primitives.gray.500}"}, "gold": {"fill": {"default": {"description": "", "type": "color", "value": "{primitives.gold.500}"}, "hover": {"description": "", "type": "color", "value": "{primitives.gold.400}"}, "disabled": {"description": "", "type": "color", "value": "{primitives.teal.200}"}}, "ink": {"default": {"description": "", "type": "color", "value": "{primitives.gold.900}"}, "disabled": {"description": "", "type": "color", "value": "{primitives.gray.500}"}}}, "danger": {"fill": {"default": {"description": "", "type": "color", "value": "{primitives.red.700}"}, "hover": {"description": "", "type": "color", "value": "{primitives.red.600}"}, "disabled": {"description": "", "type": "color", "value": "{primitives.teal.200}"}}, "ink": {"default": {"description": "", "type": "color", "value": "{primitives.gray.white}"}, "disabled": {"description": "", "type": "color", "value": "{primitives.gray.500}"}}}}, "textbutton": {"ink": {"default": {"description": "", "type": "color", "value": "{primitives.teal.700}"}, "hover": {"description": "", "type": "color", "value": "{primitives.teal.900}"}}, "stroke": {"default": {"description": "", "type": "color", "value": "{primitives.petrol.300}"}, "hover": {"description": "", "type": "color", "value": "{primitives.petrol.400}"}}}, "inputs": {"border": {"default": {"description": "", "type": "color", "value": "{primitives.gray.300}"}, "hover": {"description": "", "type": "color", "value": "{primitives.gray.500}"}, "disabled": {"description": "", "type": "color", "value": "{primitives.gray.300}"}, "active": {"description": "", "type": "color", "value": "{primitives.petrol.700}"}}, "fill": {"default": {"description": "", "type": "color", "value": "{primitives.petrol.100}"}, "hover": {"description": "", "type": "color", "value": "{primitives.petrol.200}"}, "disabled": {"description": "", "type": "color", "value": "{primitives.gray.100}"}, "active": {"description": "", "type": "color", "value": "{primitives.petrol.100}"}, "active-selected": {"description": "", "type": "color", "value": "{primitives.teal.700}"}}}, "chart": {"fill": {"subtle-background": {"description": "", "type": "color", "value": "{primitives.teal.300}"}, "brand-strong": {"description": "", "type": "color", "value": "{primitives.teal.700}"}, "brand-stronger": {"description": "", "type": "color", "value": "{primitives.teal.600}"}, "gold": {"description": "", "type": "color", "value": "{primitives.gold.500}"}, "muted-background": {"description": "", "type": "color", "value": "{primitives.teal.200}"}}, "stroke": {"strong": {"description": "", "type": "color", "value": "{primitives.petrol.600}"}, "subtle": {"description": "", "type": "color", "value": "{primitives.teal.300}"}, "gold": {"description": "", "type": "color", "value": "{primitives.gold.500}"}}, "ink": {"on": {"brand": {"description": "", "type": "color", "value": "{primitives.gray.white}"}}, "default": {"description": "", "type": "color", "value": "{primitives.gray.900}"}, "green-subtle": {"description": "", "type": "color", "value": "{primitives.teal.300}"}}}, "calendar": {"fill": {"date": {"hover": {"description": "", "type": "color", "value": "{primitives.petrol.200}"}, "active": {"description": "", "type": "color", "value": "{primitives.teal.900}"}, "period": {"description": "", "type": "color", "value": "{primitives.petrol.100}"}}}, "ink": {"default": {"description": "", "type": "color", "value": "{primitives.gray.900}"}, "disbaled": {"description": "", "type": "color", "value": "{primitives.petrol.500}"}, "contrast": {"description": "", "type": "color", "value": "{primitives.gray.white}"}}}, "toggle": {"background-off": {"description": "", "type": "color", "value": "{primitives.petrol.200}"}, "background-on": {"description": "", "type": "color", "value": "{primitives.teal.700}"}, "handle-on": {"description": "", "type": "color", "value": "{primitives.gray.white}"}, "handle-off": {"description": "", "type": "color", "value": "{primitives.gray.white}"}, "handle-on-success": {"description": "", "type": "color", "value": "{primitives.green.200}"}, "icon-on-handle": {"description": "", "type": "color", "value": "{primitives.teal.900}"}}}, "dark": {"ink": {"neutral": {"default": {"description": "", "type": "color", "value": "{primitives.gray.white}"}, "subtle": {"description": "", "type": "color", "value": "{primitives.petrol.300}"}, "disabled": {"description": "", "type": "color", "value": "{primitives.petrol.500}"}, "muted": {"description": "", "type": "color", "value": "{primitives.petrol.400}"}}, "brand": {"default": {"description": "", "type": "color", "value": "{primitives.teal.100}"}, "muted": {"description": "", "type": "color", "value": "{primitives.teal.400}"}}, "gold": {"default": {"description": "", "type": "color", "value": "{primitives.gold.500}"}, "muted": {"description": "", "type": "color", "value": "{primitives.gold.600}"}}, "danger": {"default": {"description": "", "type": "color", "value": "{primitives.red.400}"}, "muted": {"description": "", "type": "color", "value": "{primitives.red.500}"}}, "success": {"default": {"description": "", "type": "color", "value": "{primitives.green.600}"}}, "on": {"gold": {"default": {"description": "", "type": "color", "value": "{primitives.gold.900}"}, "emphasis": {"description": "", "type": "color", "value": "{primitives.gold.700}"}, "muted": {"description": "", "type": "color", "value": "{primitives.gray.600}"}}, "success": {"default": {"description": "", "type": "color", "value": "{primitives.teal.800}"}, "strong": {"description": "", "type": "color", "value": "{primitives.green.800}"}, "emphasis": {"description": "", "type": "color", "value": "{primitives.green.900}"}, "muted": {"description": "", "type": "color", "value": "{primitives.gray.600}"}}, "danger": {"bold": {"default": {"description": "", "type": "color", "value": "{primitives.gray.white}"}}, "subtle": {"emphasis": {"description": "", "type": "color", "value": "{primitives.red.700}"}, "default": {"description": "", "type": "color", "value": "{primitives.red.900}"}, "strong": {"description": "", "type": "color", "value": "{primitives.red.600}"}, "muted": {"description": "", "type": "color", "value": "{primitives.gray.600}"}}}, "info": {"default": {"description": "", "type": "color", "value": "{primitives.blue.900}"}, "emphasis": {"description": "", "type": "color", "value": "{primitives.blue.800}"}, "muted": {"description": "", "type": "color", "value": "{primitives.gray.600}"}}, "gray": {"subtle": {"description": "", "type": "color", "value": "{primitives.gray.700}"}, "muted": {"description": "", "type": "color", "value": "{primitives.gray.300}"}, "default": {"description": "", "type": "color", "value": "{primitives.gray.900}"}}, "brand": {"default": {"description": "", "type": "color", "value": "{primitives.gray.white}"}}, "light green": {"default": {"description": "", "type": "color", "value": "{primitives.teal.800}"}, "subtle": {"description": "", "type": "color", "value": "{primitives.teal.700}"}}, "yellow": {"default": {"description": "", "type": "color", "value": "{primitives.yellow.900}"}, "subtle": {"description": "", "type": "color", "value": "{primitives.yellow.700}"}}}}, "background": {"root": {"default": {"description": "", "type": "color", "value": "{primitives.teal.900}"}, "muted": {"description": "", "type": "color", "value": "{primitives.teal.800}"}}, "overlay": {"description": "Used as backgound on madals", "type": "color", "value": "#04131680", "blendMode": "normal"}}, "fill": {"light green": {"subtle": {"description": "", "type": "color", "value": "{primitives.teal.200}"}, "muted": {"description": "", "type": "color", "value": "{primitives.teal.100}"}}, "gold": {"subtle": {"description": "", "type": "color", "value": "{primitives.gold.300}"}, "emphasis": {"description": "", "type": "color", "value": "{primitives.gold.500}"}, "muted": {"description": "", "type": "color", "value": "{primitives.gold.100}"}}, "danger": {"subtle": {"description": "", "type": "color", "value": "{primitives.red.200}"}, "bold": {"description": "", "type": "color", "value": "{primitives.red.600}"}}, "interactive": {"muted": {"description": "", "type": "color", "value": "{primitives.petrol.800}"}, "subtle": {"description": "", "type": "color", "value": "{primitives.petrol.700}"}, "emphasis": {"description": "", "type": "color", "value": "{primitives.petrol.600}"}, "top": {"description": "", "type": "color", "value": "{primitives.petrol.900}"}, "list hover": {"description": "", "type": "color", "value": "{primitives.petrol.800}"}}, "gray": {"subtle": {"description": "", "type": "color", "value": "{primitives.gray.200}"}, "muted": {"description": "", "type": "color", "value": "{primitives.gray.100}"}}, "brand": {"emphasis": {"description": "", "type": "color", "value": "{primitives.teal.700}"}, "subtle": {"description": "", "type": "color", "value": "{primitives.teal.600}"}, "muted": {"description": "", "type": "color", "value": "{primitives.teal.800}"}}, "info": {"subtle": {"description": "", "type": "color", "value": "{primitives.blue.200}"}, "muted": {"description": "", "type": "color", "value": "{primitives.blue.100}"}}, "success": {"subtle": {"description": "", "type": "color", "value": "{primitives.green.200}"}, "muted": {"description": "", "type": "color", "value": "{primitives.green.100}"}, "bold": {"description": "", "type": "color", "value": "{primitives.green.600}"}}, "float": {"default": {"description": "", "type": "color", "value": "{primitives.petrol.950}"}}, "yellow": {"subtle": {"description": "", "type": "color", "value": "{primitives.yellow.200}"}, "muted": {"description": "", "type": "color", "value": "{primitives.yellow.100}"}}}, "stroke": {"neutral": {"muted": {"description": "<PERSON>ne stoken skal bare brukes når bla bla", "type": "color", "value": "{primitives.petrol.800}"}, "subtle": {"description": "", "type": "color", "value": "{primitives.petrol.700}"}, "disabled": {"description": "", "type": "color", "value": "{primitives.petrol.600}"}, "active": {"description": "", "type": "color", "value": "{primitives.gray.white}"}, "emphasis": {"description": "", "type": "color", "value": "{primitives.petrol.600}"}, "strong": {"description": "", "type": "color", "value": "{primitives.petrol.500}"}}, "danger": {"emphasis": {"description": "", "type": "color", "value": "{primitives.red.500}"}, "subtle": {"description": "", "type": "color", "value": "{primitives.red.700}"}, "muted": {"description": "", "type": "color", "value": "{primitives.red.300}"}}, "gold": {"emphasis": {"description": "", "type": "color", "value": "{primitives.gold.500}"}, "muted": {"description": "", "type": "color", "value": "{primitives.gold.300}"}}, "success": {"emphasis": {"description": "", "type": "color", "value": "{primitives.green.600}"}, "muted": {"description": "", "type": "color", "value": "{primitives.green.300}"}}, "focus": {"description": "", "type": "color", "value": "{primitives.gray.white}"}, "yellow": {"subtle": {"description": "", "type": "color", "value": "{primitives.yellow.500}"}, "muted": {"description": "", "type": "color", "value": "{primitives.yellow.300}"}}}, "button": {"primary": {"fill": {"default": {"description": "", "type": "color", "value": "{primitives.teal.700}"}, "hover": {"description": "", "type": "color", "value": "{primitives.teal.600}"}, "disabled": {"description": "", "type": "color", "value": "{primitives.petrol.800}"}}, "ink": {"default": {"description": "", "type": "color", "value": "{primitives.gray.white}"}, "disabled": {"description": "", "type": "color", "value": "{primitives.petrol.400}"}}}, "secondary": {"border": {"default": {"description": "", "type": "color", "value": "{primitives.petrol.500}"}, "hover": {"description": "", "type": "color", "value": "{primitives.petrol.400}"}, "disabled": {"description": "", "type": "color", "value": "{primitives.petrol.700}"}}, "ink": {"default": {"description": "", "type": "color", "value": "{primitives.gray.white}"}, "disabled": {"description": "", "type": "color", "value": "{primitives.petrol.400}"}}}, "tertiary": {"fill": {"default": {"description": "", "type": "color", "value": "{primitives.petrol.800}"}, "hover": {"description": "", "type": "color", "value": "{primitives.petrol.700}"}, "disabled": {"description": "", "type": "color", "value": "{primitives.petrol.800}"}}, "ink": {"default": {"description": "", "type": "color", "value": "{primitives.gray.white}"}, "disabled": {"description": "", "type": "color", "value": "{primitives.petrol.400}"}}}, "ghost": {"fill": {"hover": {"description": "", "type": "color", "value": "{primitives.petrol.800}"}}, "ink": {"default": {"description": "", "type": "color", "value": "{primitives.gray.white}"}, "disabled": {"description": "", "type": "color", "value": "{primitives.petrol.500}"}, "subtle": {"description": "", "type": "color", "value": "{primitives.petrol.400}"}}}, "ink-disabled 2": {"description": "", "type": "color", "value": "{primitives.petrol.400}"}, "gold": {"fill": {"default": {"description": "", "type": "color", "value": "{primitives.gold.500}"}, "hover": {"description": "", "type": "color", "value": "{primitives.gold.400}"}, "disabled": {"description": "", "type": "color", "value": "{primitives.petrol.800}"}}, "ink": {"default": {"description": "", "type": "color", "value": "{primitives.gold.900}"}, "disabled": {"description": "", "type": "color", "value": "{primitives.petrol.400}"}}}, "danger": {"fill": {"default": {"description": "", "type": "color", "value": "{primitives.red.600}"}, "hover": {"description": "", "type": "color", "value": "{primitives.red.700}"}, "disabled": {"description": "", "type": "color", "value": "{primitives.petrol.800}"}}, "ink": {"default": {"description": "", "type": "color", "value": "{primitives.gray.white}"}, "disabled": {"description": "", "type": "color", "value": "{primitives.petrol.400}"}}}}, "textbutton": {"ink": {"default": {"description": "", "type": "color", "value": "{primitives.petrol.300}"}, "hover": {"description": "", "type": "color", "value": "{primitives.gray.white}"}}, "stroke": {"default": {"description": "", "type": "color", "value": "{primitives.petrol.600}"}, "hover": {"description": "", "type": "color", "value": "{primitives.petrol.500}"}}}, "inputs": {"border": {"default": {"description": "", "type": "color", "value": "{primitives.petrol.500}"}, "hover": {"description": "", "type": "color", "value": "{primitives.petrol.400}"}, "disabled": {"description": "", "type": "color", "value": "{primitives.petrol.600}"}, "active": {"description": "", "type": "color", "value": "{primitives.petrol.500}"}}, "fill": {"default": {"description": "", "type": "color", "value": "{primitives.petrol.800}"}, "hover": {"description": "", "type": "color", "value": "{primitives.petrol.700}"}, "disabled": {"description": "", "type": "color", "value": "{primitives.petrol.900}"}, "active": {"description": "", "type": "color", "value": "{primitives.petrol.800}"}, "active-selected": {"description": "", "type": "color", "value": "{primitives.teal.600}"}}}, "chart": {"fill": {"subtle-background": {"description": "", "type": "color", "value": "{primitives.teal.800}"}, "brand-strong": {"description": "", "type": "color", "value": "{primitives.teal.700}"}, "brand-stronger": {"description": "", "type": "color", "value": "{primitives.teal.600}"}, "gold": {"description": "", "type": "color", "value": "{primitives.gold.500}"}, "muted-background": {"description": "", "type": "color", "value": "{primitives.teal.800}"}}, "stroke": {"strong": {"description": "", "type": "color", "value": "{primitives.petrol.500}"}, "subtle": {"description": "", "type": "color", "value": "{primitives.teal.700}"}, "gold": {"description": "", "type": "color", "value": "{primitives.gold.500}"}}, "ink": {"on": {"brand": {"description": "", "type": "color", "value": "{primitives.gray.white}"}}, "default": {"description": "", "type": "color", "value": "{primitives.gray.white}"}, "green-subtle": {"description": "", "type": "color", "value": "{primitives.teal.700}"}}}, "calendar": {"fill": {"date": {"hover": {"description": "", "type": "color", "value": "{primitives.petrol.700}"}, "active": {"description": "", "type": "color", "value": "{primitives.gray.white}"}, "period": {"description": "", "type": "color", "value": "{primitives.petrol.800}"}}}, "ink": {"default": {"description": "", "type": "color", "value": "{primitives.gray.white}"}, "disbaled": {"description": "", "type": "color", "value": "{primitives.gray.500}"}, "contrast": {"description": "", "type": "color", "value": "{primitives.gray.900}"}}}, "toggle": {"background-off": {"description": "", "type": "color", "value": "{primitives.petrol.700}"}, "background-on": {"description": "", "type": "color", "value": "{primitives.teal.600}"}, "handle-on": {"description": "", "type": "color", "value": "{primitives.gray.white}"}, "handle-off": {"description": "", "type": "color", "value": "{primitives.petrol.400}"}, "handle-on-success": {"description": "", "type": "color", "value": "{primitives.green.200}"}, "icon-on-handle": {"description": "", "type": "color", "value": "{primitives.teal.900}"}}}}, "typography": {"desktop": {"font-family": {"family": {"display": {"type": "string", "value": "Basel Classic"}, "headline": {"type": "string", "value": "Basel Grotesk"}, "body": {"type": "string", "value": "Basel Grotesk"}}, "weight": {"regular": {"type": "string", "value": "regular - 435"}, "medium": {"type": "string", "value": "medium - 535"}}}, "body": {"xs": {"font-size": {"type": "dimension", "value": 12}, "line-height": {"type": "dimension", "value": 18}}, "sm": {"font-size": {"type": "dimension", "value": 14}, "line-height": {"type": "dimension", "value": 22}}, "md": {"font-size": {"type": "dimension", "value": 16}, "line-height": {"type": "dimension", "value": 24}}, "lg": {"font-size": {"type": "dimension", "value": 18}, "line-height": {"type": "dimension", "value": 30}}, "xl": {"font-size": {"type": "dimension", "value": 22}, "line-height": {"type": "dimension", "value": 34}}}, "title": {"xs": {"font-size": {"type": "dimension", "value": 16}, "line-height": {"type": "dimension", "value": 20}}, "sm": {"font-size": {"type": "dimension", "value": 18}, "line-height": {"type": "dimension", "value": 24}}, "md": {"font-size": {"type": "dimension", "value": 24}, "line-height": {"type": "dimension", "value": 28}}, "lg": {"font-size": {"type": "dimension", "value": 32}, "line-height": {"type": "dimension", "value": 38}}}, "display": {"xs": {"font-size": {"type": "dimension", "value": 20}, "line-height": {"type": "dimension", "value": 24}}, "sm": {"font-size": {"type": "dimension", "value": 24}, "line-height": {"type": "dimension", "value": 28}}, "md": {"font-size": {"type": "dimension", "value": 32}, "line-height": {"type": "dimension", "value": 40}}, "xl": {"font-size": {"type": "dimension", "value": 48}, "line-height": {"type": "dimension", "value": 58}}, "lg": {"font-size": {"type": "dimension", "value": 40}, "line-height": {"type": "dimension", "value": 48}}, "2xl": {"font-size": {"type": "dimension", "value": 56}, "line-height": {"type": "dimension", "value": 64}}}, "label": {"lg": {"font-size": {"type": "dimension", "value": 16}, "line-height": {"type": "dimension", "value": 24}}, "md": {"font-size": {"type": "dimension", "value": 14}, "line-height": {"type": "dimension", "value": 20}}, "sm": {"font-size": {"type": "dimension", "value": 12}, "line-height": {"type": "dimension", "value": 16}}}}, "mobile": {"font-family": {"family": {"display": {"type": "string", "value": "Basel Classic"}, "headline": {"type": "string", "value": "Basel Grotesk"}, "body": {"type": "string", "value": "Basel Grotesk"}}, "weight": {"regular": {"type": "string", "value": "regular - 435"}, "medium": {"type": "string", "value": "medium - 535"}}}, "body": {"xs": {"font-size": {"type": "dimension", "value": 12}, "line-height": {"type": "dimension", "value": 18}}, "sm": {"font-size": {"type": "dimension", "value": 14}, "line-height": {"type": "dimension", "value": 22}}, "md": {"font-size": {"type": "dimension", "value": 16}, "line-height": {"type": "dimension", "value": 24}}, "lg": {"font-size": {"type": "dimension", "value": 18}, "line-height": {"type": "dimension", "value": 30}}, "xl": {"font-size": {"type": "dimension", "value": 20}, "line-height": {"type": "dimension", "value": 30}}}, "title": {"xs": {"font-size": {"type": "dimension", "value": 16}, "line-height": {"type": "dimension", "value": 20}}, "sm": {"font-size": {"type": "dimension", "value": 18}, "line-height": {"type": "dimension", "value": 24}}, "md": {"font-size": {"type": "dimension", "value": 20}, "line-height": {"type": "dimension", "value": 24}}, "lg": {"font-size": {"type": "dimension", "value": 28}, "line-height": {"type": "dimension", "value": 30}}}, "display": {"xs": {"font-size": {"type": "dimension", "value": 16}, "line-height": {"type": "dimension", "value": 20}}, "sm": {"font-size": {"type": "dimension", "value": 20}, "line-height": {"type": "dimension", "value": 24}}, "md": {"font-size": {"type": "dimension", "value": 24}, "line-height": {"type": "dimension", "value": 32}}, "xl": {"font-size": {"type": "dimension", "value": 40}, "line-height": {"type": "dimension", "value": 48}}, "lg": {"font-size": {"type": "dimension", "value": 32}, "line-height": {"type": "dimension", "value": 40}}, "2xl": {"font-size": {"type": "dimension", "value": 48}, "line-height": {"type": "dimension", "value": 56}}}, "label": {"lg": {"font-size": {"type": "dimension", "value": 16}, "line-height": {"type": "dimension", "value": 24}}, "md": {"font-size": {"type": "dimension", "value": 14}, "line-height": {"type": "dimension", "value": 20}}, "sm": {"font-size": {"type": "dimension", "value": 12}, "line-height": {"type": "dimension", "value": 16}}}}, "display": {"2xl": {"fontSize": {"type": "dimension", "value": 56}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Basel Classic"}, "fontWeight": {"type": "number", "value": 400}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 64}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "xl": {"fontSize": {"type": "dimension", "value": 48}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Basel Classic"}, "fontWeight": {"type": "number", "value": 400}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 58}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "lg": {"fontSize": {"type": "dimension", "value": 40}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Basel Classic"}, "fontWeight": {"type": "number", "value": 400}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 48}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "md": {"fontSize": {"type": "dimension", "value": 32}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Basel Classic"}, "fontWeight": {"type": "number", "value": 400}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 40}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "sm": {"fontSize": {"type": "dimension", "value": 24}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Basel Classic"}, "fontWeight": {"type": "number", "value": 400}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0.24}, "lineHeight": {"type": "dimension", "value": 28}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "xs": {"fontSize": {"type": "dimension", "value": 20}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Basel Classic"}, "fontWeight": {"type": "number", "value": 400}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0.4}, "lineHeight": {"type": "dimension", "value": 24}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}}, "title": {"lg": {"fontSize": {"type": "dimension", "value": 32}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Basel Grotesk"}, "fontWeight": {"type": "number", "value": 500}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 38}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "md": {"fontSize": {"type": "dimension", "value": 24}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Basel Grotesk"}, "fontWeight": {"type": "number", "value": 500}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 28}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "sm": {"fontSize": {"type": "dimension", "value": 18}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Basel Grotesk"}, "fontWeight": {"type": "number", "value": 500}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 24}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "xs": {"fontSize": {"type": "dimension", "value": 16}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Basel Grotesk"}, "fontWeight": {"type": "number", "value": 500}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 20}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}}, "body": {"xl": {"regular": {"description": "Used for ingress, larger paragraphs or sentences.", "fontSize": {"type": "dimension", "value": 22}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Basel Grotesk"}, "fontWeight": {"type": "number", "value": 400}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 34}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}}, "lg": {"regular": {"fontSize": {"type": "dimension", "value": 18}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Basel Grotesk"}, "fontWeight": {"type": "number", "value": 400}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 30}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "bold": {"fontSize": {"type": "dimension", "value": 18}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Basel Grotesk"}, "fontWeight": {"type": "number", "value": 500}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 30}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}}, "md": {"regular": {"fontSize": {"type": "dimension", "value": 16}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Basel Grotesk"}, "fontWeight": {"type": "number", "value": 400}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 24}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "bold": {"fontSize": {"type": "dimension", "value": 16}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Basel Grotesk"}, "fontWeight": {"type": "number", "value": 500}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 24}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}}, "sm": {"regular": {"fontSize": {"type": "dimension", "value": 14}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Basel Grotesk"}, "fontWeight": {"type": "number", "value": 400}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 22}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "bold": {"fontSize": {"type": "dimension", "value": 14}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Basel Grotesk"}, "fontWeight": {"type": "number", "value": 500}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 22}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}}, "xs": {"regular": {"fontSize": {"type": "dimension", "value": 12}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Basel Grotesk"}, "fontWeight": {"type": "number", "value": 400}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 18}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}}}, "label": {"lg": {"fontSize": {"type": "dimension", "value": 16}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Basel Grotesk"}, "fontWeight": {"type": "number", "value": 500}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 24}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "md": {"fontSize": {"type": "dimension", "value": 14}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Basel Grotesk"}, "fontWeight": {"type": "number", "value": 500}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 20}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "sm": {"fontSize": {"type": "dimension", "value": 12}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Basel Grotesk"}, "fontWeight": {"type": "number", "value": 500}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 16}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}}, "detail": {"md": {"description": "Used for tags and labels", "fontSize": {"type": "dimension", "value": 12}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Basel Grotesk"}, "fontWeight": {"type": "number", "value": 500}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0.2}, "lineHeight": {"type": "dimension", "value": 12}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "uppercase"}}, "sm": {"description": "Used for tags and labels", "fontSize": {"type": "dimension", "value": 10}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Basel Grotesk"}, "fontWeight": {"type": "number", "value": 500}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0.2}, "lineHeight": {"type": "dimension", "value": 10}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "uppercase"}}}}}